<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.settings.SettingsActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/settings"
        app:titleTextColor="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- تنظیمات نمایش -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/display_settings"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:id="@+id/layoutDisplaySettings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/display_settings"
                            android:src="@android:drawable/ic_menu_preferences" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/display_settings"
                            android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/arrow"
                            android:rotation="180"
                            android:src="@android:drawable/ic_media_play" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- تنظیمات اعلان‌ها -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/notification_settings"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:id="@+id/layoutNotificationSettings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/notification_settings"
                            android:src="@android:drawable/ic_dialog_email" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/notification_settings"
                            android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/arrow"
                            android:rotation="180"
                            android:src="@android:drawable/ic_media_play" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- تنظیمات امنیتی -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/security_settings"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:id="@+id/layoutSecuritySettings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/security_settings"
                            android:src="@android:drawable/ic_lock_lock" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/security_settings"
                            android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/arrow"
                            android:rotation="180"
                            android:src="@android:drawable/ic_media_play" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- تهیه نسخه پشتیبان -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/backup_data"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:id="@+id/layoutBackup"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/backup_data"
                            android:src="@android:drawable/ic_menu_upload" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/backup_data"
                            android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/arrow"
                            android:rotation="180"
                            android:src="@android:drawable/ic_media_play" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- بازیابی اطلاعات -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/restore_data"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:id="@+id/layoutRestore"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/restore_data"
                            android:src="@android:drawable/ic_menu_upload" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/restore_data"
                            android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:contentDescription="@string/arrow"
                            android:rotation="180"
                            android:src="@android:drawable/ic_media_play" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 