package com.example.sharen.ui.customer

import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.example.sharen.R
import com.example.sharen.databinding.ActivityCustomerFormBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CustomerFormActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCustomerFormBinding
    private val viewModel: CustomerFormViewModel by viewModels()
    private var customerId: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCustomerFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        customerId = intent.getStringExtra(EXTRA_CUSTOMER_ID)
        
        setupToolbar()
        setupClickListeners()
        setupObservers()
        
        // Load customer data if in edit mode
        customerId?.let {
            viewModel.loadCustomer(it)
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        // Will be updated based on whether we're editing or creating
        supportActionBar?.title = getString(R.string.add_new_customer)
    }

    private fun setupClickListeners() {
        binding.btnSave.setOnClickListener {
            saveCustomer()
        }
        
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupObservers() {
        viewModel.isEditMode.observe(this) { isEditMode ->
            supportActionBar?.title = if (isEditMode) {
                getString(R.string.edit_customer)
            } else {
                getString(R.string.add_new_customer)
            }
        }
        
        viewModel.customer.observe(this) { customer ->
            customer?.let {
                binding.etCustomerName.setText(it.name)
                binding.etPhone.setText(it.phone)
                binding.etAddress.setText(it.address ?: "")
                binding.etNotes.setText(it.notes ?: "")
            }
        }
        
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSave.isEnabled = !isLoading
            binding.etCustomerName.isEnabled = !isLoading
            binding.etPhone.isEnabled = !isLoading
            binding.etAddress.isEnabled = !isLoading
            binding.etNotes.isEnabled = !isLoading
        }
        
        viewModel.error.observe(this) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }
        
        viewModel.saveSuccess.observe(this) { success ->
            if (success) {
                val message = if (viewModel.isEditMode.value == true) {
                    getString(R.string.customer_updated)
                } else {
                    getString(R.string.customer_created)
                }
                Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }
    
    private fun saveCustomer() {
        val name = binding.etCustomerName.text.toString().trim()
        val phone = binding.etPhone.text.toString().trim()
        val address = binding.etAddress.text.toString().trim().ifEmpty { null }
        val notes = binding.etNotes.text.toString().trim().ifEmpty { null }
        
        viewModel.saveCustomer(name, phone, address, notes)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    companion object {
        const val EXTRA_CUSTOMER_ID = "extra_customer_id"
    }
} 