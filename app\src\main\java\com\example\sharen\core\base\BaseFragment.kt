package com.example.sharen.core.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding

/**
 * Base Fragment برای تمام Fragment های برنامه
 */
abstract class BaseFragment<VB : ViewBinding> : Fragment() {

    private var _binding: VB? = null
    protected val binding get() = _binding!!

    abstract fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): VB
    abstract fun setupViews()
    abstract fun observeData()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = getViewBinding(inflater, container)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews()
        observeData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    /**
     * نمایش Loading
     */
    protected open fun showLoading() {
        (activity as? BaseActivity<*>)?.showLoading()
    }

    /**
     * مخفی کردن Loading
     */
    protected open fun hideLoading() {
        (activity as? BaseActivity<*>)?.hideLoading()
    }

    /**
     * نمایش پیام خطا
     */
    protected open fun showError(message: String) {
        (activity as? BaseActivity<*>)?.showError(message)
    }

    /**
     * نمایش پیام موفقیت
     */
    protected open fun showSuccess(message: String) {
        (activity as? BaseActivity<*>)?.showSuccess(message)
    }
}
