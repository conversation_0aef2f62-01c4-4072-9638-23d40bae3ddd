package com.example.sharen.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Domain Model برای کاربر
 */
@Parcelize
data class User(
    val id: String,
    val email: String,
    val name: String,
    val phone: String,
    val role: UserRole,
    val isApproved: Boolean = false,
    val imageUrl: String? = null,
    val referrerId: String? = null,
    val referrerCode: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) : Parcelable

/**
 * نقش‌های کاربری
 */
enum class UserRole(val displayName: String, val priority: Int) {
    ADMIN("مدیر سیستم", 4),
    MANAGER("مدیر", 3),
    SELLER("فروشنده", 2),
    CUSTOMER("مشتری", 1);

    companion object {
        fun fromString(role: String): UserRole {
            return values().find { it.name.equals(role, ignoreCase = true) } ?: CUSTOMER
        }
    }
}

/**
 * وضعیت کاربر
 */
enum class UserStatus {
    PENDING,    // در انتظار تأیید
    APPROVED,   // تأیید شده
    REJECTED,   // رد شده
    SUSPENDED   // تعلیق شده
}
