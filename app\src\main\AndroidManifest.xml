<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <application
        android:name=".SharenApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Sharen"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.Sharen.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".presentation.ui.auth.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.Sharen.NoActionBar" />

        <activity
            android:name=".presentation.ui.auth.RegisterActivity"
            android:exported="false"
            android:theme="@style/Theme.Sharen.NoActionBar" />

        <activity
            android:name=".presentation.ui.auth.ForgotPasswordActivity"
            android:exported="false"
            android:theme="@style/Theme.Sharen.NoActionBar" />

        <activity
            android:name=".presentation.ui.dashboard.DashboardActivity"
            android:exported="false" />

        <activity
            android:name=".presentation.ui.customer.CustomerActivity"
            android:exported="false" />

        <activity
            android:name=".presentation.ui.product.ProductActivity"
            android:exported="false" />

        <activity
            android:name=".presentation.ui.invoice.InvoiceActivity"
            android:exported="false" />

        <activity
            android:name=".presentation.ui.payment.PaymentActivity"
            android:exported="false" />

        <activity
            android:name=".presentation.ui.report.ReportActivity"
            android:exported="false" />

        <activity
            android:name=".presentation.ui.settings.SettingsActivity"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>