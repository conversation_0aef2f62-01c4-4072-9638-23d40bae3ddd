package com.example.sharen.domain.usecase.product

import com.example.sharen.domain.model.Product
import com.example.sharen.domain.repository.ProductRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use Case برای دریافت محصولات کم موجود
 */
class GetLowStockProductsUseCase @Inject constructor(
    private val productRepository: ProductRepository
) {
    operator fun invoke(): Flow<List<Product>> {
        return productRepository.getLowStockProducts()
    }
}
