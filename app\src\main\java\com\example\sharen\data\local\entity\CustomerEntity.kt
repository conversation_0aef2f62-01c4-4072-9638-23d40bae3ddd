package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Customer
import java.util.Date
import java.util.UUID

@Entity(
    tableName = "customers",
    foreignKeys = [
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index("userId")]
)
data class CustomerEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val name: String,
    val phone: String,
    val address: String? = null,
    val notes: String? = null,
    val creditLimit: Long = 0,
    val totalPurchases: Long = 0,
    val totalPayments: Long = 0,
    val totalDebt: Long = 0,
    val lastPurchaseDate: Long? = null,
    val lastPaymentDate: Long? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    fun toDomainModel(): Customer = Customer(
        id = id,
        userId = userId,
        name = name,
        phone = phone,
        address = address,
        notes = notes,
        creditLimit = creditLimit,
        totalPurchases = totalPurchases,
        totalPayments = totalPayments,
        totalDebt = totalDebt,
        lastPurchaseDate = lastPurchaseDate?.let { Date(it) },
        lastPaymentDate = lastPaymentDate?.let { Date(it) },
        createdAt = Date(this.createdAt),
        updatedAt = Date(this.updatedAt)
    )

    companion object {
        fun fromDomainModel(customer: Customer): CustomerEntity = CustomerEntity(
            id = customer.id,
            userId = customer.userId,
            name = customer.name,
            phone = customer.phone,
            address = customer.address,
            notes = customer.notes,
            creditLimit = customer.creditLimit,
            totalPurchases = customer.totalPurchases,
            totalPayments = customer.totalPayments,
            totalDebt = customer.totalDebt,
            lastPurchaseDate = customer.lastPurchaseDate?.time,
            lastPaymentDate = customer.lastPaymentDate?.time,
            createdAt = customer.createdAt.time,
            updatedAt = customer.updatedAt.time
        )
    }
}