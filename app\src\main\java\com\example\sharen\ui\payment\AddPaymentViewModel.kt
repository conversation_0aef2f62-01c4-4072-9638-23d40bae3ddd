package com.example.sharen.ui.payment

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.domain.model.Payment
import com.example.sharen.domain.model.PaymentMethod
import com.example.sharen.domain.model.PaymentStatus
import com.example.sharen.domain.repository.PaymentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Date
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class AddPaymentViewModel @Inject constructor(
    private val paymentRepository: PaymentRepository
) : ViewModel() {

    private val _state = MutableStateFlow<AddPaymentState>(AddPaymentState.Initial)
    val state: StateFlow<AddPaymentState> = _state.asStateFlow()

    fun savePayment(
        amount: Double,
        method: PaymentMethod,
        referenceNumber: String? = null,
        notes: String? = null
    ) {
        viewModelScope.launch {
            try {
                _state.value = AddPaymentState.Saving

                val payment = Payment(
                    id = UUID.randomUUID().toString(),
                    customerId = "TODO", // TODO: Get from user session
                    amount = amount,
                    date = Date(),
                    status = PaymentStatus.PENDING,
                    method = method,
                    referenceNumber = referenceNumber,
                    notes = notes
                )

                val result = paymentRepository.addPayment(payment)
                if (result.isFailure) {
                    throw result.exceptionOrNull() ?: Exception("خطا در ذخیره پرداخت")
                }
                _state.value = AddPaymentState.Saved
            } catch (e: Exception) {
                _state.value = AddPaymentState.Error(e.message ?: "خطا در ذخیره پرداخت")
            }
        }
    }
}

sealed class AddPaymentState {
    object Initial : AddPaymentState()
    object Saving : AddPaymentState()
    object Saved : AddPaymentState()
    data class Error(val message: String) : AddPaymentState()
}