package com.example.sharen.data.remote

import com.example.sharen.data.model.Report
import com.example.sharen.data.model.ReportData
import com.example.sharen.data.model.ReportType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ReportRemoteDataSource @Inject constructor(
    private val apiService: SupabaseApiService
) {
    suspend fun getReportData(startDate: Long, endDate: Long): ReportData {
        return apiService.getReportData(startDate, endDate).body() ?: ReportData()
    }

    // Financial Reports
    suspend fun generateDailyFinancialReport(date: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Daily Financial Report - ${date}",
            description = "Daily financial report for ${date}",
            startDate = date,
            endDate = date,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateFinancialReportContent(date, date)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create daily financial report")
    }

    suspend fun generateMonthlyFinancialReport(year: Int, month: Int): Result<Report> = runCatching {
        val startDate = getFirstDayOfMonth(year, month)
        val endDate = getLastDayOfMonth(year, month)
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Monthly Financial Report - $year/$month",
            description = "Monthly financial report for $year/$month",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateFinancialReportContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create monthly financial report")
    }

    suspend fun generateYearlyFinancialReport(year: Int): Result<Report> = runCatching {
        val startDate = getFirstDayOfYear(year)
        val endDate = getLastDayOfYear(year)
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Yearly Financial Report - $year",
            description = "Yearly financial report for $year",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateFinancialReportContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create yearly financial report")
    }

    suspend fun generateProfitLossReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Profit & Loss Report",
            description = "Profit and loss report from ${startDate} to ${endDate}",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateProfitLossContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create profit & loss report")
    }

    suspend fun generateDebtReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Debt Report",
            description = "Report on outstanding debts",
            startDate = Date(), // Assuming current date for debt report
            endDate = Date(), // Assuming current date for debt report
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateDebtReportContent()
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create debt report")
    }

    // Sales Reports
    suspend fun generateDailySalesReport(date: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Daily Sales Report - ${date}",
            description = "Daily sales report for ${date}",
            startDate = date,
            endDate = date,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateSalesReportContent(date, date)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create daily sales report")
    }

    suspend fun generateMonthlySalesReport(year: Int, month: Int): Result<Report> = runCatching {
        val startDate = getFirstDayOfMonth(year, month)
        val endDate = getLastDayOfMonth(year, month)
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Monthly Sales Report - $year/$month",
            description = "Monthly sales report for $year/$month",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateSalesReportContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create monthly sales report")
    }

    suspend fun generateYearlySalesReport(year: Int): Result<Report> = runCatching {
        val startDate = getFirstDayOfYear(year)
        val endDate = getLastDayOfYear(year)
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Yearly Sales Report - $year",
            description = "Yearly sales report for $year",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateSalesReportContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create yearly sales report")
    }

    suspend fun generateSalesByProductReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Sales by Product Report",
            description = "Sales by product report from ${startDate} to ${endDate}",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateSalesByProductContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create sales by product report")
    }

    suspend fun generateSalesByCustomerReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Sales by Customer Report",
            description = "Sales by customer report from ${startDate} to ${endDate}",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateSalesByCustomerContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create sales by customer report")
    }

    suspend fun generateSalesByCategoryReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Sales by Category Report",
            description = "Sales by category report from ${startDate} to ${endDate}",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateSalesByCategoryContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create sales by category report")
    }

    // Inventory Reports
    suspend fun generateCurrentInventoryReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.INVENTORY,
            title = "Current Inventory Report",
            description = "Report on current inventory levels",
            startDate = Date(), // Assuming current date for inventory report
            endDate = Date(), // Assuming current date for inventory report
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateCurrentInventoryContent()
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create current inventory report")
    }

    suspend fun generateLowStockReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.INVENTORY,
            title = "Low Stock Report",
            description = "Report on products with low stock",
            startDate = Date(), // Assuming current date for low stock report
            endDate = Date(), // Assuming current date for low stock report
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateLowStockContent()
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create low stock report")
    }

    suspend fun generateInventoryMovementReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.INVENTORY,
            title = "Inventory Movement Report",
            description = "Report on inventory movement from ${startDate} to ${endDate}",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateInventoryMovementContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create inventory movement report")
    }

    // Customer Reports
    suspend fun generateCustomerActivityReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.CUSTOMER,
            title = "Customer Activity Report",
            description = "Customer activity report from ${startDate} to ${endDate}",
            startDate = startDate,
            endDate = endDate,
            createdAt = Date(),
            createdBy = "",
            data = generateCustomerActivityContent(startDate, endDate)
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create customer activity report")
    }

    suspend fun generateCustomerLoyaltyReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.CUSTOMER,
            title = "Customer Loyalty Report",
            description = "Report on customer loyalty",
            startDate = Date(),
            endDate = Date(),
            createdAt = Date(),
            createdBy = "",
            data = generateCustomerLoyaltyContent()
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create customer loyalty report")
    }

    suspend fun generateCustomerDebtReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.CUSTOMER,
            title = "Customer Debt Report",
            description = "Report on customer debts",
            startDate = Date(),
            endDate = Date(),
            createdAt = Date(),
            createdBy = "",
            data = generateCustomerDebtContent()
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create customer debt report")
    }

    suspend fun generateInventoryValueReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.INVENTORY,
            title = "Inventory Value Report",
            description = "Report on current inventory value",
            startDate = Date(), // Assuming current date for inventory value report
            endDate = Date(), // Assuming current date for inventory value report
            createdAt = Date(),
            createdBy = "", // Will be set by the server
            data = generateInventoryValueContent()
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create inventory value report")
    }

    // General Report Management
    suspend fun saveReport(report: Report): Result<Report> = runCatching {
        apiService.createReport(report).body() ?: throw Exception("Failed to save report")
    }

    suspend fun deleteReport(reportId: String): Result<Unit> = runCatching {
        apiService.deleteReport(reportId).body() ?: throw Exception("Failed to delete report")
    }

    suspend fun getReport(reportId: String): Result<Report> = runCatching {
        apiService.getReport(reportId).body() ?: throw Exception("Report not found")
    }

    fun getAllReports(): Flow<List<Report>> = flow {
        val response = apiService.getReports()
        if (response.isSuccessful) {
            emit(response.body() ?: emptyList())
        } else {
            throw Exception("Failed to fetch reports")
        }
    }

    fun getReportsByType(type: ReportType): Flow<List<Report>> = flow {
        val response = apiService.getReports()
        if (response.isSuccessful) {
            val reports = response.body() ?: emptyList()
            emit(reports.filter { it.type == type })
        } else {
            throw Exception("Failed to fetch reports by type")
        }
    }

    fun getReportsByDateRange(startDate: Date, endDate: Date): Flow<List<Report>> = flow {
        val response = apiService.getReports()
        if (response.isSuccessful) {
            val reports = response.body() ?: emptyList()
            emit(reports.filter {
                it.createdAt in startDate..endDate
            })
        } else {
            throw Exception("Failed to fetch reports by date range")
        }
    }

    // Helper functions for report content generation
    private fun generateFinancialReportContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "totalRevenue" to 0.0,
            "totalExpenses" to 0.0,
            "netProfit" to 0.0,
            "details" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateProfitLossContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "revenue" to mapOf(
                "total" to 0.0,
                "details" to emptyList<Map<String, Any>>()
            ),
            "expenses" to mapOf(
                "total" to 0.0,
                "details" to emptyList<Map<String, Any>>()
            ),
            "netProfit" to 0.0
        )
    }

    private fun generateDebtReportContent(): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "totalDebt" to 0.0,
            "customers" to emptyList<Map<String, Any>>(),
            "installments" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateSalesReportContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "totalSales" to 0.0,
            "totalOrders" to 0,
            "averageOrderValue" to 0.0,
            "details" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateSalesByProductContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "products" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateSalesByCustomerContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "customers" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateSalesByCategoryContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "categories" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateCurrentInventoryContent(): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "totalItems" to 0,
            "totalValue" to 0.0,
            "products" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateLowStockContent(): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "threshold" to 10,
            "products" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateInventoryMovementContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "movements" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateInventoryValueContent(): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "totalValue" to 0.0,
            "categories" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateCustomerActivityContent(startDate: Date, endDate: Date): Map<String, Any> {
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "customerActivities" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateCustomerLoyaltyContent(): Map<String, Any> {
        return mapOf(
            "loyalCustomers" to emptyList<Map<String, Any>>()
        )
    }

    private fun generateCustomerDebtContent(): Map<String, Any> {
        return mapOf(
            "customerDebts" to emptyList<Map<String, Any>>()
        )
    }

    // Helper functions for date calculations
    private fun getFirstDayOfMonth(year: Int, month: Int): Date {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, month - 1, 1, 0, 0, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.time
    }

    private fun getLastDayOfMonth(year: Int, month: Int): Date {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, month, 0, 23, 59, 59)
        calendar.set(java.util.Calendar.MILLISECOND, 999)
        return calendar.time
    }

    private fun getFirstDayOfYear(year: Int): Date {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, 0, 1, 0, 0, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.time
    }

    private fun getLastDayOfYear(year: Int): Date {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, 11, 31, 23, 59, 59)
        calendar.set(java.util.Calendar.MILLISECOND, 999)
        return calendar.time
    }

    // Report Export
    suspend fun exportReportToPdf(reportId: String): Result<String> = runCatching {
        apiService.exportReportToPdf(reportId).body() ?: throw Exception("Failed to export report to PDF")
    }

    suspend fun exportReportToExcel(reportId: String): Result<String> = runCatching {
        apiService.exportReportToExcel(reportId).body() ?: throw Exception("Failed to export report to Excel")
    }

    suspend fun exportReportToCsv(reportId: String): Result<String> = runCatching {
        apiService.exportReportToCsv(reportId).body() ?: throw Exception("Failed to export report to CSV")
    }

    // Report Templates
    suspend fun saveReportTemplate(template: Report): Result<Report> = runCatching {
        apiService.saveReportTemplate(template).body() ?: throw Exception("Failed to save report template")
    }

    suspend fun getReportTemplate(templateId: String): Result<Report> = runCatching {
        apiService.getReportTemplate(templateId).body() ?: throw Exception("Report template not found")
    }

    fun getAllReportTemplates(): Flow<List<Report>> = flow {
        val response = apiService.getAllReportTemplates()
        if (response.isSuccessful) {
            emit(response.body() ?: emptyList())
        } else {
            throw Exception("Failed to fetch report templates")
        }
    }

    suspend fun deleteReportTemplate(templateId: String): Result<Unit> = runCatching {
        apiService.deleteReportTemplate(templateId).body() ?: throw Exception("Failed to delete report template")
    }

    suspend fun updateReport(report: Report): Result<Report> = runCatching {
        apiService.updateReport(report.id, report).body() ?: throw Exception("Failed to update report")
    }
}
