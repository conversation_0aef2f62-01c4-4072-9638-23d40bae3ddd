package com.example.sharen.ui.payment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import com.example.sharen.R
import com.example.sharen.domain.model.PaymentMethod
import com.example.sharen.databinding.FragmentAddPaymentBinding
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.util.Date

@AndroidEntryPoint
class AddPaymentFragment : Fragment() {

    private var _binding: FragmentAddPaymentBinding? = null
    private val binding get() = _binding!!

    private val viewModel: AddPaymentViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAddPaymentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupToolbar()
        setupPaymentMethodSpinner()
        setupSaveButton()
        observeState()
    }

    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }
    }

    private fun setupPaymentMethodSpinner() {
        val methods = PaymentMethod.values()
        val adapter = PaymentMethodAdapter(requireContext(), methods)
        binding.paymentMethodSpinner.adapter = adapter
    }

    private fun setupSaveButton() {
        binding.saveButton.setOnClickListener {
            val amount = binding.amountEditText.text.toString().toDoubleOrNull()
            val method = binding.paymentMethodSpinner.selectedItem as PaymentMethod
            val referenceNumber = binding.referenceNumberEditText.text.toString()
            val notes = binding.notesEditText.text.toString()

            if (amount == null) {
                binding.amountEditText.error = getString(R.string.required_field)
                return@setOnClickListener
            }

            viewModel.savePayment(
                amount = amount,
                method = method,
                referenceNumber = referenceNumber.takeIf { it.isNotBlank() },
                notes = notes.takeIf { it.isNotBlank() }
            )
        }
    }

    private fun observeState() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.state.collect { state ->
                    when (state) {
                        is AddPaymentState.Saving -> {
                            binding.saveButton.isEnabled = false
                            binding.progressBar.visibility = View.VISIBLE
                        }
                        is AddPaymentState.Saved -> {
                            findNavController().navigateUp()
                        }
                        is AddPaymentState.Error -> {
                            binding.saveButton.isEnabled = true
                            binding.progressBar.visibility = View.GONE
                            Snackbar.make(
                                binding.root,
                                state.message,
                                Snackbar.LENGTH_LONG
                            ).show()
                        }
                        else -> {
                            binding.saveButton.isEnabled = true
                            binding.progressBar.visibility = View.GONE
                        }
                    }
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}