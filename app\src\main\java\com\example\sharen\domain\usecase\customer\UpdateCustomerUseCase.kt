package com.example.sharen.domain.usecase.customer

import com.example.sharen.domain.model.Customer
import com.example.sharen.domain.repository.CustomerRepository
import java.util.Date
import javax.inject.Inject

/**
 * Use Case برای بروزرسانی اطلاعات مشتری
 */
class UpdateCustomerUseCase @Inject constructor(
    private val customerRepository: CustomerRepository
) {
    suspend operator fun invoke(customer: Customer): Result<Customer> {
        
        // اعتبارسنجی ورودی‌ها
        if (customer.name.isBlank()) {
            return Result.failure(IllegalArgumentException("نام مشتری نمی‌تواند خالی باشد"))
        }
        
        if (customer.phone.isBlank()) {
            return Result.failure(IllegalArgumentException("شماره تلفن نمی‌تواند خالی باشد"))
        }
        
        if (!customer.phone.matches(Regex("^09\\d{9}$"))) {
            return Result.failure(IllegalArgumentException("شماره تلفن معتبر نیست"))
        }
        
        if (customer.creditLimit < 0) {
            return Result.failure(IllegalArgumentException("حد اعتبار نمی‌تواند منفی باشد"))
        }
        
        // بروزرسانی تاریخ آخرین تغییر
        val updatedCustomer = customer.copy(
            name = customer.name.trim(),
            phone = customer.phone.trim(),
            address = customer.address?.trim(),
            notes = customer.notes?.trim(),
            updatedAt = Date()
        )
        
        return customerRepository.updateCustomer(updatedCustomer)
    }
}
