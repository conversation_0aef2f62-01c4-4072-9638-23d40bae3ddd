package com.example.sharen.di

import com.example.sharen.data.repository.ReportRepository
import com.example.sharen.data.repository.ReportRepositoryImpl
import com.example.sharen.data.remote.ReportRemoteDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ReportModule {

    @Provides
    @Singleton
    fun provideReportRepository(remoteDataSource: ReportRemoteDataSource): ReportRepository {
        return ReportRepositoryImpl(remoteDataSource)
    }
} 