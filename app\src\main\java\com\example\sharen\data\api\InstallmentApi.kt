package com.example.sharen.data.api

import com.example.sharen.data.model.Installment
import retrofit2.http.*

interface InstallmentApi {
    @GET("installments")
    suspend fun getInstallments(): List<Installment>

    @GET("installments/{id}")
    suspend fun getInstallment(@Path("id") id: Long): Installment

    @POST("installments")
    suspend fun createInstallment(@Body installment: Installment): Installment

    @PUT("installments/{id}")
    suspend fun updateInstallment(
        @Path("id") id: Long,
        @Body installment: Installment
    ): Installment

    @DELETE("installments/{id}")
    suspend fun deleteInstallment(@Path("id") id: Long)

    @POST("installments/{id}/pay")
    suspend fun payInstallment(
        @Path("id") id: Long,
        @Query("amount") amount: Double
    ): Installment

    @POST("installments/{id}/reminder")
    suspend fun sendReminder(@Path("id") id: Long)

    @GET("installments/statistics")
    suspend fun getInstallmentStatistics(
        @Query("startDate") startDate: String,
        @Query("endDate") endDate: String
    ): Map<String, Double>

    @GET("installments/{id}/remaining")
    suspend fun calculateRemainingAmount(@Path("id") id: Long): Double

    @POST("installments/schedule")
    suspend fun generateInstallmentSchedule(
        @Query("totalAmount") totalAmount: Double,
        @Query("numberOfInstallments") numberOfInstallments: Int,
        @Query("startDate") startDate: String,
        @Query("intervalInDays") intervalInDays: Int
    ): List<Installment>
} 