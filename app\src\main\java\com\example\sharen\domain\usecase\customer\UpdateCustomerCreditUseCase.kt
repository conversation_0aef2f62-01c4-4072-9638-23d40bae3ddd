package com.example.sharen.domain.usecase.customer

import com.example.sharen.domain.repository.CustomerRepository
import javax.inject.Inject

/**
 * Use Case برای بروزرسانی حد اعتبار مشتری
 */
class UpdateCustomerCreditUseCase @Inject constructor(
    private val customerRepository: CustomerRepository
) {
    suspend operator fun invoke(customerId: String, newCreditLimit: Long): Result<Unit> {
        
        // اعتبارسنجی ورودی‌ها
        if (customerId.isBlank()) {
            return Result.failure(IllegalArgumentException("شناسه مشتری نمی‌تواند خالی باشد"))
        }
        
        if (newCreditLimit < 0) {
            return Result.failure(IllegalArgumentException("حد اعتبار نمی‌تواند منفی باشد"))
        }
        
        return customerRepository.updateCustomerCredit(customerId, newCreditLimit)
    }
}
