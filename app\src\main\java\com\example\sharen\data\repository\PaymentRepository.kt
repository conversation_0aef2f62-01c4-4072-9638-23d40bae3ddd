package com.example.sharen.data.repository

import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentStatus
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface PaymentRepository {
    val paymentDao: com.example.sharen.data.local.dao.PaymentDao

    suspend fun createPayment(payment: Payment): Result<Payment>
    suspend fun updatePayment(payment: Payment): Result<Payment>
    suspend fun deletePayment(paymentId: String): Result<Unit>
    suspend fun getPayment(paymentId: String): Result<Payment>
    fun getAllPayments(): Flow<List<Payment>>
    fun getPaymentsByCustomer(customerId: String): Flow<List<Payment>>
    fun getPaymentsByDateRange(startDate: Date, endDate: Date): Flow<List<Payment>>
    fun getPaymentsByStatus(status: PaymentStatus): Flow<List<Payment>>
    suspend fun confirmPayment(paymentId: String): Result<Payment>
    suspend fun rejectPayment(paymentId: String, reason: String): Result<Payment>
    suspend fun getTotalPaymentsByDateRange(startDate: Date, endDate: Date): Result<Double>
    suspend fun getCustomerTotalPayments(customerId: String): Result<Double>
    suspend fun getPaymentStatistics(startDate: Date, endDate: Date): Result<Map<String, Any>>
}
