<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <ImageView
            android:id="@+id/ivProduct"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="@string/product_image"
            android:src="@android:drawable/ic_menu_report_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvProductName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivProduct"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="پیراهن مردانه" />

        <TextView
            android:id="@+id/tvProductCode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textAppearance="@style/TextAppearance.AppCompat.Small"
            android:textColor="@android:color/darker_gray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvProductName"
            app:layout_constraintTop_toBottomOf="@+id/tvProductName"
            tools:text="کد: P1234" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="@color/divider"
            app:layout_constraintTop_toBottomOf="@+id/ivProduct" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:weightSum="4"
            app:layout_constraintTop_toBottomOf="@+id/divider">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/quantity"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvQuantity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                    tools:text="2" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/unit_price"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvUnitPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    tools:text="120,000 تومان" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/discount"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvDiscount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/red"
                    tools:text="20,000 تومان" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/total_price"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvTotalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                    android:textColor="@color/primary"
                    android:textStyle="bold"
                    tools:text="220,000 تومان" />
            </LinearLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 