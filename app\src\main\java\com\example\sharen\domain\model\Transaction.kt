package com.example.sharen.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Domain Model برای تراکنش
 */
@Parcelize
data class Transaction(
    val id: String,
    val customerId: String,
    val type: TransactionType,
    val amount: Double,
    val description: String,
    val referenceId: String? = null,
    val date: Date = Date(),
    val createdAt: Date = Date()
) : Parcelable

/**
 * نوع تراکنش
 */
enum class TransactionType(val displayName: String) {
    SALE("فروش"),
    PAYMENT("پرداخت"),
    REFUND("بازگشت وجه"),
    DISCOUNT("تخفیف"),
    ADJUSTMENT("تعدیل")
}
