package com.example.sharen.ui.invoice

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceItem
import com.example.sharen.databinding.ActivitySalesInvoiceBinding
import com.example.sharen.ui.customer.CustomerSelectionActivity
import com.example.sharen.ui.product.ProductSelectionActivity
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.util.Locale

@AndroidEntryPoint
class SalesInvoiceActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySalesInvoiceBinding
    private val viewModel: SalesInvoiceViewModel by viewModels()
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))
    private lateinit var invoiceItemsAdapter: SalesInvoiceItemAdapter

    companion object {
        const val EXTRA_INVOICE_ID = "invoice_id"
        private const val REQUEST_CUSTOMER_SELECTION = 1001
        private const val REQUEST_PRODUCT_SELECTION = 1002
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySalesInvoiceBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // دریافت شناسه فاکتور (اگر در حالت ویرایش هستیم)
        val invoiceId = intent.getStringExtra(EXTRA_INVOICE_ID)
        
        setupUI()
        setupAdapter()
        setupObservers()
        
        if (invoiceId != null) {
            // حالت ویرایش فاکتور
            supportActionBar?.title = getString(R.string.edit_invoice)
            viewModel.loadInvoice(invoiceId)
        } else {
            // حالت ایجاد فاکتور جدید
            supportActionBar?.title = getString(R.string.add_invoice)
            viewModel.initNewInvoice()
        }
    }
    
    private fun setupUI() {
        // تنظیم نوار ابزار
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        // دکمه انتخاب مشتری
        binding.btnSelectCustomer.setOnClickListener {
            selectCustomer()
        }
        
        // دکمه افزودن محصول
        binding.btnAddItem.setOnClickListener {
            selectProduct()
        }
        
        // دکمه محاسبه مجدد
        binding.btnRecalculate.setOnClickListener {
            viewModel.calculateTotals()
        }
        
        // دکمه ذخیره
        binding.btnSave.setOnClickListener {
            saveInvoice()
        }
    }
    
    private fun setupAdapter() {
        invoiceItemsAdapter = SalesInvoiceItemAdapter(
            numberFormatter = numberFormatter,
            onEditQuantity = { item, position -> editItemQuantity(item, position) },
            onRemoveItem = { position -> removeItem(position) }
        )
        
        binding.rvInvoiceItems.apply {
            layoutManager = LinearLayoutManager(this@SalesInvoiceActivity)
            adapter = invoiceItemsAdapter
        }
    }
    
    private fun setupObservers() {
        // مشاهده‌گر فاکتور
        viewModel.invoice.observe(this) { invoice ->
            updateUI(invoice)
        }
        
        // مشاهده‌گر آیتم‌های فاکتور
        viewModel.invoiceItems.observe(this) { items ->
            invoiceItemsAdapter.submitList(items)
            
            // نمایش یا عدم نمایش پیام خالی بودن
            binding.emptyView.visibility = if (items.isEmpty()) View.VISIBLE else View.GONE
            
            // فعال یا غیرفعال کردن دکمه ذخیره
            binding.btnSave.isEnabled = items.isNotEmpty() && viewModel.isCustomerSelected()
        }
        
        // مشاهده‌گر وضعیت بارگذاری
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
        
        // مشاهده‌گر پیام‌های خطا
        viewModel.errorMessage.observe(this) { message ->
            if (!message.isNullOrEmpty()) {
                Toast.makeText(this, message, Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun updateUI(invoice: Invoice) {
        // نمایش اطلاعات مشتری
        binding.tvCustomerName.text = invoice.customerName.ifEmpty { getString(R.string.select_customer) }
        
        // نمایش شماره فاکتور
        binding.tvInvoiceNumber.text = invoice.invoiceNumber
        
        // نمایش جزئیات مالی
        binding.tvSubtotal.text = "${numberFormatter.format(invoice.totalAmount)} تومان"
        binding.etDiscount.setText(invoice.discount.toString())
        binding.etTax.setText(invoice.tax.toString())
        binding.tvFinalAmount.text = "${numberFormatter.format(invoice.finalAmount)} تومان"
        
        // فعال یا غیرفعال کردن دکمه ذخیره
        binding.btnSave.isEnabled = invoice.items.isNotEmpty() && viewModel.isCustomerSelected()
    }
    
    private fun selectCustomer() {
        val intent = Intent(this, CustomerSelectionActivity::class.java)
        startActivityForResult(intent, REQUEST_CUSTOMER_SELECTION)
    }
    
    private fun selectProduct() {
        val intent = Intent(this, ProductSelectionActivity::class.java)
        startActivityForResult(intent, REQUEST_PRODUCT_SELECTION)
    }
    
    private fun editItemQuantity(item: InvoiceItem, position: Int) {
        // ایجاد یک دیالوگ برای تغییر تعداد
        val dialogView = layoutInflater.inflate(R.layout.dialog_edit_quantity, null)
        val quantityEditText = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.etQuantity)
        val discountEditText = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.etDiscount)
        
        // تنظیم مقادیر فعلی
        quantityEditText.setText(item.quantity.toString())
        discountEditText.setText(item.discount.toString())
        
        AlertDialog.Builder(this)
            .setTitle(R.string.edit_invoice)
            .setView(dialogView)
            .setPositiveButton(R.string.save) { _, _ ->
                try {
                    val newQuantity = quantityEditText.text.toString().toIntOrNull() ?: item.quantity
                    val newDiscount = discountEditText.text.toString().toLongOrNull() ?: item.discount
                    
                    if (newQuantity <= 0) {
                        Toast.makeText(this, "تعداد باید بزرگتر از صفر باشد", Toast.LENGTH_SHORT).show()
                        return@setPositiveButton
                    }
                    
                    viewModel.updateInvoiceItem(position, newQuantity, newDiscount)
                } catch (e: Exception) {
                    Toast.makeText(this, "لطفاً مقادیر معتبر وارد کنید", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }
    
    private fun removeItem(position: Int) {
        viewModel.removeInvoiceItem(position)
    }
    
    private fun saveInvoice() {
        // دریافت مقادیر از فیلدها
        val discount = binding.etDiscount.text.toString().toLongOrNull() ?: 0
        val tax = binding.etTax.text.toString().toLongOrNull() ?: 0
        
        // ذخیره فاکتور
        viewModel.saveInvoice(discount, tax).observe(this) { isSuccess ->
            if (isSuccess) {
                Toast.makeText(this, R.string.invoice_created, Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (resultCode == RESULT_OK) {
            when (requestCode) {
                REQUEST_CUSTOMER_SELECTION -> {
                    val customerId = data?.getStringExtra("customer_id") ?: return
                    val customerName = data.getStringExtra("customer_name") ?: return
                    viewModel.setCustomer(customerId, customerName)
                }
                REQUEST_PRODUCT_SELECTION -> {
                    val productId = data?.getStringExtra("product_id") ?: return
                    val productName = data.getStringExtra("product_name") ?: return
                    val productCode = data.getStringExtra("product_code")
                    val productPrice = data.getLongExtra("product_price", 0)
                    
                    viewModel.addInvoiceItem(
                        productId = productId,
                        productName = productName,
                        productCode = productCode,
                        unitPrice = productPrice
                    )
                }
            }
        }
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    override fun onBackPressed() {
        // اگر تغییرات ذخیره نشده وجود دارد، تأیید از کاربر
        if (viewModel.hasUnsavedChanges()) {
            AlertDialog.Builder(this)
                .setTitle(R.string.cancel)
                .setMessage("تغییرات ذخیره نشده وجود دارد. آیا مطمئن هستید می‌خواهید خارج شوید؟")
                .setPositiveButton(R.string.confirm) { _, _ -> super.onBackPressed() }
                .setNegativeButton(R.string.cancel, null)
                .show()
        } else {
            super.onBackPressed()
        }
    }
} 