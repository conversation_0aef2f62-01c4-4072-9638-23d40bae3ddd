package com.example.sharen.domain.repository

import com.example.sharen.domain.model.Product
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Repository Interface برای مدیریت محصولات
 */
interface ProductRepository {

    /**
     * دریافت تمام محصولات
     */
    fun getAllProducts(): Flow<List<Product>>

    /**
     * دریافت محصول با شناسه
     */
    suspend fun getProductById(productId: String): Result<Product?>

    /**
     * دریافت محصولات دسته‌بندی
     */
    fun getProductsByCategory(categoryId: String): Flow<List<Product>>

    /**
     * دریافت محصولات برند
     */
    fun getProductsByBrand(brandId: String): Flow<List<Product>>

    /**
     * جستجوی محصولات
     */
    fun searchProducts(query: String): Flow<List<Product>>

    /**
     * فیلتر محصولات بر اساس موجودی
     */
    fun getProductsByStockStatus(inStock: Boolean): Flow<List<Product>>

    /**
     * دریافت محصولات کم موجود
     */
    fun getLowStockProducts(): Flow<List<Product>>

    /**
     * دریافت محصولات ناموجود
     */
    fun getOutOfStockProducts(): Flow<List<Product>>

    /**
     * افزودن محصول جدید
     */
    suspend fun addProduct(product: Product): Result<Product>

    /**
     * بروزرسانی محصول
     */
    suspend fun updateProduct(product: Product): Result<Product>

    /**
     * حذف محصول
     */
    suspend fun deleteProduct(productId: String): Result<Unit>

    /**
     * بروزرسانی موجودی محصول
     */
    suspend fun updateProductStock(productId: String, newStock: Int): Result<Unit>

    /**
     * کاهش موجودی محصول (هنگام فروش)
     */
    suspend fun decreaseStock(productId: String, quantity: Int): Result<Unit>

    /**
     * افزایش موجودی محصول (هنگام خرید)
     */
    suspend fun increaseStock(productId: String, quantity: Int): Result<Unit>

    /**
     * دریافت محصولات بر اساس بازه قیمت
     */
    fun getProductsByPriceRange(minPrice: Long, maxPrice: Long): Flow<List<Product>>

    /**
     * دریافت محصولات فعال
     */
    fun getActiveProducts(): Flow<List<Product>>

    /**
     * دریافت محصولات غیرفعال
     */
    fun getInactiveProducts(): Flow<List<Product>>

    /**
     * دریافت محصولات بر اساس جنس
     */
    fun getProductsByMaterial(material: String): Flow<List<Product>>

    /**
     * دریافت محصولات پرفروش
     */
    fun getBestSellingProducts(limit: Int = 10): Flow<List<Product>>

    /**
     * دریافت محصولات جدید
     */
    fun getNewProducts(daysSinceAdded: Int = 30): Flow<List<Product>>
}
