package com.example.sharen.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Domain Model برای پرداخت
 */
@Parcelize
data class Payment(
    val id: String,
    val customerId: String,
    val invoiceId: String? = null,
    val amount: Double,
    val date: Date,
    val status: PaymentStatus = PaymentStatus.PENDING,
    val method: PaymentMethod,
    val referenceNumber: String? = null,
    val notes: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date(),
    val createdBy: String? = null,
    val updatedBy: String? = null
) : Parcelable

/**
 * وضعیت پرداخت
 */
enum class PaymentStatus(val displayName: String) {
    PENDING("در انتظار تأیید"),
    CONFIRMED("تأیید شده"),
    REJECTED("رد شده"),
    COMPLETED("تکمیل شده"),
    FAILED("ناموفق")
}

/**
 * روش پرداخت
 */
enum class PaymentMethod(val displayName: String) {
    CASH("نقدی"),
    CREDIT_CARD("کارت بانکی"),
    BANK_TRANSFER("انتقال بانکی"),
    CHECK("چک"),
    INSTALLMENT("اقساط"),
    ONLINE("آنلاین")
}
