package com.example.sharen.ui.invoice

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceItem
import com.example.sharen.data.model.InvoiceStatus
import com.example.sharen.data.model.Payment
import com.example.sharen.data.repository.InvoiceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InvoiceDetailsViewModel @Inject constructor(
    private val invoiceRepository: InvoiceRepository
) : ViewModel() {

    // اطلاعات فاکتور
    private val _invoice = MutableLiveData<Invoice>()
    val invoice: LiveData<Invoice> = _invoice

    // آیتم‌های فاکتور
    private val _invoiceItems = MutableLiveData<List<InvoiceItem>>()
    val invoiceItems: LiveData<List<InvoiceItem>> = _invoiceItems

    // پرداخت‌های فاکتور
    private val _payments = MutableLiveData<List<Payment>>()
    val payments: LiveData<List<Payment>> = _payments

    // نشانگر بارگذاری
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // پیام خطا
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    /**
     * بارگذاری اطلاعات فاکتور
     */
    fun loadInvoice(invoiceId: String) {
        viewModelScope.launch {
            // بارگذاری فاکتور
            invoiceRepository.getInvoiceById(invoiceId)
                .onStart { _isLoading.value = true }
                .catch { e ->
                    _errorMessage.value = e.message
                    _isLoading.value = false
                }
                .collect { invoice ->
                    _invoice.value = invoice
                    _isLoading.value = false
                    loadInvoiceItems(invoiceId)
                    loadPayments(invoiceId)
                }
        }
    }

    /**
     * بارگذاری آیتم‌های فاکتور
     */
    private fun loadInvoiceItems(invoiceId: String) {
        viewModelScope.launch {
            invoiceRepository.getInvoiceItems(invoiceId)
                .catch { e -> _errorMessage.value = e.message }
                .collect { items -> _invoiceItems.value = items }
        }
    }

    /**
     * بارگذاری پرداخت‌های فاکتور
     */
    private fun loadPayments(invoiceId: String) {
        viewModelScope.launch {
            invoiceRepository.getPaymentsByInvoiceId(invoiceId)
                .catch { e -> _errorMessage.value = e.message }
                .collect { payments -> _payments.value = payments }
        }
    }

    /**
     * به‌روزرسانی وضعیت فاکتور
     */
    fun updateInvoiceStatus(invoiceId: String, newStatus: InvoiceStatus) {
        viewModelScope.launch {
            _isLoading.value = true
            invoiceRepository.updateInvoiceStatus(invoiceId, newStatus)
                .onSuccess { loadInvoice(invoiceId) }
                .onFailure { e ->
                    _errorMessage.value = e.message
                    _isLoading.value = false
                }
        }
    }

    /**
     * حذف فاکتور
     */
    fun deleteInvoice(invoiceId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            invoiceRepository.deleteInvoice(invoiceId)
                .onSuccess { _isLoading.value = false }
                .onFailure { e ->
                    _errorMessage.value = e.message
                    _isLoading.value = false
                }
        }
    }
} 