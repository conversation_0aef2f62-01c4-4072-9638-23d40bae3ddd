package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Transaction
import com.example.sharen.domain.model.TransactionType
import java.util.Date

@Entity(
    tableName = "transactions",
    foreignKeys = [
        ForeignKey(
            entity = CustomerEntity::class,
            parentColumns = ["id"],
            childColumns = ["customerId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("customerId"),
        Index("type"),
        Index("date")
    ]
)
data class TransactionEntity(
    @PrimaryKey
    val id: String,
    val customerId: String,
    val type: String,
    val amount: Long, // Store as cents
    val description: String,
    val referenceId: String? = null,
    val date: Long,
    val createdAt: Long
) {
    fun toDomainModel(): Transaction = Transaction(
        id = id,
        customerId = customerId,
        type = TransactionType.valueOf(type),
        amount = amount / 100.0, // Convert from cents
        description = description,
        referenceId = referenceId,
        date = Date(date),
        createdAt = Date(createdAt)
    )

    companion object {
        fun fromDomainModel(transaction: Transaction): TransactionEntity = TransactionEntity(
            id = transaction.id,
            customerId = transaction.customerId,
            type = transaction.type.name,
            amount = (transaction.amount * 100).toLong(), // Convert to cents
            description = transaction.description,
            referenceId = transaction.referenceId,
            date = transaction.date.time,
            createdAt = transaction.createdAt.time
        )
    }
}