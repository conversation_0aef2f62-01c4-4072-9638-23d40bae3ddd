package com.example.sharen.ui.invoice

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.InvoiceItem
import com.example.sharen.databinding.ItemSalesInvoiceProductBinding
import java.text.NumberFormat

class SalesInvoiceItemAdapter(
    private val numberFormatter: NumberFormat,
    private val onEditQuantity: (InvoiceItem, Int) -> Unit,
    private val onRemoveItem: (Int) -> Unit
) : ListAdapter<InvoiceItem, SalesInvoiceItemAdapter.InvoiceItemViewHolder>(InvoiceItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InvoiceItemViewHolder {
        val binding = ItemSalesInvoiceProductBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return InvoiceItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: InvoiceItemViewHolder, position: Int) {
        holder.bind(getItem(position), position)
    }

    inner class InvoiceItemViewHolder(
        private val binding: ItemSalesInvoiceProductBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: InvoiceItem, position: Int) {
            // نام محصول
            binding.tvProductName.text = item.productName
            binding.tvProductCode.text = item.productCode ?: ""

            // اطلاعات قیمت و تعداد
            binding.tvQuantity.text = numberFormatter.format(item.quantity)
            binding.tvUnitPrice.text = "${numberFormatter.format(item.unitPrice)} تومان"
            binding.tvTotalPrice.text = "${numberFormatter.format(item.totalPrice)} تومان"

            // اطلاعات تخفیف (اگر وجود داشته باشد)
            if (item.discount > 0) {
                binding.tvDiscount.text = "${numberFormatter.format(item.discount)} تومان"
                binding.tvDiscount.setTextColor(binding.root.context.getColor(R.color.red))
            } else {
                binding.tvDiscount.text = "0"
                binding.tvDiscount.setTextColor(binding.root.context.getColor(R.color.text_secondary))
            }

            // تنظیم کلیک‌ها
            binding.btnEdit.setOnClickListener {
                onEditQuantity(item, position)
            }

            binding.btnRemove.setOnClickListener {
                onRemoveItem(position)
            }
        }
    }

    /**
     * برای مقایسه آیتم‌ها و بهینه‌سازی رندرینگ
     */
    class InvoiceItemDiffCallback : DiffUtil.ItemCallback<InvoiceItem>() {
        override fun areItemsTheSame(oldItem: InvoiceItem, newItem: InvoiceItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: InvoiceItem, newItem: InvoiceItem): Boolean {
            return oldItem.quantity == newItem.quantity &&
                   oldItem.unitPrice == newItem.unitPrice &&
                   oldItem.discount == newItem.discount &&
                   oldItem.tax == newItem.tax
        }
    }
} 