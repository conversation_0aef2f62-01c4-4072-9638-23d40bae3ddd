package com.example.sharen.ui.invoice

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceItem
import com.example.sharen.data.model.InvoiceStatus
import com.example.sharen.data.model.PaymentType
import com.example.sharen.data.repository.InvoiceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class SalesInvoiceViewModel @Inject constructor(
    private val invoiceRepository: InvoiceRepository
) : ViewModel() {

    // فاکتور در حال ویرایش
    private val _invoice = MutableLiveData<Invoice>()
    val invoice: LiveData<Invoice> = _invoice

    // آیتم‌های فاکتور
    private val _invoiceItems = MutableLiveData<List<InvoiceItem>>()
    val invoiceItems: LiveData<List<InvoiceItem>> = _invoiceItems

    // نشانگر بارگذاری
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // پیام خطا
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // وضعیت ذخیره‌سازی
    private val _saveStatus = MutableLiveData<Boolean>()

    // آیا فاکتور تغییر کرده است؟
    private var isDirty = false

    // آیا در حالت ویرایش هستیم؟
    private var isEditMode = false

    /**
     * مقداردهی اولیه فاکتور جدید
     */
    fun initNewInvoice() {
        val invoice = Invoice(
            invoiceNumber = "INV-NEW", // شماره موقت تا زمان ذخیره‌سازی
            customerId = "",
            customerName = "",
            status = InvoiceStatus.DRAFT,
            paymentType = PaymentType.CASH,
            createdAt = Date(),
            updatedAt = Date()
        )
        _invoice.value = invoice
        _invoiceItems.value = emptyList()
        isDirty = false
        isEditMode = false
    }

    /**
     * بارگذاری فاکتور موجود برای ویرایش
     */
    fun loadInvoice(invoiceId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            isEditMode = true

            invoiceRepository.getInvoiceById(invoiceId)
                .onStart { _isLoading.value = true }
                .catch { e ->
                    _errorMessage.value = e.message
                    _isLoading.value = false
                }
                .collect { invoice ->
                    _invoice.value = invoice
                    _invoiceItems.value = invoice.items
                    _isLoading.value = false
                    isDirty = false
                }
        }
    }

    /**
     * تنظیم مشتری فاکتور
     */
    fun setCustomer(customerId: String, customerName: String) {
        _invoice.value = _invoice.value?.copy(
            customerId = customerId,
            customerName = customerName
        )
        isDirty = true
    }

    /**
     * افزودن آیتم به فاکتور
     */
    fun addInvoiceItem(productId: String, productName: String, productCode: String?, unitPrice: Long) {
        val currentItems = _invoiceItems.value ?: emptyList()
        
        // بررسی آیا این محصول قبلاً اضافه شده است
        val existingItemIndex = currentItems.indexOfFirst { it.productId == productId }
        
        if (existingItemIndex >= 0) {
            // اگر محصول قبلاً اضافه شده، تعداد آن را افزایش دهید
            val existingItem = currentItems[existingItemIndex]
            val updatedItem = existingItem.copy(
                quantity = existingItem.quantity + 1
            )
            updateInvoiceItem(existingItemIndex, updatedItem.quantity, existingItem.discount)
        } else {
            // محصول جدید اضافه کنید
            val newItem = InvoiceItem(
                invoiceId = _invoice.value?.id ?: "",
                productId = productId,
                productName = productName,
                productCode = productCode,
                quantity = 1,
                unitPrice = unitPrice
            )
            
            val newItems = currentItems.toMutableList().apply {
                add(newItem)
            }
            
            _invoiceItems.value = newItems
            isDirty = true
            
            // به‌روزرسانی مبلغ کل فاکتور
            calculateTotals()
        }
    }

    /**
     * حذف آیتم از فاکتور
     */
    fun removeInvoiceItem(position: Int) {
        val currentItems = _invoiceItems.value ?: return
        if (position < 0 || position >= currentItems.size) return
        
        val newItems = currentItems.toMutableList().apply {
            removeAt(position)
        }
        
        _invoiceItems.value = newItems
        isDirty = true
        
        // به‌روزرسانی مبلغ کل فاکتور
        calculateTotals()
    }

    /**
     * به‌روزرسانی آیتم فاکتور
     */
    fun updateInvoiceItem(position: Int, newQuantity: Int, newDiscount: Long) {
        val currentItems = _invoiceItems.value ?: return
        if (position < 0 || position >= currentItems.size) return
        
        val item = currentItems[position]
        val updatedItem = item.copy(
            quantity = newQuantity,
            discount = newDiscount
        )
        
        val newItems = currentItems.toMutableList().apply {
            set(position, updatedItem)
        }
        
        _invoiceItems.value = newItems
        isDirty = true
        
        // به‌روزرسانی مبلغ کل فاکتور
        calculateTotals()
    }

    /**
     * محاسبه مجدد مبالغ فاکتور
     */
    fun calculateTotals() {
        val items = _invoiceItems.value ?: emptyList()
        val currentInvoice = _invoice.value ?: return
        
        // محاسبه مبلغ کل بر اساس آیتم‌ها
        val totalAmount = items.sumOf { it.totalPrice }
        
        // محاسبه مبلغ نهایی
        val finalAmount = totalAmount - currentInvoice.discount + currentInvoice.tax
        
        // به‌روزرسانی فاکتور
        _invoice.value = currentInvoice.copy(
            totalAmount = totalAmount,
            finalAmount = finalAmount,
            remainingAmount = finalAmount,
            items = items
        )
        
        isDirty = true
    }

    /**
     * ذخیره فاکتور
     */
    fun saveInvoice(discount: Long, tax: Long): LiveData<Boolean> {
        viewModelScope.launch {
            _isLoading.value = true
            
            val currentInvoice = _invoice.value ?: return@launch
            val items = _invoiceItems.value ?: emptyList()
            
            // بررسی آیا مشتری انتخاب شده است
            if (currentInvoice.customerId.isEmpty()) {
                _errorMessage.value = "لطفاً یک مشتری انتخاب کنید"
                _isLoading.value = false
                _saveStatus.value = false
                return@launch
            }
            
            // بررسی آیا فاکتور آیتم دارد
            if (items.isEmpty()) {
                _errorMessage.value = "لطفاً حداقل یک محصول به فاکتور اضافه کنید"
                _isLoading.value = false
                _saveStatus.value = false
                return@launch
            }
            
            // به‌روزرسانی مبالغ فاکتور
            val totalAmount = items.sumOf { it.totalPrice }
            val finalAmount = totalAmount - discount + tax
            
            val updatedInvoice = currentInvoice.copy(
                totalAmount = totalAmount,
                discount = discount,
                tax = tax,
                finalAmount = finalAmount,
                remainingAmount = finalAmount,
                updatedAt = Date()
            )
            
            if (isEditMode) {
                // ویرایش فاکتور موجود
                invoiceRepository.updateInvoice(updatedInvoice)
                    .onSuccess { 
                        _isLoading.value = false
                        isDirty = false
                        _saveStatus.value = true
                    }
                    .onFailure { e ->
                        _errorMessage.value = e.message
                        _isLoading.value = false
                        _saveStatus.value = false
                    }
            } else {
                // ایجاد فاکتور جدید
                invoiceRepository.createInvoice(updatedInvoice, items)
                    .onSuccess { 
                        _isLoading.value = false
                        isDirty = false
                        _saveStatus.value = true
                    }
                    .onFailure { e ->
                        _errorMessage.value = e.message
                        _isLoading.value = false
                        _saveStatus.value = false
                    }
            }
        }
        
        return _saveStatus
    }

    /**
     * آیا مشتری انتخاب شده است؟
     */
    fun isCustomerSelected(): Boolean {
        return !(_invoice.value?.customerId.isNullOrEmpty())
    }

    /**
     * آیا تغییرات ذخیره نشده وجود دارد؟
     */
    fun hasUnsavedChanges(): Boolean {
        return isDirty
    }
} 