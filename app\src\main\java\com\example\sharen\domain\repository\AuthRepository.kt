package com.example.sharen.domain.repository

import com.example.sharen.domain.model.User

/**
 * Repository Interface برای مدیریت احراز هویت
 */
interface AuthRepository {
    
    /**
     * ورود کاربر
     */
    suspend fun login(email: String, password: String): Result<User>
    
    /**
     * ثبت‌نام کاربر
     */
    suspend fun register(
        name: String,
        email: String,
        phone: String,
        password: String,
        referrerCode: String? = null
    ): Result<User>
    
    /**
     * بازنشانی رمز عبور
     */
    suspend fun resetPassword(email: String): Result<Unit>
    
    /**
     * خروج کاربر
     */
    suspend fun logout(): Result<Unit>
    
    /**
     * بررسی وضعیت ورود کاربر
     */
    suspend fun isLoggedIn(): Boolean
    
    /**
     * دریافت کاربر فعلی
     */
    suspend fun getCurrentUser(): Result<User?>
    
    /**
     * تغییر رمز عبور
     */
    suspend fun changePassword(
        currentPassword: String,
        newPassword: String
    ): Result<Unit>
    
    /**
     * تأیید ایمیل
     */
    suspend fun verifyEmail(token: String): Result<Unit>
    
    /**
     * ارسال مجدد ایمیل تأیید
     */
    suspend fun resendVerificationEmail(): Result<Unit>
    
    /**
     * بروزرسانی پروفایل کاربر
     */
    suspend fun updateProfile(
        name: String,
        phone: String
    ): Result<User>
    
    /**
     * حذف حساب کاربری
     */
    suspend fun deleteAccount(): Result<Unit>
}
