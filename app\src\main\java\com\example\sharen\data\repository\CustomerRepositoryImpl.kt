package com.example.sharen.data.repository

import com.example.sharen.data.local.dao.CustomerDao
import com.example.sharen.data.local.entity.CustomerEntity
import com.example.sharen.domain.model.Customer
import com.example.sharen.domain.repository.CustomerRepository
import com.example.sharen.domain.repository.CustomerStatistics
import com.example.sharen.domain.repository.CustomerPurchase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CustomerRepositoryImpl @Inject constructor(
    private val customerDao: CustomerDao
) : CustomerRepository {

    override fun getAllCustomers(): Flow<List<Customer>> {
        return customerDao.getAllCustomers().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun getCustomerById(customerId: String): Result<Customer?> {
        return try {
            val customerEntity = customerDao.getCustomerById(customerId)
            val customer = customerEntity?.toDomainModel()
            Result.success(customer)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getCustomersBySeller(sellerId: String): Flow<List<Customer>> {
        return customerDao.getCustomersBySeller(sellerId).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun searchCustomers(query: String): Flow<List<Customer>> {
        return customerDao.searchCustomers(query).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getCustomersByDebtStatus(hasDebt: Boolean): Flow<List<Customer>> {
        return if (hasDebt) {
            customerDao.getCustomersWithDebt().map { entities ->
                entities.map { it.toDomainModel() }
            }
        } else {
            customerDao.getCustomersWithoutDebt().map { entities ->
                entities.map { it.toDomainModel() }
            }
        }
    }

    override fun getCustomersWithLowCredit(): Flow<List<Customer>> {
        return customerDao.getCustomersWithLowCredit().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun addCustomer(customer: Customer): Result<Customer> {
        return try {
            val customerEntity = CustomerEntity.fromDomainModel(customer)
            customerDao.insertCustomer(customerEntity)
            Result.success(customer)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateCustomer(customer: Customer): Result<Customer> {
        return try {
            val customerEntity = CustomerEntity.fromDomainModel(customer)
            customerDao.updateCustomer(customerEntity)
            Result.success(customer)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteCustomer(customerId: String): Result<Unit> {
        return try {
            customerDao.deleteCustomer(customerId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateCustomerCredit(customerId: String, creditLimit: Long): Result<Unit> {
        return try {
            customerDao.updateCustomerCredit(customerId, creditLimit)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateCustomerDebt(customerId: String, debtAmount: Long): Result<Unit> {
        return try {
            customerDao.updateCustomerDebt(customerId, debtAmount)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun recordPurchase(customerId: String, amount: Long, date: Date): Result<Unit> {
        return try {
            val customer = customerDao.getCustomerById(customerId)
            if (customer != null) {
                val updatedCustomer = customer.copy(
                    totalPurchases = customer.totalPurchases + amount,
                    lastPurchaseDate = date.time,
                    updatedAt = System.currentTimeMillis()
                )
                customerDao.updateCustomer(updatedCustomer)
                Result.success(Unit)
            } else {
                Result.failure(Exception("مشتری یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun recordPayment(customerId: String, amount: Long, date: Date): Result<Unit> {
        return try {
            val customer = customerDao.getCustomerById(customerId)
            if (customer != null) {
                val updatedCustomer = customer.copy(
                    totalPayments = customer.totalPayments + amount,
                    totalDebt = maxOf(0, customer.totalDebt - amount),
                    lastPaymentDate = date.time,
                    updatedAt = System.currentTimeMillis()
                )
                customerDao.updateCustomer(updatedCustomer)
                Result.success(Unit)
            } else {
                Result.failure(Exception("مشتری یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getCustomerStatistics(customerId: String): Result<CustomerStatistics> {
        return try {
            val customer = customerDao.getCustomerById(customerId)
            if (customer != null) {
                val stats = CustomerStatistics(
                    totalPurchases = customer.totalPurchases,
                    totalPayments = customer.totalPayments,
                    totalDebt = customer.totalDebt,
                    averagePurchaseAmount = if (customer.totalPurchases > 0) customer.totalPurchases / 1 else 0,
                    purchaseCount = 1,
                    paymentCount = 1,
                    lastPurchaseDate = customer.lastPurchaseDate?.let { Date(it) },
                    lastPaymentDate = customer.lastPaymentDate?.let { Date(it) }
                )
                Result.success(stats)
            } else {
                Result.failure(Exception("مشتری یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getCustomerPurchaseHistory(customerId: String): Flow<List<CustomerPurchase>> {
        return customerDao.getCustomerPurchaseHistory(customerId).map { entities ->
            entities.map { entity ->
                CustomerPurchase(
                    invoiceId = entity.invoiceId,
                    amount = entity.amount,
                    date = Date(entity.date),
                    status = entity.status
                )
            }
        }
    }

    override fun getCustomersByDateRange(startDate: Date, endDate: Date): Flow<List<Customer>> {
        return customerDao.getCustomersByDateRange(startDate.time, endDate.time).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getActiveCustomers(): Flow<List<Customer>> {
        val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000)
        return customerDao.getActiveCustomers(thirtyDaysAgo).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getInactiveCustomers(daysSinceLastPurchase: Int): Flow<List<Customer>> {
        val cutoffDate = System.currentTimeMillis() - (daysSinceLastPurchase * 24 * 60 * 60 * 1000)
        return customerDao.getInactiveCustomers(cutoffDate).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
}