package com.example.sharen.ui.invoice

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentStatus
import com.example.sharen.databinding.ItemPaymentBinding
import java.text.NumberFormat
import java.text.SimpleDateFormat

class PaymentAdapter(
    private val numberFormatter: NumberFormat,
    private val dateFormatter: SimpleDateFormat
) : ListAdapter<Payment, PaymentAdapter.PaymentViewHolder>(PaymentDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PaymentViewHolder {
        val binding = ItemPaymentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PaymentViewHolder(binding)
    }

    override fun onBindViewHolder(holder: PaymentViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class PaymentViewHolder(
        private val binding: ItemPaymentBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(payment: Payment) {
            // تاریخ پرداخت
            binding.tvPaymentDate.text = dateFormatter.format(payment.paymentDate)
            
            // مبلغ پرداخت
            binding.tvAmount.text = "${numberFormatter.format(payment.amount)} تومان"
            
            // روش پرداخت
            binding.tvPaymentMethod.text = getPaymentMethodText(payment.method)
            
            // شماره مرجع
            binding.tvReferenceNumber.text = payment.referenceNumber ?: "-"
            
            // وضعیت پرداخت
            binding.tvStatus.text = getStatusText(payment.status)
            binding.tvStatus.setTextColor(
                ContextCompat.getColor(
                    binding.root.context,
                    getStatusColor(payment.status)
                )
            )
        }
        
        private fun getPaymentMethodText(method: com.example.sharen.data.model.PaymentMethod): String {
            return when (method) {
                com.example.sharen.data.model.PaymentMethod.CASH -> binding.root.context.getString(R.string.cash)
                com.example.sharen.data.model.PaymentMethod.CREDIT_CARD -> binding.root.context.getString(R.string.card)
                com.example.sharen.data.model.PaymentMethod.BANK_TRANSFER -> "انتقال بانکی"
                com.example.sharen.data.model.PaymentMethod.CHECK -> "چک"
                com.example.sharen.data.model.PaymentMethod.OTHER -> "سایر"
            }
        }
        
        private fun getStatusText(status: PaymentStatus): String {
            return when (status) {
                PaymentStatus.PENDING -> binding.root.context.getString(R.string.pending)
                PaymentStatus.COMPLETED -> binding.root.context.getString(R.string.paid)
                PaymentStatus.FAILED -> "ناموفق"
                PaymentStatus.REFUNDED -> "بازپرداخت"
            }
        }
        
        private fun getStatusColor(status: PaymentStatus): Int {
            return when (status) {
                PaymentStatus.PENDING -> R.color.orange
                PaymentStatus.COMPLETED -> R.color.green
                PaymentStatus.FAILED -> R.color.red
                PaymentStatus.REFUNDED -> R.color.blue
            }
        }
    }

    /**
     * برای مقایسه آیتم‌ها و بهینه‌سازی رندرینگ
     */
    class PaymentDiffCallback : DiffUtil.ItemCallback<Payment>() {
        override fun areItemsTheSame(oldItem: Payment, newItem: Payment): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Payment, newItem: Payment): Boolean {
            return oldItem == newItem
        }
    }
} 