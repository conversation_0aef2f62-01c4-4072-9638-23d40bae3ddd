package com.example.sharen.domain.usecase.customer

import com.example.sharen.domain.model.Customer
import com.example.sharen.domain.repository.CustomerRepository
import java.util.Date
import java.util.UUID
import javax.inject.Inject

/**
 * Use Case برای افزودن مشتری جدید
 */
class AddCustomerUseCase @Inject constructor(
    private val customerRepository: CustomerRepository
) {
    suspend operator fun invoke(
        userId: String,
        name: String,
        phone: String,
        address: String? = null,
        creditLimit: Long = 0,
        notes: String? = null
    ): Result<Customer> {
        
        // اعتبارسنجی ورودی‌ها
        if (name.isBlank()) {
            return Result.failure(IllegalArgumentException("نام مشتری نمی‌تواند خالی باشد"))
        }
        
        if (phone.isBlank()) {
            return Result.failure(IllegalArgumentException("شماره تلفن نمی‌تواند خالی باشد"))
        }
        
        if (!phone.matches(Regex("^09\\d{9}$"))) {
            return Result.failure(IllegalArgumentException("شماره تلفن معتبر نیست"))
        }
        
        if (creditLimit < 0) {
            return Result.failure(IllegalArgumentException("حد اعتبار نمی‌تواند منفی باشد"))
        }
        
        // ایجاد مشتری جدید
        val customer = Customer(
            id = UUID.randomUUID().toString(),
            userId = userId,
            name = name.trim(),
            phone = phone.trim(),
            address = address?.trim(),
            creditLimit = creditLimit,
            totalPurchases = 0,
            totalPayments = 0,
            totalDebt = 0,
            lastPurchaseDate = null,
            lastPaymentDate = null,
            notes = notes?.trim(),
            createdAt = Date(),
            updatedAt = Date()
        )
        
        return customerRepository.addCustomer(customer)
    }
}
