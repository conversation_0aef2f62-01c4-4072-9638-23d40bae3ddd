package com.example.sharen.data.repository.impl

import com.example.sharen.data.local.dao.UserDao
import com.example.sharen.data.local.entity.UserEntity
import com.example.sharen.data.model.Result
import com.example.sharen.data.model.User
import com.example.sharen.data.remote.AuthRemoteDataSource
import com.example.sharen.data.repository.AuthRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class AuthRepositoryImpl @Inject constructor(
    private val userDao: UserDao,
    private val authRemoteDataSource: AuthRemoteDataSource
) : AuthRepository {

    override suspend fun login(email: String, password: String): Result<User> {
        return authRemoteDataSource.login(email, password).also { result ->
            if (result is Result.Success) {
                userDao.clearCurrentUser()
                userDao.insertUser(UserEntity.fromUser(result.data))
                userDao.setCurrentUser(result.data.id.toString())
            }
        }
    }

    override suspend fun register(name: String, email: String, password: String, phone: String): Result<User> {
        return authRemoteDataSource.register(name, email, password, phone).also { result ->
            if (result is Result.Success) {
                userDao.clearCurrentUser()
                userDao.insertUser(UserEntity.fromUser(result.data))
                userDao.setCurrentUser(result.data.id.toString())
            }
        }
    }

    override suspend fun logout() {
        userDao.clearCurrentUser()
    }

    override fun getCurrentUser(): Flow<User?> {
        return userDao.getCurrentUser().map { it?.toDomainModel() }
    }

    override suspend fun updateUser(user: User): Result<User> {
        return authRemoteDataSource.updateUser(user).also { result ->
            if (result is Result.Success) {
                userDao.updateUser(UserEntity.fromDomainModel(result.data))
            }
        }
    }

    override suspend fun changePassword(oldPassword: String, newPassword: String): Result<Unit> {
        return authRemoteDataSource.changePassword(oldPassword, newPassword)
    }
}