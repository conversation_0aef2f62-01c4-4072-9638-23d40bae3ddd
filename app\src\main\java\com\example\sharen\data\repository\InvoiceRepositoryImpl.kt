package com.example.sharen.data.repository

import com.example.sharen.data.model.Invoice as DataInvoice
import com.example.sharen.data.model.InvoiceItem as DataInvoiceItem
import com.example.sharen.data.model.InvoiceStatus as DataInvoiceStatus
import com.example.sharen.data.model.Payment as DataPayment
import com.example.sharen.data.model.PaymentMethod as DataPaymentMethod
import com.example.sharen.data.model.PaymentStatus as DataPaymentStatus
import com.example.sharen.data.model.PaymentType as DataPaymentType

import com.example.sharen.domain.model.Invoice as DomainInvoice
import com.example.sharen.domain.model.InvoiceItem as DomainInvoiceItem
import com.example.sharen.domain.model.InvoiceStatus as DomainInvoiceStatus
import com.example.sharen.domain.model.PaymentType as DomainPaymentType

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import java.util.Calendar
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * پیاده‌سازی رپوزیتوری فاکتورها با داده‌های آزمایشی
 */
@Singleton
class InvoiceRepositoryImpl @Inject constructor() : InvoiceRepository {

    // لیست فاکتورهای آزمایشی
    private val mockInvoices = mutableListOf<DataInvoice>()

    // لیست آیتم‌های فاکتور آزمایشی
    private val mockInvoiceItems = mutableMapOf<String, MutableList<DataInvoiceItem>>()

    // لیست پرداخت‌های آزمایشی
    private val mockPayments = mutableListOf<DataPayment>()

    // شماره فاکتور برای فاکتورهای جدید
    private var lastInvoiceNumber = 1000

    init {
        // ایجاد داده‌های آزمایشی
        createMockData()
    }

    /**
     * ایجاد داده‌های آزمایشی
     */
    private fun createMockData() {
        val customers = listOf(
            Pair("c1", "محمد احمدی"),
            Pair("c2", "فاطمه حسینی"),
            Pair("c3", "علی نجفی"),
            Pair("c4", "مریم محمدی"),
            Pair("c5", "سعید کریمی")
        )

        val productNames = listOf(
            Pair("p1", "پیراهن مردانه"),
            Pair("p2", "شلوار جین زنانه"),
            Pair("p3", "کفش ورزشی"),
            Pair("p4", "کیف دستی زنانه"),
            Pair("p5", "تیشرت آستین کوتاه"),
            Pair("p6", "شال زنانه"),
            Pair("p7", "کت تک مردانه"),
            Pair("p8", "مانتو زنانه")
        )

        val calendar = Calendar.getInstance()

        // ایجاد ۱۰ فاکتور آزمایشی
        for (i in 1..10) {
            val invoiceId = "inv$i"
            val customerIndex = Random.nextInt(customers.size)
            val invoiceDate = getRandomDate(60) // تاریخ تصادفی در ۶۰ روز گذشته

            // تعیین تصادفی وضعیت فاکتور
            val status = when (Random.nextInt(5)) {
                0 -> DataInvoiceStatus.DRAFT
                1 -> DataInvoiceStatus.PENDING
                2 -> DataInvoiceStatus.APPROVED
                3 -> DataInvoiceStatus.PAID
                4 -> DataInvoiceStatus.PARTIALLY_PAID
                else -> DataInvoiceStatus.APPROVED
            }

            // تعیین تصادفی روش پرداخت
            val paymentType = when (Random.nextInt(4)) {
                0 -> DataPaymentType.CASH
                1 -> DataPaymentType.CARD
                2 -> DataPaymentType.INSTALLMENT
                3 -> DataPaymentType.MIXED
                else -> DataPaymentType.CASH
            }

            // ایجاد آیتم‌های فاکتور
            val items = mutableListOf<DataInvoiceItem>()
            val itemCount = Random.nextInt(1, 5) // ۱ تا ۴ آیتم در هر فاکتور

            var totalAmount = 0L

            for (j in 1..itemCount) {
                val productIndex = Random.nextInt(productNames.size)
                val quantity = Random.nextInt(1, 5) // ۱ تا ۴ عدد
                val unitPrice = Random.nextLong(50000, 500000) // قیمت بین ۵۰ هزار تا ۵۰۰ هزار تومان
                val discount = if (Random.nextBoolean()) Random.nextLong(5000, 50000) else 0

                val item = DataInvoiceItem(
                    id = "item${i}_$j",
                    invoiceId = invoiceId,
                    productId = productNames[productIndex].first,
                    productName = productNames[productIndex].second,
                    productCode = "CODE-${productNames[productIndex].first}",
                    quantity = quantity,
                    unitPrice = unitPrice,
                    discount = discount
                )

                items.add(item)
                totalAmount += item.totalPrice
            }

            mockInvoiceItems[invoiceId] = items

            // محاسبه تخفیف و مالیات فاکتور
            val discount = if (Random.nextBoolean()) Random.nextLong(10000, 50000) else 0
            val tax = if (Random.nextBoolean()) (totalAmount * 0.09).toLong() else 0
            val finalAmount = totalAmount - discount + tax

            // تعیین مبلغ پرداخت شده بر اساس وضعیت فاکتور
            val paidAmount = when (status) {
                DataInvoiceStatus.PAID -> finalAmount
                DataInvoiceStatus.PARTIALLY_PAID -> (finalAmount * Random.nextDouble(0.1, 0.9)).toLong()
                else -> 0
            }

            val remainingAmount = finalAmount - paidAmount

            // ایجاد فاکتور
            val invoice = DataInvoice(
                id = invoiceId,
                invoiceNumber = "INV-${1000 + i}",
                customerId = customers[customerIndex].first,
                customerName = customers[customerIndex].second,
                totalAmount = totalAmount,
                discount = discount,
                tax = tax,
                finalAmount = finalAmount,
                paidAmount = paidAmount,
                remainingAmount = remainingAmount,
                status = status,
                paymentType = paymentType,
                createdAt = invoiceDate,
                updatedAt = invoiceDate,
                items = items,
                isDeleted = false,
                approvedAt = null,
                approvedBy = null
            )

            mockInvoices.add(invoice)

            // اگر فاکتور پرداخت شده یا پرداخت جزئی دارد، یک پرداخت ایجاد کنیم
            if (paidAmount > 0) {
                val payment = DataPayment(
                    id = "payment${mockPayments.size + 1}",
                    customerId = invoice.customerId,
                    orderId = invoiceId,
                    amount = paidAmount.toDouble(),
                    date = getDateAfter(invoiceDate, Random.nextInt(0, 5)), // ۰ تا ۵ روز بعد از فاکتور
                    method = if (paymentType == DataPaymentType.CARD) DataPaymentMethod.CREDIT_CARD else DataPaymentMethod.CASH,
                    status = DataPaymentStatus.COMPLETED,
                    referenceNumber = if (paymentType == DataPaymentType.CARD) "REF-${10000 + i}" else null,
                    createdAt = invoiceDate,
                    updatedAt = invoiceDate,
                    notes = null
                )

                mockPayments.add(payment)
            }
        }

        // بروزرسانی شماره آخرین فاکتور
        lastInvoiceNumber = 1010
    }

    override fun getAllInvoices(): Flow<List<DomainInvoice>> = flow {
        emit(mockInvoices.filter { !it.isDeleted }.map { it.toDomainModel() })
    }

    override suspend fun getInvoiceById(invoiceId: String): Result<DomainInvoice?> {
        return try {
            val invoice = mockInvoices.find { it.id == invoiceId && !it.isDeleted }
            Result.success(invoice?.toDomainModel())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getInvoicesByCustomer(customerId: String): Flow<List<DomainInvoice>> = flow {
        val result = mockInvoices.filter { it.customerId == customerId && !it.isDeleted }.map { it.toDomainModel() }
        emit(result)
    }

    override fun getInvoicesByDateRange(startDate: Date, endDate: Date): Flow<List<DomainInvoice>> = flow {
        val result = mockInvoices.filter {
            !it.isDeleted && (it.createdAt.after(startDate) || it.createdAt == startDate) &&
                    (it.createdAt.before(endDate) || it.createdAt == endDate)
        }.map { it.toDomainModel() }
        emit(result)
    }

    override fun getInvoicesByStatus(status: DomainInvoiceStatus): Flow<List<DomainInvoice>> = flow {
        val result = mockInvoices.filter {
            !it.isDeleted && it.status.name == status.name
        }.map { it.toDomainModel() }
        emit(result)
    }

    override suspend fun addInvoice(invoice: DomainInvoice): Result<DomainInvoice> {
        return try {
            // ایجاد شماره فاکتور جدید
            lastInvoiceNumber++

            // کپی فاکتور با شماره جدید و تبدیل به DataInvoice
            val newInvoice = DataInvoice(
                id = "inv${lastInvoiceNumber}",
                invoiceNumber = "INV-$lastInvoiceNumber",
                customerId = invoice.customerId,
                customerName = invoice.customerName,
                totalAmount = invoice.totalAmount,
                discount = invoice.discountAmount,
                tax = invoice.taxAmount,
                finalAmount = invoice.finalAmount,
                paidAmount = invoice.paidAmount,
                remainingAmount = invoice.remainingAmount,
                status = DataInvoiceStatus.valueOf(invoice.status.name),
                paymentType = DataPaymentType.valueOf(invoice.paymentType.name),
                createdAt = Date(),
                updatedAt = Date(),
                items = invoice.items.map { it.toDataModel() },
                isDeleted = false,
                approvedAt = invoice.approvedAt,
                approvedBy = invoice.approvedBy
            )

            // افزودن فاکتور به لیست
            mockInvoices.add(newInvoice)

            // افزودن آیتم‌ها به لیست mockInvoiceItems
            mockInvoiceItems[newInvoice.id] = newInvoice.items.toMutableList()

            Result.success(newInvoice.toDomainModel())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateInvoice(invoice: DomainInvoice): Result<DomainInvoice> {
        return try {
            val index = mockInvoices.indexOfFirst { it.id == invoice.id }
            if (index != -1) {
                // بروزرسانی فاکتور و تبدیل به DataInvoice
                val updatedInvoice = DataInvoice(
                    id = invoice.id,
                    invoiceNumber = invoice.invoiceNumber,
                    customerId = invoice.customerId,
                    customerName = invoice.customerName,
                    totalAmount = invoice.totalAmount,
                    discount = invoice.discountAmount,
                    tax = invoice.taxAmount,
                    finalAmount = invoice.finalAmount,
                    paidAmount = invoice.paidAmount,
                    remainingAmount = invoice.remainingAmount,
                    status = DataInvoiceStatus.valueOf(invoice.status.name),
                    paymentType = DataPaymentType.valueOf(invoice.paymentType.name),
                    createdAt = invoice.createdAt,
                    updatedAt = Date(),
                    items = invoice.items.map { it.toDataModel() },
                    isDeleted = invoice.isDeleted,
                    approvedAt = invoice.approvedAt,
                    approvedBy = invoice.approvedBy
                )
                mockInvoices[index] = updatedInvoice

                // بروزرسانی آیتم‌ها در لیست mockInvoiceItems
                mockInvoiceItems[updatedInvoice.id] = updatedInvoice.items.toMutableList()

                Result.success(updatedInvoice.toDomainModel())
            } else {
                Result.failure(NoSuchElementException("فاکتور با شناسه ${invoice.id} یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteInvoice(invoiceId: String): Result<Unit> {
        return try {
            val index = mockInvoices.indexOfFirst { it.id == invoiceId }
            if (index != -1) {
                // حذف منطقی فاکتور
                val invoice = mockInvoices[index]
                mockInvoices[index] = invoice.copy(isDeleted = true, updatedAt = Date())
                Result.success(Unit)
            } else {
                Result.failure(NoSuchElementException("فاکتور با شناسه $invoiceId یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun searchInvoices(query: String): Flow<List<DomainInvoice>> = flow {
        val searchQuery = query.trim().lowercase()
        val result = mockInvoices.filter { invoice ->
            !invoice.isDeleted && (
                    invoice.invoiceNumber.lowercase().contains(searchQuery) ||
                            invoice.customerName.lowercase().contains(searchQuery) ||
                            invoice.id.lowercase().contains(searchQuery)
                    )
        }.map { it.toDomainModel() }
        emit(result)
    }

    override suspend fun getTotalSalesByDateRange(startDate: Date, endDate: Date): Result<Long> {
        return try {
            val total = mockInvoices
                .filter {
                    !it.isDeleted && (it.status == DataInvoiceStatus.PAID || it.status == DataInvoiceStatus.PARTIALLY_PAID) &&
                            (it.createdAt.after(startDate) || it.createdAt == startDate) &&
                            (it.createdAt.before(endDate) || it.createdAt == endDate)
                }
                .sumOf { it.paidAmount }
            Result.success(total)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * دریافت تاریخ تصادفی در بازه روزهای گذشته
     */
    private fun getRandomDate(daysAgo: Int): Date {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -Random.nextInt(0, daysAgo))
        return calendar.time
    }

    /**
     * دریافت تاریخ با افزودن تعداد روز به تاریخ مبدا
     */
    private fun getDateAfter(baseDate: Date, daysAfter: Int): Date {
        val calendar = Calendar.getInstance()
        calendar.time = baseDate
        calendar.add(Calendar.DAY_OF_YEAR, daysAfter)
        return calendar.time
    }

    private fun DataInvoice.toDomainModel(): DomainInvoice {
        return DomainInvoice(
            id = this.id,
            invoiceNumber = this.invoiceNumber,
            customerId = this.customerId,
            customerName = this.customerName,
            sellerId = null, // Assuming sellerId is not in data model
            sellerName = null, // Assuming sellerName is not in data model
            items = this.items.map { it.toDomainModel() },
            totalAmount = this.totalAmount,
            discountAmount = this.discount, // Mapping discount
            taxAmount = this.tax, // Mapping tax
            finalAmount = this.finalAmount,
            paidAmount = this.paidAmount,
            remainingAmount = this.remainingAmount,
            status = DomainInvoiceStatus.valueOf(this.status.name), // Mapping status
            paymentType = DomainPaymentType.valueOf(this.paymentType.name), // Mapping payment type
            dueDate = null, // Assuming dueDate is not in data model
            notes = null, // Assuming notes is not in data model
            isDeleted = this.isDeleted,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
            approvedAt = this.approvedAt,
            approvedBy = this.approvedBy
        )
    }

    private fun DataInvoiceItem.toDomainModel(): DomainInvoiceItem {
        return DomainInvoiceItem(
            id = this.id,
            invoiceId = this.invoiceId,
            productId = this.productId,
            productName = this.productName,
            productCode = this.productCode,
            quantity = this.quantity,
            unitPrice = this.unitPrice,
            discountAmount = this.discount, // Mapping discount
            taxAmount = 0, // Assuming taxAmount is not in data model. Need to confirm or calculate.
            totalPrice = this.totalPrice,
            notes = null // Assuming notes is not in data model
        )
    }

    private fun DomainInvoice.toDataModel(): DataInvoice {
        return DataInvoice(
            id = this.id,
            invoiceNumber = this.invoiceNumber,
            customerId = this.customerId,
            customerName = this.customerName,
            items = this.items.map { it.toDataModel() },
            totalAmount = this.totalAmount,
            discount = this.discountAmount,
            tax = this.taxAmount,
            finalAmount = this.finalAmount,
            paidAmount = this.paidAmount,
            remainingAmount = this.remainingAmount,
            status = DataInvoiceStatus.valueOf(this.status.name),
            paymentType = DataPaymentType.valueOf(this.paymentType.name),
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
            isDeleted = this.isDeleted,
            approvedAt = this.approvedAt,
            approvedBy = this.approvedBy
        )
    }

    private fun DomainInvoiceItem.toDataModel(): DataInvoiceItem {
        return DataInvoiceItem(
            id = this.id,
            invoiceId = this.invoiceId,
            productId = this.productId,
            productName = this.productName,
            productCode = this.productCode,
            quantity = this.quantity,
            unitPrice = this.unitPrice,
            discount = this.discountAmount,
            // totalPrice is a calculated property in DataInvoiceItem, so it's not passed here
        )
    }
}
