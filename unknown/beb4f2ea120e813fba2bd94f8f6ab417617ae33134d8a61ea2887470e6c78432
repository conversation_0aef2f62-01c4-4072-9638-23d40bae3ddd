package com.example.sharen.presentation.ui.customer

import android.content.Intent
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.core.base.BaseActivity
import com.example.sharen.core.utils.gone
import com.example.sharen.core.utils.visible
import com.example.sharen.databinding.ActivityCustomerBinding
import com.example.sharen.domain.model.Customer
import com.example.sharen.presentation.ui.customer.adapter.CustomerAdapter
import com.example.sharen.presentation.viewmodel.CustomerViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * صفحه مدیریت مشتریان
 */
@AndroidEntryPoint
class CustomerActivity : BaseActivity<ActivityCustomerBinding>() {

    private val customerViewModel: CustomerViewModel by viewModels()
    private lateinit var customerAdapter: CustomerAdapter

    override fun getViewBinding(): ActivityCustomerBinding {
        return ActivityCustomerBinding.inflate(layoutInflater)
    }

    override fun setupViews() {
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        setupSearchView()
    }

    override fun observeData() {
        observeCustomers()
        observeLoadingState()
        observeMessages()
    }

    private fun setupToolbar() {
        binding.toolbar.apply {
            title = "مشتریان"
            setNavigationOnClickListener {
                finish()
            }
        }
    }

    private fun setupRecyclerView() {
        customerAdapter = CustomerAdapter(
            onItemClick = { customer ->
                navigateToCustomerDetails(customer)
            },
            onCallClick = { customer ->
                makePhoneCall(customer.phone)
            },
            onEditClick = { customer ->
                navigateToEditCustomer(customer)
            }
        )

        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@CustomerActivity)
            adapter = customerAdapter
        }
    }

    private fun setupClickListeners() {
        binding.apply {
            fabAdd.setOnClickListener {
                navigateToAddCustomer()
            }

            swipeRefresh.setOnRefreshListener {
                customerViewModel.refreshCustomers()
            }
        }
    }

    private fun setupSearchView() {
        binding.searchView.setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { customerViewModel.searchCustomers(it) }
                return true
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                if (newText.isNullOrEmpty()) {
                    customerViewModel.loadCustomers()
                } else {
                    customerViewModel.searchCustomers(newText)
                }
                return true
            }
        })
    }

    private fun observeCustomers() {
        lifecycleScope.launch {
            customerViewModel.customers.collect { customers ->
                customerAdapter.submitList(customers)
                updateEmptyState(customers.isEmpty())
            }
        }
    }

    private fun observeLoadingState() {
        lifecycleScope.launch {
            customerViewModel.isLoading.collect { isLoading ->
                binding.swipeRefresh.isRefreshing = isLoading
                if (isLoading) {
                    binding.progressBar.visible()
                } else {
                    binding.progressBar.gone()
                }
            }
        }
    }

    private fun observeMessages() {
        lifecycleScope.launch {
            customerViewModel.error.collect { error ->
                error?.let {
                    showError(it)
                    customerViewModel.clearError()
                }
            }
        }

        lifecycleScope.launch {
            customerViewModel.success.collect { success ->
                success?.let {
                    showSuccess(it)
                    customerViewModel.clearSuccess()
                }
            }
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            binding.emptyState.visible()
            binding.recyclerView.gone()
        } else {
            binding.emptyState.gone()
            binding.recyclerView.visible()
        }
    }

    private fun navigateToCustomerDetails(customer: Customer) {
        val intent = Intent(this, CustomerDetailsActivity::class.java)
        intent.putExtra("customer_id", customer.id)
        startActivity(intent)
    }

    private fun navigateToAddCustomer() {
        val intent = Intent(this, CustomerFormActivity::class.java)
        startActivityForResult(intent, REQUEST_ADD_CUSTOMER)
    }

    private fun navigateToEditCustomer(customer: Customer) {
        val intent = Intent(this, CustomerFormActivity::class.java)
        intent.putExtra("customer", customer)
        startActivityForResult(intent, REQUEST_EDIT_CUSTOMER)
    }

    private fun makePhoneCall(phoneNumber: String) {
        try {
            val intent = Intent(Intent.ACTION_DIAL)
            intent.data = android.net.Uri.parse("tel:$phoneNumber")
            startActivity(intent)
        } catch (e: Exception) {
            showError("خطا در برقراری تماس")
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            when (requestCode) {
                REQUEST_ADD_CUSTOMER, REQUEST_EDIT_CUSTOMER -> {
                    customerViewModel.refreshCustomers()
                }
            }
        }
    }

    companion object {
        private const val REQUEST_ADD_CUSTOMER = 1001
        private const val REQUEST_EDIT_CUSTOMER = 1002
    }
}
