package com.example.sharen.core.base

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding

/**
 * Base Activity برای تمام Activity های برنامه
 * شامل قابلیت‌های مشترک مثل ViewBinding، Loading، Error Handling
 */
abstract class BaseActivity<VB : ViewBinding> : AppCompatActivity() {

    private var _binding: VB? = null
    protected val binding get() = _binding!!

    abstract fun getViewBinding(): VB
    abstract fun setupViews()
    abstract fun observeData()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = getViewBinding()
        setContentView(binding.root)

        setupViews()
        observeData()
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    /**
     * نمایش Loading
     */
    open fun showLoading() {
        // پیاده‌سازی نمایش loading
    }

    /**
     * مخفی کردن Loading
     */
    open fun hideLoading() {
        // پیاده‌سازی مخفی کردن loading
    }

    /**
     * نمایش پیام خطا
     */
    open fun showError(message: String) {
        // پیاده‌سازی نمایش خطا
    }

    /**
     * نمایش پیام موفقیت
     */
    open fun showSuccess(message: String) {
        // پیاده‌سازی نمایش موفقیت
    }
}
