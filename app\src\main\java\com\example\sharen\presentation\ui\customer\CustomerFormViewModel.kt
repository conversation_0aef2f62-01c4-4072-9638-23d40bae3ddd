package com.example.sharen.presentation.ui.customer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.domain.model.Customer
import com.example.sharen.domain.repository.AuthRepository
import com.example.sharen.domain.repository.CustomerRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Date
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class CustomerFormViewModel @Inject constructor(
    private val customerRepository: CustomerRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _state = MutableStateFlow<CustomerFormState>(CustomerFormState.Initial)
    val state: StateFlow<CustomerFormState> = _state.asStateFlow()
    
    private var currentCustomer: Customer? = null

    fun loadCustomer(customerId: String) {
        viewModelScope.launch {
            try {
                _state.value = CustomerFormState.Loading
                
                val result = customerRepository.getCustomerById(customerId)
                if (result.isSuccess) {
                    val customer = result.getOrNull()
                    if (customer != null) {
                        currentCustomer = customer
                        _state.value = CustomerFormState.CustomerLoaded(customer)
                    } else {
                        _state.value = CustomerFormState.Error("مشتری یافت نشد")
                    }
                } else {
                    _state.value = CustomerFormState.Error(
                        result.exceptionOrNull()?.message ?: "خطا در بارگذاری اطلاعات مشتری"
                    )
                }
            } catch (e: Exception) {
                _state.value = CustomerFormState.Error(
                    e.message ?: "خطا در بارگذاری اطلاعات مشتری"
                )
            }
        }
    }

    fun createCustomer(
        name: String,
        phone: String,
        email: String?,
        address: String?
    ) {
        viewModelScope.launch {
            try {
                _state.value = CustomerFormState.Loading
                
                val userId = authRepository.getCurrentUserId()
                if (userId == null) {
                    _state.value = CustomerFormState.Error("شناسه کاربر یافت نشد. لطفاً دوباره وارد شوید.")
                    return@launch
                }

                val customer = Customer(
                    id = UUID.randomUUID().toString(),
                    userId = userId,
                    name = name,
                    email = email,
                    phone = phone,
                    address = address,
                    createdAt = Date(),
                    updatedAt = Date()
                )
                
                val result = customerRepository.addCustomer(customer)
                if (result.isSuccess) {
                    _state.value = CustomerFormState.Saved
                } else {
                    _state.value = CustomerFormState.Error(
                        result.exceptionOrNull()?.message ?: "خطا در ذخیره مشتری"
                    )
                }
            } catch (e: Exception) {
                _state.value = CustomerFormState.Error(
                    e.message ?: "خطا در ذخیره مشتری"
                )
            }
        }
    }

    fun updateCustomer(
        name: String,
        phone: String,
        email: String?,
        address: String?
    ) {
        val customer = currentCustomer ?: return
        
        viewModelScope.launch {
            try {
                _state.value = CustomerFormState.Loading
                
                val updatedCustomer = customer.copy(
                    name = name,
                    phone = phone,
                    email = email,
                    address = address,
                    updatedAt = Date()
                )
                
                val result = customerRepository.updateCustomer(updatedCustomer)
                if (result.isSuccess) {
                    _state.value = CustomerFormState.Saved
                } else {
                    _state.value = CustomerFormState.Error(
                        result.exceptionOrNull()?.message ?: "خطا در بروزرسانی مشتری"
                    )
                }
            } catch (e: Exception) {
                _state.value = CustomerFormState.Error(
                    e.message ?: "خطا در بروزرسانی مشتری"
                )
            }
        }
    }
}
