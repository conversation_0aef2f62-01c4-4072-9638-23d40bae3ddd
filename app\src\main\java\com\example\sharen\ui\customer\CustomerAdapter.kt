package com.example.sharen.ui.customer

import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Customer
import com.example.sharen.databinding.ItemCustomerBinding
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.TimeUnit

class CustomerAdapter(
    private val onCustomerClick: (Customer) -> Unit,
    private val onNewInvoiceClick: (Customer) -> Unit
) : ListAdapter<Customer, CustomerAdapter.CustomerViewHolder>(CustomerDiffCallback()) {

    // Formatters
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))
    private val dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa"))

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CustomerViewHolder {
        val binding = ItemCustomerBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CustomerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CustomerViewHolder, position: Int) {
        val customer = getItem(position)
        holder.bind(customer)
    }

    inner class CustomerViewHolder(
        private val binding: ItemCustomerBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onCustomerClick(getItem(position))
                }
            }

            binding.btnCall.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val customer = getItem(position)
                    val intent = Intent(Intent.ACTION_DIAL).apply {
                        data = Uri.parse("tel:${customer.phone}")
                    }
                    binding.root.context.startActivity(intent)
                }
            }

            binding.btnMessage.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val customer = getItem(position)
                    val intent = Intent(Intent.ACTION_SENDTO).apply {
                        data = Uri.parse("smsto:${customer.phone}")
                    }
                    binding.root.context.startActivity(intent)
                }
            }

            binding.btnNewInvoice.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onNewInvoiceClick(getItem(position))
                }
            }
        }

        fun bind(customer: Customer) {
            // Basic info
            binding.tvCustomerName.text = customer.name
            binding.tvCustomerPhone.text = customer.phone

            // Handle debt amount visibility
            if (customer.hasDebt()) {
                binding.tvDebtAmount.visibility = View.VISIBLE
                binding.tvDebtAmount.text = "${numberFormatter.format(customer.debtAmount)} تومان"
            } else {
                binding.tvDebtAmount.visibility = View.GONE
            }

            // Last purchase info
            customer.lastPurchaseDate?.let { lastPurchaseDate ->
                val daysPassed = TimeUnit.MILLISECONDS.toDays(
                    System.currentTimeMillis() - lastPurchaseDate.time
                )
                
                binding.tvLastPurchase.text = when {
                    daysPassed == 0L -> binding.root.context.getString(R.string.today)
                    daysPassed == 1L -> binding.root.context.getString(R.string.yesterday)
                    daysPassed < 30L -> binding.root.context.getString(
                        R.string.days_ago, numberFormatter.format(daysPassed)
                    )
                    else -> "${binding.root.context.getString(R.string.last_purchase)}: ${dateFormatter.format(lastPurchaseDate)}"
                }
                
                binding.tvLastPurchase.visibility = View.VISIBLE
            } ?: run {
                binding.tvLastPurchase.visibility = View.GONE
            }
        }
    }

    class CustomerDiffCallback : DiffUtil.ItemCallback<Customer>() {
        override fun areItemsTheSame(oldItem: Customer, newItem: Customer): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Customer, newItem: Customer): Boolean {
            return oldItem == newItem
        }
    }
} 