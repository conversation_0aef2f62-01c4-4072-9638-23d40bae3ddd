<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">
    
    <!-- Background Circle -->
    <path
        android:fillColor="@color/primary"
        android:pathData="M60,60m-50,0a50,50 0,1 1,100 0a50,50 0,1 1,-100 0" />
    
    <!-- Inner Circle -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M60,60m-35,0a35,35 0,1 1,70 0a35,35 0,1 1,-70 0" />
    
    <!-- Letter S -->
    <path
        android:fillColor="@color/primary"
        android:pathData="M45,40 Q40,35 50,35 Q65,35 65,45 Q65,50 60,52 L50,55 Q45,57 45,62 Q45,70 55,70 Q60,70 65,65"
        android:strokeWidth="3"
        android:strokeColor="@color/primary"
        android:strokeLineCap="round" />
        
</vector>
