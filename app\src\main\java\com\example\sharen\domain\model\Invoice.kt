package com.example.sharen.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Domain Model برای فاکتور
 */
@Parcelize
data class Invoice(
    val id: String,
    val invoiceNumber: String,
    val customerId: String,
    val customerName: String,
    val sellerId: String? = null,
    val sellerName: String? = null,
    val items: List<InvoiceItem> = emptyList(),
    val totalAmount: Long,
    val discountAmount: Long = 0,
    val taxAmount: Long = 0,
    val finalAmount: Long,
    val paidAmount: Long = 0,
    val remainingAmount: Long,
    val status: InvoiceStatus = InvoiceStatus.DRAFT,
    val paymentType: PaymentType = PaymentType.CASH,
    val dueDate: Date? = null,
    val notes: String? = null,
    val isDeleted: Boolean = false,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date(),
    val approvedAt: Date? = null,
    val approvedBy: String? = null
) : Parcelable {
    
    /**
     * آیا فاکتور کاملاً پرداخت شده؟
     */
    val isFullyPaid: Boolean
        get() = paidAmount >= finalAmount
    
    /**
     * آیا فاکتور جزئی پرداخت شده؟
     */
    val isPartiallyPaid: Boolean
        get() = paidAmount > 0 && paidAmount < finalAmount
    
    /**
     * آیا فاکتور معوق است؟
     */
    val isOverdue: Boolean
        get() = dueDate?.let { it.before(Date()) } == true && !isFullyPaid
    
    /**
     * محاسبه مبلغ باقیمانده
     */
    val calculatedRemainingAmount: Long
        get() = finalAmount - paidAmount
    
    /**
     * آیا فاکتور اقساطی است؟
     */
    val isInstallment: Boolean
        get() = paymentType == PaymentType.INSTALLMENT
    
    /**
     * تعداد آیتم‌های فاکتور
     */
    val itemCount: Int
        get() = items.size
    
    /**
     * مجموع تعداد کالاها
     */
    val totalQuantity: Int
        get() = items.sumOf { it.quantity }
}

/**
 * آیتم فاکتور
 */
@Parcelize
data class InvoiceItem(
    val id: String,
    val invoiceId: String,
    val productId: String,
    val productName: String,
    val productCode: String? = null,
    val quantity: Int,
    val unitPrice: Long,
    val discountAmount: Long = 0,
    val taxAmount: Long = 0,
    val totalPrice: Long,
    val notes: String? = null
) : Parcelable {
    
    /**
     * قیمت نهایی بعد از تخفیف و مالیات
     */
    val finalPrice: Long
        get() = (unitPrice * quantity) - discountAmount + taxAmount
}

/**
 * وضعیت فاکتور
 */
enum class InvoiceStatus(val displayName: String) {
    DRAFT("پیش‌نویس"),
    PENDING("در انتظار تأیید"),
    APPROVED("تأیید شده"),
    PAID("پرداخت شده"),
    PARTIALLY_PAID("پرداخت جزئی"),
    CANCELLED("لغو شده"),
    OVERDUE("معوق")
}

/**
 * نوع پرداخت
 */
enum class PaymentType(val displayName: String) {
    CASH("نقدی"),
    CARD("کارت بانکی"),
    INSTALLMENT("اقساطی"),
    MIXED("ترکیبی"),
    CREDIT("اعتباری")
}
