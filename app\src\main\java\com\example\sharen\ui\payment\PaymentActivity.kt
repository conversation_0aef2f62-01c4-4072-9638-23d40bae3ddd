package com.example.sharen.ui.payment

import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.example.sharen.R
import com.example.sharen.data.model.PaymentMethod
import com.example.sharen.databinding.ActivityPaymentBinding
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale

@AndroidEntryPoint
class PaymentActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPaymentBinding
    private val viewModel: PaymentViewModel by viewModels()
    private lateinit var numberFormatter: NumberFormat
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPaymentBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        numberFormatter = NumberFormat.getInstance(Locale("fa", "IR"))
        
        // دریافت شناسه فاکتور از intent
        val invoiceId = intent.getStringExtra("invoice_id")
        if (invoiceId != null) {
            viewModel.setInvoiceId(invoiceId)
        }
        
        setupToolbar()
        setupUI()
        setupObservers()
        
        // بارگذاری اطلاعات فاکتور
        viewModel.loadInvoiceDetails()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.new_payment)
    }
    
    private fun setupUI() {
        // تنظیم دراپ‌داون روش پرداخت
        val paymentMethods = PaymentMethod.values().map { getPaymentMethodText(it) }
        val adapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, paymentMethods)
        binding.spinnerPaymentMethod.adapter = adapter
        
        // تنظیم دکمه ثبت پرداخت
        binding.btnSubmitPayment.setOnClickListener {
            submitPayment()
        }
    }
    
    private fun setupObservers() {
        // مشاهده اطلاعات فاکتور
        viewModel.invoiceDetails.observe(this) { invoice ->
            invoice?.let {
                binding.tvInvoiceNumber.text = it.number
                binding.tvCustomerName.text = it.customerName
                binding.tvInvoiceDate.text = SimpleDateFormat("yyyy/MM/dd", Locale("fa", "IR")).format(it.createdAt)
                binding.tvTotalAmount.text = "${numberFormatter.format(it.finalAmount)} تومان"
                binding.tvPaidAmount.text = "${numberFormatter.format(it.paidAmount)} تومان"
                binding.tvRemainingAmount.text = "${numberFormatter.format(it.remainingAmount)} تومان"
                
                // تنظیم پیش‌فرض مبلغ پرداخت به مقدار باقی‌مانده
                binding.etAmount.setText(it.remainingAmount.toString())
            }
        }
        
        // مشاهده وضعیت ثبت پرداخت
        viewModel.paymentResult.observe(this) { success ->
            if (success) {
                Toast.makeText(this, getString(R.string.payment_success), Toast.LENGTH_SHORT).show()
                finish()
            }
        }
        
        // مشاهده خطاها
        viewModel.error.observe(this) { error ->
            Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun submitPayment() {
        // اعتبارسنجی مبلغ پرداخت
        val amountText = binding.etAmount.text.toString()
        if (amountText.isEmpty()) {
            binding.etAmount.error = getString(R.string.amount_required)
            return
        }
        
        // اعتبارسنجی روش پرداخت
        val selectedPaymentMethod = binding.spinnerPaymentMethod.selectedItem as? String
        if (selectedPaymentMethod == null) {
            Toast.makeText(this, getString(R.string.payment_method_required), Toast.LENGTH_SHORT).show()
            return
        }
        
        // اعتبارسنجی مبلغ پرداخت
        val amount = amountText.toFloat()
        if (amount <= 0) {
            Toast.makeText(this, getString(R.string.amount_invalid), Toast.LENGTH_SHORT).show()
            return
        }
        
        // اعتبارسنجی مبلغ باقی‌مانده
        val remainingAmount = binding.tvRemainingAmount.text.toString().toFloat()
        if (amount > remainingAmount) {
            Toast.makeText(this, getString(R.string.amount_exceeds_remaining), Toast.LENGTH_SHORT).show()
            return
        }
        
        // اعتبارسنجی تاریخ پرداخت
        val paymentDate = binding.tvInvoiceDate.text.toString()
        if (paymentDate.isEmpty()) {
            Toast.makeText(this, getString(R.string.payment_date_required), Toast.LENGTH_SHORT).show()
            return
        }
        
        // اعتبارسنجی شماره تراکنش
        val transactionNumber = binding.etTransactionNumber.text.toString()
        if (transactionNumber.isEmpty()) {
            Toast.makeText(this, getString(R.string.transaction_number_required), Toast.LENGTH_SHORT).show()
            return
        }
        
        // اعتبارسنجی اطلاعات پرداخت
        val payment = Payment(
            amount = amount,
            paymentMethod = selectedPaymentMethod,
            paymentDate = paymentDate,
            transactionNumber = transactionNumber
        )
        
        // ثبت پرداخت
        viewModel.submitPayment(payment)
    }
} 