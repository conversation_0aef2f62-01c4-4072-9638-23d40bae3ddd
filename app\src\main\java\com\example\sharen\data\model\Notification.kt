package com.example.sharen.data.model

import java.util.Date

/**
 * مدل داده اعلان
 */
data class Notification(
    val id: String = java.util.UUID.randomUUID().toString(),
    val title: String,                          // عنوان اعلان
    val message: String,                        // متن اعلان
    val type: NotificationType,                 // نوع اعلان
    val referenceId: String? = null,            // شناسه مرجع (مثلاً شناسه فاکتور یا پرداخت)
    val isRead: Boolean = false,                // آیا خوانده شده است
    val createdAt: Date = Date(),               // تاریخ ایجاد
    val updatedAt: Date = Date()                // تاریخ به‌روزرسانی
)

/**
 * انواع اعلان
 */
enum class NotificationType {
    ALL,            // همه (فقط برای فیلتر کردن استفاده می‌شود)
    SYSTEM,         // سیستمی
    INVOICE,        // فاکتور
    PAYMENT,        // پرداخت
    INSTALLMENT,    // قسط
    USER,           // کاربر
    PRODUCT         // محصول
} 