package com.example.sharen.data.local

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.example.sharen.data.local.converter.DateConverter
import com.example.sharen.data.local.dao.*
import com.example.sharen.data.local.entity.*

@Database(
    entities = [
        UserEntity::class,
        CustomerEntity::class,
        ProductEntity::class,
        CategoryEntity::class,
        InvoiceEntity::class,
        InvoiceItemEntity::class,
        PaymentEntity::class,
        InstallmentEntity::class,
        TransactionEntity::class,
        OrderEntity::class,
        OrderItemEntity::class
    ],
    version = 2,
    exportSchema = true
)
@TypeConverters(DateConverter::class)
abstract class SharenDatabase : RoomDatabase() {

    // DAOs
    abstract fun userDao(): UserDao
    abstract fun customerDao(): CustomerDao
    abstract fun productDao(): ProductDao
    abstract fun categoryDao(): CategoryDao
    abstract fun invoiceDao(): InvoiceDao
    abstract fun invoiceItemDao(): InvoiceItemDao
    abstract fun paymentDao(): PaymentDao
    abstract fun installmentDao(): InstallmentDao

    companion object {
        const val DATABASE_NAME = "sharen_database"

        @Volatile
        private var INSTANCE: SharenDatabase? = null

        fun getDatabase(context: Context): SharenDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    SharenDatabase::class.java,
                    DATABASE_NAME
                )
                .addMigrations(MIGRATION_1_2)
                .fallbackToDestructiveMigration() // فقط برای development
                .build()
                INSTANCE = instance
                instance
            }
        }

        /**
         * Migration از version 1 به 2
         */
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // اضافه کردن جدول payments
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS payments (
                        id TEXT PRIMARY KEY NOT NULL,
                        customerId TEXT NOT NULL,
                        invoiceId TEXT,
                        amount INTEGER NOT NULL,
                        method TEXT NOT NULL,
                        status TEXT NOT NULL,
                        description TEXT,
                        referenceNumber TEXT,
                        createdAt INTEGER NOT NULL,
                        updatedAt INTEGER NOT NULL,
                        FOREIGN KEY(customerId) REFERENCES customers(id) ON DELETE CASCADE,
                        FOREIGN KEY(invoiceId) REFERENCES invoices(id) ON DELETE SET NULL
                    )
                """.trimIndent())

                // اضافه کردن جدول installments
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS installments (
                        id TEXT PRIMARY KEY NOT NULL,
                        invoiceId TEXT NOT NULL,
                        customerId TEXT NOT NULL,
                        amount INTEGER NOT NULL,
                        dueDate INTEGER NOT NULL,
                        paidDate INTEGER,
                        status TEXT NOT NULL,
                        notes TEXT,
                        createdAt INTEGER NOT NULL,
                        updatedAt INTEGER NOT NULL,
                        FOREIGN KEY(invoiceId) REFERENCES invoices(id) ON DELETE CASCADE,
                        FOREIGN KEY(customerId) REFERENCES customers(id) ON DELETE CASCADE
                    )
                """.trimIndent())

                // اضافه کردن جدول transactions
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS transactions (
                        id TEXT PRIMARY KEY NOT NULL,
                        type TEXT NOT NULL,
                        amount INTEGER NOT NULL,
                        description TEXT,
                        customerId TEXT,
                        invoiceId TEXT,
                        paymentId TEXT,
                        createdAt INTEGER NOT NULL,
                        FOREIGN KEY(customerId) REFERENCES customers(id) ON DELETE SET NULL,
                        FOREIGN KEY(invoiceId) REFERENCES invoices(id) ON DELETE SET NULL,
                        FOREIGN KEY(paymentId) REFERENCES payments(id) ON DELETE SET NULL
                    )
                """.trimIndent())

                // اضافه کردن جدول orders
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS orders (
                        id TEXT PRIMARY KEY NOT NULL,
                        orderNumber TEXT NOT NULL,
                        supplierId TEXT,
                        status TEXT NOT NULL,
                        totalAmount INTEGER NOT NULL,
                        notes TEXT,
                        orderDate INTEGER NOT NULL,
                        expectedDeliveryDate INTEGER,
                        actualDeliveryDate INTEGER,
                        createdAt INTEGER NOT NULL,
                        updatedAt INTEGER NOT NULL
                    )
                """.trimIndent())

                // اضافه کردن جدول order_items
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS order_items (
                        id TEXT PRIMARY KEY NOT NULL,
                        orderId TEXT NOT NULL,
                        productId TEXT NOT NULL,
                        quantity INTEGER NOT NULL,
                        unitPrice INTEGER NOT NULL,
                        totalPrice INTEGER NOT NULL,
                        FOREIGN KEY(orderId) REFERENCES orders(id) ON DELETE CASCADE,
                        FOREIGN KEY(productId) REFERENCES products(id) ON DELETE CASCADE
                    )
                """.trimIndent())

                // بروزرسانی جدول customers برای اضافه کردن فیلدهای جدید
                database.execSQL("""
                    ALTER TABLE customers ADD COLUMN creditLimit INTEGER NOT NULL DEFAULT 0
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE customers ADD COLUMN totalPurchases INTEGER NOT NULL DEFAULT 0
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE customers ADD COLUMN totalPayments INTEGER NOT NULL DEFAULT 0
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE customers ADD COLUMN totalDebt INTEGER NOT NULL DEFAULT 0
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE customers ADD COLUMN lastPurchaseDate INTEGER
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE customers ADD COLUMN lastPaymentDate INTEGER
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE customers ADD COLUMN notes TEXT
                """.trimIndent())

                // بروزرسانی جدول products برای اضافه کردن فیلدهای جدید
                database.execSQL("""
                    ALTER TABLE products ADD COLUMN brandId TEXT
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN type TEXT NOT NULL DEFAULT 'CLOTHING'
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN season TEXT NOT NULL DEFAULT 'ALL_SEASON'
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN gender TEXT NOT NULL DEFAULT 'UNISEX'
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN sizes TEXT NOT NULL DEFAULT '[]'
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN colors TEXT NOT NULL DEFAULT '[]'
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN materials TEXT NOT NULL DEFAULT '[]'
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN imageUrls TEXT NOT NULL DEFAULT '[]'
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN tags TEXT NOT NULL DEFAULT '[]'
                """.trimIndent())

                database.execSQL("""
                    ALTER TABLE products ADD COLUMN minimumStock INTEGER NOT NULL DEFAULT 0
                """.trimIndent())

                // ایجاد index ها برای بهبود performance
                database.execSQL("CREATE INDEX IF NOT EXISTS index_payments_customerId ON payments(customerId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_payments_invoiceId ON payments(invoiceId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_installments_invoiceId ON installments(invoiceId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_installments_customerId ON installments(customerId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_transactions_customerId ON transactions(customerId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_order_items_orderId ON order_items(orderId)")
                database.execSQL("CREATE INDEX IF NOT EXISTS index_order_items_productId ON order_items(productId)")
            }
        }
    }
}