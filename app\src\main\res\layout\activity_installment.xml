<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.payment.InstallmentActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="@string/installments"
        app:titleTextColor="@color/white" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- اطلاعات فاکتور -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/invoice_info"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/invoice_number"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvInvoiceNumber"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            tools:text="INV-123456" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/customer_name"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvCustomerName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            tools:text="علی رضایی" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_amount"
                                android:textAppearance="@style/TextAppearance.AppCompat.Small" />

                            <TextView
                                android:id="@+id/tvTotalAmount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textColor="@color/primary"
                                android:textStyle="bold"
                                tools:text="۱,۰۰۰,۰۰۰ تومان" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/paid_amount"
                                android:textAppearance="@style/TextAppearance.AppCompat.Small" />

                            <TextView
                                android:id="@+id/tvPaidAmount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textColor="@color/green"
                                android:textStyle="bold"
                                tools:text="۵۰۰,۰۰۰ تومان" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/remaining_amount"
                                android:textAppearance="@style/TextAppearance.AppCompat.Small" />

                            <TextView
                                android:id="@+id/tvRemainingAmount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textColor="@color/red"
                                android:textStyle="bold"
                                tools:text="۵۰۰,۰۰۰ تومان" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- لیست اقساط -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/installments_list"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"
                        android:background="@color/divider" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_installment" />

                    <TextView
                        android:id="@+id/tvEmptyState"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="32dp"
                        android:text="@string/no_installments"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:visibility="gone" />

                    <ProgressBar
                        android:id="@+id/progressBar"
                        style="?android:attr/progressBarStyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddInstallment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/add_installment"
        android:src="@android:drawable/ic_input_add"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 