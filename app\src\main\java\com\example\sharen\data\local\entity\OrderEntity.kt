package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Order
import com.example.sharen.domain.model.OrderStatus
import java.util.Date

@Entity(
    tableName = "orders",
    foreignKeys = [
        ForeignKey(
            entity = CustomerEntity::class,
            parentColumns = ["id"],
            childColumns = ["customerId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["sellerId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [
        Index("customerId"),
        Index("sellerId")
    ]
)
data class OrderEntity(
    @PrimaryKey
    val id: String,
    val customerId: String,
    val sellerId: String,
    val orderNumber: String,
    val totalAmount: Long, // Store as cents
    val status: String,
    val orderDate: Long,
    val deliveryDate: Long? = null,
    val notes: String? = null,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toDomainModel(): Order = Order(
        id = id,
        customerId = customerId,
        sellerId = sellerId,
        orderNumber = orderNumber,
        totalAmount = totalAmount / 100.0, // Convert from cents
        status = OrderStatus.valueOf(status),
        orderDate = Date(orderDate),
        deliveryDate = deliveryDate?.let { Date(it) },
        notes = notes,
        createdAt = Date(this.createdAt),
        updatedAt = Date(this.updatedAt)
    )

    companion object {
        fun fromDomainModel(order: Order): OrderEntity = OrderEntity(
            id = order.id,
            customerId = order.customerId,
            sellerId = order.sellerId,
            orderNumber = order.orderNumber,
            totalAmount = (order.totalAmount * 100).toLong(), // Convert to cents
            status = order.status.name,
            orderDate = order.orderDate.time,
            deliveryDate = order.deliveryDate?.time,
            notes = order.notes,
            createdAt = order.createdAt.time,
            updatedAt = order.updatedAt.time
        )
    }
}