package com.example.sharen.data.repository

import com.example.sharen.data.model.Report
import com.example.sharen.data.model.ReportType
import com.example.sharen.data.model.ReportData
import com.example.sharen.data.remote.ReportRemoteDataSource
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ReportRepositoryImpl @Inject constructor(
    private val remoteDataSource: ReportRemoteDataSource
) : ReportRepository {
    
    override suspend fun getReportData(startDate: Long, endDate: Long): ReportData {
        return remoteDataSource.getReportData(startDate, endDate)
    }
    
    // Financial Reports
    override suspend fun generateDailyFinancialReport(date: Date): Result<Report> {
        return remoteDataSource.generateDailyFinancialReport(date)
    }
    
    override suspend fun generateMonthlyFinancialReport(year: Int, month: Int): Result<Report> {
        return remoteDataSource.generateMonthlyFinancialReport(year, month)
    }
    
    override suspend fun generateYearlyFinancialReport(year: Int): Result<Report> {
        return remoteDataSource.generateYearlyFinancialReport(year)
    }
    
    override suspend fun generateProfitLossReport(startDate: Date, endDate: Date): Result<Report> {
        return remoteDataSource.generateProfitLossReport(startDate, endDate)
    }
    
    override suspend fun generateDebtReport(): Result<Report> {
        return remoteDataSource.generateDebtReport()
    }
    
    // Sales Reports
    override suspend fun generateDailySalesReport(date: Date): Result<Report> {
        return remoteDataSource.generateDailySalesReport(date)
    }
    
    override suspend fun generateMonthlySalesReport(year: Int, month: Int): Result<Report> {
        return remoteDataSource.generateMonthlySalesReport(year, month)
    }
    
    override suspend fun generateYearlySalesReport(year: Int): Result<Report> {
        return remoteDataSource.generateYearlySalesReport(year)
    }
    
    override suspend fun generateSalesByCustomerReport(startDate: Date, endDate: Date): Result<Report> {
        return remoteDataSource.generateSalesByCustomerReport(startDate, endDate)
    }
    
    override suspend fun generateSalesByProductReport(startDate: Date, endDate: Date): Result<Report> {
        return remoteDataSource.generateSalesByProductReport(startDate, endDate)
    }

    override suspend fun generateSalesByCategoryReport(startDate: Date, endDate: Date): Result<Report> {
        return remoteDataSource.generateSalesByCategoryReport(startDate, endDate)
    }
    
    // Inventory Reports
    override suspend fun generateCurrentInventoryReport(): Result<Report> {
        return remoteDataSource.generateCurrentInventoryReport()
    }
    
    override suspend fun generateLowStockReport(): Result<Report> {
        return remoteDataSource.generateLowStockReport()
    }
    
    override suspend fun generateInventoryMovementReport(startDate: Date, endDate: Date): Result<Report> {
        return remoteDataSource.generateInventoryMovementReport(startDate, endDate)
    }

    override suspend fun generateInventoryValueReport(): Result<Report> {
        return remoteDataSource.generateInventoryValueReport()
    }
    
    // Customer Reports
    override suspend fun generateCustomerActivityReport(startDate: Date, endDate: Date): Result<Report> {
        return remoteDataSource.generateCustomerActivityReport(startDate, endDate)
    }
    
    override suspend fun generateCustomerLoyaltyReport(): Result<Report> {
        return remoteDataSource.generateCustomerLoyaltyReport()
    }
    
    override suspend fun generateCustomerDebtReport(): Result<Report> {
        return remoteDataSource.generateCustomerDebtReport()
    }
    
    // Report Management
    override suspend fun saveReport(report: Report): Result<Report> {
        return remoteDataSource.saveReport(report)
    }
    
    override suspend fun updateReport(report: Report): Result<Report> {
        return remoteDataSource.updateReport(report)
    }
    
    override suspend fun deleteReport(reportId: String): Result<Unit> {
        return remoteDataSource.deleteReport(reportId)
    }
    
    override suspend fun getReport(reportId: String): Result<Report> {
        return remoteDataSource.getReport(reportId)
    }
    
    override fun getAllReports(): Flow<List<Report>> {
        return remoteDataSource.getAllReports()
    }
    
    override fun getReportsByType(type: ReportType): Flow<List<Report>> {
        return remoteDataSource.getReportsByType(type)
    }
    
    override fun getReportsByDateRange(startDate: Date, endDate: Date): Flow<List<Report>> {
        return remoteDataSource.getReportsByDateRange(startDate, endDate)
    }
    
    // Report Export
    override suspend fun exportReportToPdf(reportId: String): Result<String> {
        return remoteDataSource.exportReportToPdf(reportId)
    }
    
    override suspend fun exportReportToExcel(reportId: String): Result<String> {
        return remoteDataSource.exportReportToExcel(reportId)
    }
    
    override suspend fun exportReportToCsv(reportId: String): Result<String> {
        return remoteDataSource.exportReportToCsv(reportId)
    }
    
    // Report Templates
    override suspend fun saveReportTemplate(template: Report): Result<Report> {
        return remoteDataSource.saveReportTemplate(template)
    }
    
    override suspend fun getReportTemplate(templateId: String): Result<Report> {
        return remoteDataSource.getReportTemplate(templateId)
    }
    
    override fun getAllReportTemplates(): Flow<List<Report>> {
        return remoteDataSource.getAllReportTemplates()
    }
    
    override suspend fun deleteReportTemplate(templateId: String): Result<Unit> {
        return remoteDataSource.deleteReportTemplate(templateId)
    }
}
