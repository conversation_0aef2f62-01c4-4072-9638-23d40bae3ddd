package com.example.sharen.data.local.dao

import androidx.room.*
import com.example.sharen.data.local.entity.InvoiceItemEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface InvoiceItemDao {
    @Query("SELECT * FROM invoice_items")
    fun getAllInvoiceItems(): Flow<List<InvoiceItemEntity>>

    @Query("SELECT * FROM invoice_items WHERE id = :id")
    suspend fun getInvoiceItemById(id: String): InvoiceItemEntity?

    @Query("SELECT * FROM invoice_items WHERE invoiceId = :invoiceId")
    fun getInvoiceItemsByInvoice(invoiceId: String): Flow<List<InvoiceItemEntity>>

    @Query("SELECT * FROM invoice_items WHERE productId = :productId")
    fun getInvoiceItemsByProduct(productId: String): Flow<List<InvoiceItemEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertInvoiceItem(invoiceItem: InvoiceItemEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertInvoiceItems(invoiceItems: List<InvoiceItemEntity>)

    @Update
    suspend fun updateInvoiceItem(invoiceItem: InvoiceItemEntity)

    @Delete
    suspend fun deleteInvoiceItem(invoiceItem: InvoiceItemEntity)

    @Query("DELETE FROM invoice_items")
    suspend fun deleteAllInvoiceItems()
} 