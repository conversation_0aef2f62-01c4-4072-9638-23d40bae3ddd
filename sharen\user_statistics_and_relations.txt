# جزئیات آماری و ارتباطات کاربران در شارن

## 1. ساختار کاربران در Supabase
### جدول users
- id: uuid (کلید اصلی)
- email: string (ایمیل کاربر)
- name: string (نام کامل)
- phone: string (شماره موبایل)
- role: string (نقش کاربر)
- is_approved: boolean (وضعیت تأیید)
- image_url: string (آدرس تصویر پروفایل)
- referrer_id: uuid (شناسه معرف)
- referrer_code: string (کد معرف)
- created_at: timestamp (تاریخ ثبت‌نام)
- updated_at: timestamp (آخرین بروزرسانی)

### جدول sellers
- id: uuid (کلید اصلی)
- user_id: uuid (ارجاع به جدول users)
- total_sales: decimal (مجموع فروش)
- total_commission: decimal (مجموع کمیسیون)
- total_debt: decimal (مجموع بدهی)
- commission_rate: decimal (نرخ کمیسیون)
- is_active: boolean (وضعیت فعال بودن)
- bank_account: string (شماره حساب بانکی)
- bank_name: string (نام بانک)
- bank_sheba: string (شماره شبا)

### جدول customers
- id: uuid (کلید اصلی)
- user_id: uuid (ارجاع به جدول users)
- credit_limit: decimal (سقف اعتبار)
- total_purchases: decimal (مجموع خرید)
- total_payments: decimal (مجموع پرداخت‌ها)
- total_debt: decimal (مجموع بدهی)
- last_purchase_date: timestamp (تاریخ آخرین خرید)
- last_payment_date: timestamp (تاریخ آخرین پرداخت)

## 2. آمارهای هر نقش کاربری

### ادمین
- تعداد کل کاربران
- تعداد فروشندگان فعال
- تعداد مشتریان فعال
- مجموع فروش کل
- مجموع درآمد
- مجموع بدهی‌ها
- تعداد فاکتورهای صادر شده
- تعداد محصولات موجود
- تعداد تسویه‌های انجام شده

### مدیر
- تعداد فروشندگان تحت مدیریت
- مجموع فروش روزانه/هفتگی/ماهانه
- تعداد مشتریان جدید
- وضعیت موجودی انبار
- گزارش‌های مالی
- وضعیت پرداخت‌ها
- آمار بازگشت کالا

### فروشنده
- مجموع فروش شخصی
- کمیسیون قابل دریافت
- تعداد مشتریان شخصی
- آمار فروش روزانه/هفتگی/ماهانه
- وضعیت تسویه‌ها
- رتبه‌بندی در بین فروشندگان
- درصد تحقق اهداف فروش

### مشتری
- تاریخچه خریدها
- وضعیت اعتبار
- مبلغ بدهی
- تاریخچه پرداخت‌ها
- تعداد فاکتورهای صادر شده
- وضعیت اقساط
- تخفیف‌های دریافتی

## 3. ارتباطات بین کاربران

### فروشنده-مشتری
- هر فروشنده می‌تواند چندین مشتری داشته باشد
- هر مشتری می‌تواند از چندین فروشنده خرید کند
- تاریخچه ارتباطات
- آمار فروش به هر مشتری
- وضعیت پرداخت هر مشتری

### معرف-معرف‌شده
- هر کاربر می‌تواند معرف چندین کاربر باشد
- هر کاربر می‌تواند یک معرف داشته باشد
- محاسبه پاداش معرف
- تاریخچه معرفی‌ها
- وضعیت تأیید معرفی‌ها

### مدیر-فروشنده
- هر مدیر می‌تواند چندین فروشنده را مدیریت کند
- هر فروشنده یک مدیر دارد
- گزارش‌های عملکرد
- تأیید تسویه‌ها
- تنظیم اهداف فروش

## 4. آمارهای انبار

### موجودی
- تعداد کل محصولات
- موجودی هر محصول
- حداقل موجودی
- حداکثر موجودی
- تاریخ آخرین شمارش
- ارزش کل موجودی
- محصولات کم‌بود

### ورود و خروج
- تاریخچه ورود کالا
- تاریخچه خروج کالا
- تعداد فاکتورهای ورود
- تعداد فاکتورهای خروج
- ارزش ورود و خروج
- موجودی اول دوره
- موجودی پایان دوره

### گزارش‌های انبار
- گردش کالا
- ارزش موجودی
- محصولات پرفروش
- محصولات کم‌فروش
- تاریخ انقضا
- ضایعات
- برگشت از فروش

## 5. آمارهای مالی

### درآمد
- فروش نقدی
- فروش اقساطی
- کمیسیون‌ها
- سود حاصل
- تخفیف‌ها
- مالیات
- عوارض

### هزینه‌ها
- خرید کالا
- حقوق و دستمزد
- هزینه‌های عملیاتی
- هزینه‌های اداری
- هزینه‌های مالی
- هزینه‌های بازاریابی
- سایر هزینه‌ها

### بدهی‌ها
- بدهی مشتریان
- بدهی به تأمین‌کنندگان
- اقساط پرداختی
- چک‌های دریافتی
- چک‌های پرداختی
- تسویه‌های انجام شده
- تسویه‌های در انتظار

## 6. آمارهای فروش

### کلی
- تعداد فاکتورها
- مبلغ کل فروش
- تعداد مشتریان
- متوسط سبد خرید
- نرخ تبدیل
- نرخ بازگشت
- سود حاصل

### جزئی
- فروش به تفکیک محصول
- فروش به تفکیک فروشنده
- فروش به تفکیک مشتری
- فروش به تفکیک منطقه
- فروش به تفکیک زمان
- فروش به تفکیک روش پرداخت
- فروش به تفکیک تخفیف 