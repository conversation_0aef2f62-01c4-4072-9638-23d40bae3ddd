package com.example.sharen.ui.dashboard

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Transaction
import com.example.sharen.data.model.TransactionType
import com.example.sharen.databinding.ItemTransactionBinding
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale

class TransactionAdapter(
    private val onItemClick: (Transaction) -> Unit
) : ListAdapter<Transaction, TransactionAdapter.TransactionViewHolder>(TransactionDiffCallback()) {

    private val dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa"))
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val binding = ItemTransactionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TransactionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        val transaction = getItem(position)
        holder.bind(transaction)
    }

    inner class TransactionViewHolder(
        private val binding: ItemTransactionBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(transaction: Transaction) {
            // Assuming tvTransactionNumber is for orderId/invoiceNumber
            binding.tvTransactionNumber.text = transaction.orderId ?: transaction.id
            // Customer name is not directly available in Transaction model, might need to fetch from repository
            // For now, leaving it out or setting a placeholder
            binding.tvCustomerName.text = transaction.customerId // Placeholder, ideally fetch customer name

            binding.tvTransactionDate.text = dateFormatter.format(transaction.createdAt)
            binding.tvTransactionAmount.text = 
                "${numberFormatter.format(transaction.amount)} تومان"

            // Set transaction type text and background
            val context = binding.root.context
            when (transaction.type) {
                TransactionType.SALE -> {
                    binding.tvTransactionStatus.text = context.getString(R.string.total_sales) // Placeholder, need specific string
                    binding.tvTransactionStatus.setBackgroundResource(R.drawable.bg_status_paid) // Placeholder color
                }
                TransactionType.PAYMENT -> {
                    binding.tvTransactionStatus.text = context.getString(R.string.payments) // Placeholder
                    binding.tvTransactionStatus.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.info) // Placeholder color
                }
                TransactionType.PURCHASE -> {
                    binding.tvTransactionStatus.text = "خرید" // Placeholder
                    binding.tvTransactionStatus.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.warning) // Placeholder color
                }
                TransactionType.REFUND -> {
                    binding.tvTransactionStatus.text = "بازپرداخت" // Placeholder
                    binding.tvTransactionStatus.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.error) // Placeholder color
                }
                TransactionType.ADJUSTMENT -> {
                    binding.tvTransactionStatus.text = "تعدیل" // Placeholder
                    binding.tvTransactionStatus.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.text_secondary) // Placeholder color
                }
            }
        }
    }

    class TransactionDiffCallback : DiffUtil.ItemCallback<Transaction>() {
        override fun areItemsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem == newItem
        }
    }
}
