package com.example.sharen.presentation.ui.customer.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.core.utils.toCurrency
import com.example.sharen.core.utils.toPersianDate
import com.example.sharen.databinding.ItemCustomerBinding
import com.example.sharen.domain.model.Customer

/**
 * Adapter برای نمایش لیست مشتریان
 */
class CustomerAdapter(
    private val onItemClick: (Customer) -> Unit,
    private val onCallClick: (Customer) -> Unit,
    private val onEditClick: (Customer) -> Unit
) : ListAdapter<Customer, CustomerAdapter.CustomerViewHolder>(CustomerDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CustomerViewHolder {
        val binding = ItemCustomerBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CustomerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CustomerViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class CustomerViewHolder(
        private val binding: ItemCustomerBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.apply {
                root.setOnClickListener {
                    val position = adapterPosition
                    if (position != RecyclerView.NO_POSITION) {
                        onItemClick(getItem(position))
                    }
                }

                btnCall.setOnClickListener {
                    val position = adapterPosition
                    if (position != RecyclerView.NO_POSITION) {
                        onCallClick(getItem(position))
                    }
                }

                btnEdit.setOnClickListener {
                    val position = adapterPosition
                    if (position != RecyclerView.NO_POSITION) {
                        onEditClick(getItem(position))
                    }
                }
            }
        }

        fun bind(customer: Customer) {
            binding.apply {
                // اطلاعات اصلی
                tvCustomerName.text = customer.name
                tvCustomerPhone.text = customer.phone
                tvCustomerAddress.text = customer.address ?: "آدرس ثبت نشده"

                // اطلاعات مالی
                tvTotalPurchases.text = customer.totalPurchases.toCurrency()
                tvTotalDebt.text = customer.totalDebt.toCurrency()
                tvCreditLimit.text = customer.creditLimit.toCurrency()

                // محاسبه اعتبار باقیمانده
                val remainingCredit = customer.remainingCredit
                tvRemainingCredit.text = remainingCredit.toCurrency()

                // تنظیم رنگ بر اساس وضعیت اعتباری
                val creditStatusColor = when (customer.creditStatus) {
                    com.example.sharen.domain.model.CreditStatus.CLEAR -> 
                        android.graphics.Color.parseColor("#4CAF50") // سبز
                    com.example.sharen.domain.model.CreditStatus.GOOD -> 
                        android.graphics.Color.parseColor("#2196F3") // آبی
                    com.example.sharen.domain.model.CreditStatus.WARNING -> 
                        android.graphics.Color.parseColor("#FF9800") // نارنجی
                    com.example.sharen.domain.model.CreditStatus.CRITICAL -> 
                        android.graphics.Color.parseColor("#F44336") // قرمز
                    com.example.sharen.domain.model.CreditStatus.EXCEEDED -> 
                        android.graphics.Color.parseColor("#000000") // مشکی
                }

                tvCreditStatus.apply {
                    text = customer.creditStatus.displayName
                    setTextColor(creditStatusColor)
                }

                // آخرین خرید
                tvLastPurchase.text = customer.lastPurchaseDate?.toPersianDate() ?: "خریدی نداشته"

                // تاریخ عضویت
                tvMemberSince.text = "عضو از: ${customer.createdAt.toPersianDate()}"

                // نمایش badge برای مشتریان بدهکار
                if (customer.isDebtor) {
                    badgeDebtor.visibility = android.view.View.VISIBLE
                } else {
                    badgeDebtor.visibility = android.view.View.GONE
                }

                // نمایش badge برای مشتریان VIP (خرید بالا)
                if (customer.totalPurchases > 5_000_000) {
                    badgeVip.visibility = android.view.View.VISIBLE
                } else {
                    badgeVip.visibility = android.view.View.GONE
                }
            }
        }
    }

    class CustomerDiffCallback : DiffUtil.ItemCallback<Customer>() {
        override fun areItemsTheSame(oldItem: Customer, newItem: Customer): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Customer, newItem: Customer): Boolean {
            return oldItem == newItem
        }
    }
}
