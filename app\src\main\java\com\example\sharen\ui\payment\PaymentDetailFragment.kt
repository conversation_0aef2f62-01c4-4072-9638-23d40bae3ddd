package com.example.sharen.ui.payment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.example.sharen.R
import com.example.sharen.databinding.FragmentPaymentDetailBinding
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale

@AndroidEntryPoint
class PaymentDetailFragment : Fragment() {

    private var _binding: FragmentPaymentDetailBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PaymentViewModel by viewModels()
    private val args: PaymentDetailFragmentArgs by navArgs()
    private val dateFormat = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPaymentDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupObservers()
        setupListeners()
        viewModel.getPayment(args.paymentId)
    }

    private fun setupObservers() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.payments.collect { payments ->
                    payments.find { it.id == args.paymentId }?.let { payment ->
                        updateUI(payment)
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.loading.collect { isLoading ->
                    binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.error.collect { error ->
                    error?.let {
                        Snackbar.make(binding.root, it, Snackbar.LENGTH_LONG).show()
                        viewModel.clearError()
                    }
                }
            }
        }
    }

    private fun setupListeners() {
        binding.buttonEdit.setOnClickListener {
            findNavController().navigate(
                PaymentDetailFragmentDirections.actionPaymentDetailToPaymentEdit(args.paymentId)
            )
        }

        binding.buttonDelete.setOnClickListener {
            showDeleteConfirmationDialog()
        }

        binding.buttonConfirm.setOnClickListener {
            viewModel.confirmPayment(args.paymentId)
        }

        binding.buttonReject.setOnClickListener {
            showRejectDialog()
        }
    }

    private fun updateUI(payment: Payment) {
        binding.apply {
            textViewAmount.text = payment.amount.toString()
            textViewDate.text = dateFormat.format(payment.date)
            textViewStatus.text = payment.status.name
            textViewMethod.text = payment.paymentMethod.name
            textViewReference.text = payment.referenceNumber ?: "-"
            textViewNotes.text = payment.notes ?: "-"

            // Show/hide buttons based on payment status
            buttonConfirm.isEnabled = payment.status == PaymentStatus.PENDING
            buttonReject.isEnabled = payment.status == PaymentStatus.PENDING
            buttonEdit.isEnabled = payment.status != PaymentStatus.CONFIRMED
            buttonDelete.isEnabled = payment.status != PaymentStatus.CONFIRMED
        }
    }

    private fun showDeleteConfirmationDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.delete_payment)
            .setMessage(R.string.delete_payment_confirmation)
            .setPositiveButton(R.string.delete) { _, _ ->
                viewModel.deletePayment(args.paymentId)
                findNavController().navigateUp()
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    private fun showRejectDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.reject_payment)
            .setMessage(R.string.reject_payment_confirmation)
            .setPositiveButton(R.string.reject) { _, _ ->
                viewModel.rejectPayment(args.paymentId, "Rejected by user")
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 