package com.example.sharen.util

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object DateUtils {
    private val persianDateFormat = SimpleDateFormat("yyyy/MM/dd", Locale("fa"))
    
    fun formatDate(date: Date): String {
        return persianDateFormat.format(date)
    }
    
    fun formatDateTime(date: Date): String {
        val dateTimeFormat = SimpleDateFormat("yyyy/MM/dd HH:mm", Locale("fa"))
        return dateTimeFormat.format(date)
    }
} 