package com.example.sharen.util

import java.text.NumberFormat
import java.util.Locale

object NumberUtils {
    private val numberFormat = NumberFormat.getCurrencyInstance(Locale("fa"))
    
    fun formatCurrency(amount: Double): String {
        return numberFormat.format(amount)
    }
    
    fun formatNumber(number: Double): String {
        return NumberFormat.getNumberInstance(Locale("fa")).format(number)
    }
} 