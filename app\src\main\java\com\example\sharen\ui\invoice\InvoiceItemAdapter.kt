package com.example.sharen.ui.invoice

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.data.model.InvoiceItem
import com.example.sharen.databinding.ItemInvoiceProductBinding
import java.text.NumberFormat

class InvoiceItemAdapter(
    private val numberFormatter: NumberFormat
) : ListAdapter<InvoiceItem, InvoiceItemAdapter.InvoiceItemViewHolder>(InvoiceItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InvoiceItemViewHolder {
        val binding = ItemInvoiceProductBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return InvoiceItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: InvoiceItemViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class InvoiceItemViewHolder(
        private val binding: ItemInvoiceProductBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: InvoiceItem) {
            // نام محصول
            binding.tvProductName.text = item.productName
            binding.tvProductCode.text = item.productCode ?: ""

            // اطلاعات قیمت و تعداد
            binding.tvQuantity.text = numberFormatter.format(item.quantity)
            binding.tvUnitPrice.text = "${numberFormatter.format(item.unitPrice)} تومان"
            binding.tvTotalPrice.text = "${numberFormatter.format(item.totalPrice)} تومان"

            // اطلاعات تخفیف و مالیات (اگر وجود داشته باشد)
            if (item.discount > 0) {
                binding.tvDiscount.text = "${numberFormatter.format(item.discount)} تومان"
            } else {
                binding.tvDiscount.text = "0"
            }

            if (item.tax > 0) {
                binding.tvTax.text = "${numberFormatter.format(item.tax)} تومان"
            } else {
                binding.tvTax.text = "0"
            }
        }
    }

    /**
     * برای مقایسه آیتم‌ها و بهینه‌سازی رندرینگ
     */
    class InvoiceItemDiffCallback : DiffUtil.ItemCallback<InvoiceItem>() {
        override fun areItemsTheSame(oldItem: InvoiceItem, newItem: InvoiceItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: InvoiceItem, newItem: InvoiceItem): Boolean {
            return oldItem == newItem
        }
    }
}