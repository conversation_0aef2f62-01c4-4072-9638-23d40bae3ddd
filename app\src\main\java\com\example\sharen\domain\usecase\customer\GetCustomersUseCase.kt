package com.example.sharen.domain.usecase.customer

import com.example.sharen.domain.model.Customer
import com.example.sharen.domain.repository.CustomerRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use Case برای دریافت مشتریان
 */
class GetCustomersUseCase @Inject constructor(
    private val customerRepository: CustomerRepository
) {
    
    /**
     * دریافت تمام مشتریان
     */
    operator fun invoke(): Flow<List<Customer>> {
        return customerRepository.getAllCustomers()
    }
    
    /**
     * دریافت مشتریان با فیلتر
     */
    operator fun invoke(filter: CustomerFilter): Flow<List<Customer>> {
        return when (filter) {
            is CustomerFilter.All -> customerRepository.getAllCustomers()
            is CustomerFilter.WithDebt -> customerRepository.getCustomersByDebtStatus(true)
            is CustomerFilter.WithoutDebt -> customerRepository.getCustomersByDebtStatus(false)
            is CustomerFilter.LowCredit -> customerRepository.getCustomersWithLowCredit()
            is CustomerFilter.Active -> customerRepository.getActiveCustomers()
            is CustomerFilter.Inactive -> customerRepository.getInactiveCustomers(filter.daysSinceLastPurchase)
            is CustomerFilter.BySeller -> customerRepository.getCustomersBySeller(filter.sellerId)
            is CustomerFilter.Search -> customerRepository.searchCustomers(filter.query)
        }
    }
}

/**
 * فیلترهای مشتری
 */
sealed class CustomerFilter {
    object All : CustomerFilter()
    object WithDebt : CustomerFilter()
    object WithoutDebt : CustomerFilter()
    object LowCredit : CustomerFilter()
    object Active : CustomerFilter()
    data class Inactive(val daysSinceLastPurchase: Int = 90) : CustomerFilter()
    data class BySeller(val sellerId: String) : CustomerFilter()
    data class Search(val query: String) : CustomerFilter()
}
