package com.example.sharen.data.model

import java.math.BigDecimal
import java.util.Date
import java.util.UUID

data class Customer(
    val id: String,
    val userId: String,
    val name: String,
    val phone: String,
    val address: String? = null,
    val notes: String? = null,
    val debtAmount: Long = 0,
    val totalPurchases: Long = 0,
    val lastPurchaseDate: Date? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) {
    fun hasDebt(): Boolean = debtAmount > 0
    
    fun formattedLastPurchase(): String? = lastPurchaseDate?.let {
        // Will be implemented with a proper date formatter
        it.toString()
    }
    
    fun formattedRegistrationDate(): String = 
        // Will be implemented with a proper date formatter
        createdAt.toString()
        
    fun getInitials(): String {
        if (name.isEmpty()) return "?"
        
        val parts = name.split(" ")
        return if (parts.size > 1) {
            (parts.first().firstOrNull()?.toString() ?: "") + 
                (parts.last().firstOrNull()?.toString() ?: "")
        } else {
            parts.first().take(2)
        }
    }
} 