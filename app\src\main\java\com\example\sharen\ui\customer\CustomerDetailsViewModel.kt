package com.example.sharen.ui.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Customer
import com.example.sharen.data.model.Transaction
import com.example.sharen.data.repository.CustomerRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CustomerDetailsViewModel @Inject constructor(
    private val customerRepository: CustomerRepository
) : ViewModel() {

    // Customer data
    private val _customer = MutableLiveData<Customer?>()
    val customer: LiveData<Customer?> = _customer

    // Recent transactions
    private val _recentTransactions = MutableLiveData<List<Transaction>>(emptyList())
    val recentTransactions: LiveData<List<Transaction>> = _recentTransactions

    // Loading state
    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    // Error state
    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    fun loadCustomer(customerId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            customerRepository.getCustomerById(customerId)
                .catch { e ->
                    _error.postValue(e.message ?: "خطا در بارگذاری اطلاعات مشتری")
                    _isLoading.postValue(false)
                }
                .collectLatest { customer ->
                    _customer.postValue(customer)
                    _isLoading.postValue(false)
                    
                    // For now, we'll not load transactions since we don't have a transaction repository
                    // In a real implementation, we would load transactions here
                }
        }
    }

    fun deleteCustomer(customerId: String, onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            customerRepository.deleteCustomer(customerId)
                .onSuccess {
                    _isLoading.value = false
                    onSuccess()
                }
                .onFailure { e ->
                    _error.value = e.message ?: "خطا در حذف مشتری"
                    _isLoading.value = false
                }
        }
    }
} 