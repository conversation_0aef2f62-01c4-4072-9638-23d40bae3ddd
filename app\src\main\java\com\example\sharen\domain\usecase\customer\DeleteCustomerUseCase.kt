package com.example.sharen.domain.usecase.customer

import com.example.sharen.domain.repository.CustomerRepository
import javax.inject.Inject

/**
 * Use Case برای حذف مشتری
 */
class DeleteCustomerUseCase @Inject constructor(
    private val customerRepository: CustomerRepository
) {
    suspend operator fun invoke(customerId: String): Result<Unit> {
        if (customerId.isBlank()) {
            return Result.failure(IllegalArgumentException("شناسه مشتری نمی‌تواند خالی باشد"))
        }
        
        return customerRepository.deleteCustomer(customerId)
    }
}
