package com.example.sharen

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.sharen.core.base.BaseActivity
import com.example.sharen.databinding.ActivityMainBinding
import com.example.sharen.presentation.ui.auth.LoginActivity
import com.example.sharen.presentation.ui.dashboard.DashboardActivity
import com.example.sharen.presentation.viewmodel.AuthViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Activity اصلی - Splash Screen
 */
@AndroidEntryPoint
class MainActivity : BaseActivity<ActivityMainBinding>() {

    private val authViewModel: AuthViewModel by viewModels()

    override fun getViewBinding(): ActivityMainBinding {
        return ActivityMainBinding.inflate(layoutInflater)
    }

    override fun setupViews() {
        // نمایش لوگو و انیمیشن
        setupSplashAnimation()
    }

    override fun observeData() {
        lifecycleScope.launch {
            // نمایش splash برای 2 ثانیه
            delay(2000)

            // بررسی وضعیت ورود کاربر
            checkUserLoginStatus()
        }
    }

    private fun setupSplashAnimation() {
        // انیمیشن fade in برای لوگو
        binding.ivLogo.alpha = 0f
        binding.tvAppName.alpha = 0f

        binding.ivLogo.animate()
            .alpha(1f)
            .setDuration(1000)
            .start()

        binding.tvAppName.animate()
            .alpha(1f)
            .setDuration(1000)
            .setStartDelay(500)
            .start()
    }

    private fun checkUserLoginStatus() {
        // فعلاً مستقیماً به صفحه ورود می‌رویم
        // بعداً باید وضعیت ورود کاربر را از SharedPreferences یا Database بررسی کنیم
        navigateToLogin()
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }

    private fun navigateToDashboard() {
        val intent = Intent(this, DashboardActivity::class.java)
        startActivity(intent)
        finish()
    }
}