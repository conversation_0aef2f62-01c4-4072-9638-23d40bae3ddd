package com.example.sharen

import android.app.Application
import android.util.Log
import com.example.sharen.data.migration.DataMigrationHelper
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltAndroidApp
class SharenApplication : Application() {

    @Inject
    lateinit var dataMigrationHelper: DataMigrationHelper

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    override fun onCreate() {
        super.onCreate()

        Log.d("SharenApplication", "Application started")

        // اجرای Migration داده‌ها در background
        applicationScope.launch {
            try {
                dataMigrationHelper.performDataMigration(this@SharenApplication)
                    .onSuccess {
                        Log.d("SharenApplication", "Data migration completed successfully")
                    }
                    .onFailure { error ->
                        Log.e("SharenApplication", "Data migration failed", error)
                    }
            } catch (e: Exception) {
                Log.e("SharenApplication", "Error during data migration", e)
            }
        }
    }
}