package com.example.sharen.ui.product

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.core.view.isVisible
import com.example.sharen.R
import com.example.sharen.data.model.Product
import com.example.sharen.databinding.ActivityProductListBinding
import com.google.android.material.chip.Chip
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.util.Locale

@AndroidEntryPoint
class ProductListActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProductListBinding
    private val viewModel: ProductListViewModel by viewModels()
    private lateinit var productAdapter: ProductAdapter
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))

    // لیست دسته‌بندی‌های محصولات
    private val categories = mutableSetOf<String>()
    private val categoryChips = mutableMapOf<String, Chip>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupSearchView()
        setupFilterButton()
        setupFilterChips()
        setupFab()
        setupObservers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    private fun setupRecyclerView() {
        productAdapter = ProductAdapter { product ->
            navigateToProductDetails(product)
        }
        binding.rvProducts.adapter = productAdapter
    }

    private fun setupSearchView() {
        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let {
                    if (it.isNotEmpty()) {
                        viewModel.searchProducts(it)
                    }
                }
                return true
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                newText?.let {
                    if (it.isEmpty()) {
                        viewModel.clearFilters()
                    }
                }
                return true
            }
        })
    }

    private fun setupFilterButton() {
        binding.btnFilter.setOnClickListener {
            if (binding.chipGroupFilters.isVisible) {
                binding.chipGroupFilters.visibility = View.GONE
            } else {
                binding.chipGroupFilters.visibility = View.VISIBLE
            }
        }
    }

    private fun setupFilterChips() {
        binding.chipLowStock.setOnClickListener {
            if (binding.chipLowStock.isChecked) {
                // نمایش محصولات با موجودی کم
                viewModel.products.value?.let { products ->
                    val lowStockProducts = products.filter { it.isLowStock }
                    productAdapter.submitList(lowStockProducts)
                    updateProductCount(lowStockProducts.size)
                }
            } else {
                // نمایش همه محصولات
                viewModel.loadProducts()
            }
        }
    }

    private fun setupFab() {
        binding.fabAddProduct.setOnClickListener {
            navigateToAddProduct()
        }
    }

    private fun setupObservers() {
        viewModel.products.observe(this) { products ->
            productAdapter.submitList(products)
            binding.tvNoProducts.visibility = if (products.isEmpty()) View.VISIBLE else View.GONE
            binding.rvProducts.visibility = if (products.isEmpty()) View.GONE else View.VISIBLE
            
            // به روز رسانی لیست دسته‌بندی‌ها
            updateCategories(products)
        }

        viewModel.productCount.observe(this) { count ->
            updateProductCount(count)
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(this) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun updateProductCount(count: Int) {
        binding.tvProductCount.text = getString(R.string.product_count_format, numberFormatter.format(count))
    }

    private fun updateCategories(products: List<Product>) {
        // جمع‌آوری دسته‌بندی‌های منحصر به فرد
        val newCategories = products
            .mapNotNull { it.category }
            .filter { it.isNotEmpty() }
            .toSet()
        
        if (newCategories == categories) {
            return // تغییری نداشته
        }
        
        // حذف دسته‌بندی‌های قبلی
        categoryChips.values.forEach { chip ->
            binding.chipGroupFilters.removeView(chip)
        }
        categoryChips.clear()
        
        // اضافه کردن دسته‌بندی‌های جدید
        categories.clear()
        categories.addAll(newCategories)
        
        // ایجاد چیپ برای هر دسته‌بندی
        for (category in categories) {
            val chip = Chip(this).apply {
                text = category
                isCheckable = true
                setOnClickListener {
                    if (isChecked) {
                        viewModel.filterByCategory(category)
                    } else {
                        viewModel.clearFilters()
                    }
                }
            }
            binding.chipGroupFilters.addView(chip)
            categoryChips[category] = chip
        }
    }

    private fun navigateToProductDetails(product: Product) {
        val intent = Intent(this, ProductDetailsActivity::class.java).apply {
            putExtra(ProductDetailsActivity.EXTRA_PRODUCT_ID, product.id)
        }
        startActivity(intent)
    }

    private fun navigateToAddProduct() {
        val intent = Intent(this, ProductFormActivity::class.java)
        startActivity(intent)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    override fun onResume() {
        super.onResume()
        // بارگذاری مجدد محصولات در هنگام بازگشت به صفحه
        viewModel.loadProducts()
    }
} 