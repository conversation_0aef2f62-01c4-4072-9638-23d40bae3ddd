package com.example.sharen.ui.invoice

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.widget.doAfterTextChanged
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.databinding.ActivityInvoiceListBinding
import com.example.sharen.ui.dashboard.DashboardActivity
import com.google.android.material.navigation.NavigationBarView
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.util.Locale

@AndroidEntryPoint
class InvoiceListActivity : AppCompatActivity(), NavigationBarView.OnItemSelectedListener {

    private lateinit var binding: ActivityInvoiceListBinding
    private val viewModel: InvoiceListViewModel by viewModels()
    private lateinit var adapter: InvoiceAdapter
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInvoiceListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupObservers()
    }

    private fun setupUI() {
        // تنظیم نوار ابزار
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // تنظیم منوی پایین
        binding.bottomNavigation.setOnItemSelectedListener(this)
        binding.bottomNavigation.selectedItemId = R.id.navigation_invoices

        // تنظیم RecyclerView
        adapter = InvoiceAdapter(
            onInvoiceClick = { invoice -> navigateToInvoiceDetails(invoice) },
            numberFormatter = numberFormatter
        )
        
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@InvoiceListActivity)
            adapter = <EMAIL>
        }

        // تنظیم جستجو
        binding.searchView.doAfterTextChanged { text ->
            viewModel.searchInvoices(text.toString())
        }

        // تنظیم دکمه افزودن فاکتور جدید
        binding.fabAddInvoice.setOnClickListener {
            navigateToCreateInvoice()
        }

        // تنظیم فیلترها
        binding.chipAll.setOnClickListener { viewModel.filterInvoices(null) }
        binding.chipPaid.setOnClickListener { viewModel.filterInvoices("PAID") }
        binding.chipPartiallyPaid.setOnClickListener { viewModel.filterInvoices("PARTIALLY_PAID") }
        binding.chipPending.setOnClickListener { viewModel.filterInvoices("PENDING") }
    }

    private fun setupObservers() {
        viewModel.invoices.observe(this) { invoices ->
            adapter.submitList(invoices)
            binding.emptyView.visibility = if (invoices.isEmpty()) View.VISIBLE else View.GONE
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
    }

    private fun navigateToInvoiceDetails(invoice: com.example.sharen.data.model.Invoice) {
        val intent = Intent(this, InvoiceDetailsActivity::class.java).apply {
            putExtra(InvoiceDetailsActivity.EXTRA_INVOICE_ID, invoice.id)
        }
        startActivity(intent)
    }

    private fun navigateToCreateInvoice() {
        val intent = Intent(this, SalesInvoiceActivity::class.java)
        startActivity(intent)
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.navigation_dashboard -> {
                startActivity(Intent(this, DashboardActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_customers -> {
                startActivity(Intent(this, com.example.sharen.ui.customer.CustomerListActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_invoices -> {
                // روی صفحه فعلی هستیم
                return true
            }
            R.id.navigation_products -> {
                // انتقال به صفحه محصولات
                Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
                return true
            }
            R.id.navigation_reports -> {
                // انتقال به صفحه گزارشات
                Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
                return true
            }
        }
        return false
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 