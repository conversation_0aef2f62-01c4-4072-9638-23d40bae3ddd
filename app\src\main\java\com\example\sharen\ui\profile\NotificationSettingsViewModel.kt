package com.example.sharen.ui.profile

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class NotificationSettingsViewModel @Inject constructor() : ViewModel() {
    private val _notificationsEnabled = MutableLiveData<Boolean>()
    val notificationsEnabled: LiveData<Boolean> = _notificationsEnabled

    private val _soundEnabled = MutableLiveData<Boolean>()
    val soundEnabled: LiveData<Boolean> = _soundEnabled

    private val _vibrationEnabled = MutableLiveData<Boolean>()
    val vibrationEnabled: LiveData<Boolean> = _vibrationEnabled

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    fun setNotificationsEnabled(enabled: Boolean) {
        _notificationsEnabled.value = enabled
    }

    fun setSoundEnabled(enabled: <PERSON>olean) {
        _soundEnabled.value = enabled
    }

    fun setVibrationEnabled(enabled: Boolean) {
        _vibrationEnabled.value = enabled
    }
} 