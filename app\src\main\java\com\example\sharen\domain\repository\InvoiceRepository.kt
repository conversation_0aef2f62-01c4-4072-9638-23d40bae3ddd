package com.example.sharen.domain.repository

import com.example.sharen.domain.model.Invoice
import com.example.sharen.domain.model.InvoiceItem
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Repository Interface برای مدیریت فاکتورها
 */
interface InvoiceRepository {
    
    /**
     * دریافت تمام فاکتورها
     */
    fun getAllInvoices(): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتور با شناسه
     */
    suspend fun getInvoiceById(invoiceId: String): Result<Invoice?>
    
    /**
     * دریافت فاکتورهای مشتری
     */
    fun getInvoicesByCustomer(customerId: String): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتورهای فروشنده
     */
    fun getInvoicesBySeller(sellerId: String): Flow<List<Invoice>>
    
    /**
     * جستجوی فاکتورها
     */
    fun searchInvoices(query: String): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتورها در بازه زمانی
     */
    fun getInvoicesByDateRange(startDate: Date, endDate: Date): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتورها بر اساس وضعیت
     */
    fun getInvoicesByStatus(status: com.example.sharen.domain.model.InvoiceStatus): Flow<List<Invoice>>
    
    /**
     * افزودن فاکتور جدید
     */
    suspend fun addInvoice(invoice: Invoice): Result<Invoice>
    
    /**
     * بروزرسانی فاکتور
     */
    suspend fun updateInvoice(invoice: Invoice): Result<Invoice>
    
    /**
     * حذف فاکتور
     */
    suspend fun deleteInvoice(invoiceId: String): Result<Unit>
    
    /**
     * بروزرسانی وضعیت فاکتور
     */
    suspend fun updateInvoiceStatus(invoiceId: String, status: com.example.sharen.domain.model.InvoiceStatus): Result<Unit>
    
    /**
     * دریافت آیتم‌های فاکتور
     */
    fun getInvoiceItems(invoiceId: String): Flow<List<InvoiceItem>>
    
    /**
     * افزودن آیتم به فاکتور
     */
    suspend fun addInvoiceItem(invoiceId: String, item: InvoiceItem): Result<InvoiceItem>
    
    /**
     * بروزرسانی آیتم فاکتور
     */
    suspend fun updateInvoiceItem(item: InvoiceItem): Result<InvoiceItem>
    
    /**
     * حذف آیتم از فاکتور
     */
    suspend fun deleteInvoiceItem(itemId: String): Result<Unit>
    
    /**
     * محاسبه مجدد مبالغ فاکتور
     */
    suspend fun recalculateInvoice(invoiceId: String): Result<Invoice>
    
    /**
     * دریافت آمار فروش
     */
    suspend fun getSalesStatistics(startDate: Date, endDate: Date): Result<Map<String, Any>>
    
    /**
     * دریافت فاکتورهای اخیر
     */
    fun getRecentInvoices(limit: Int = 10): Flow<List<Invoice>>
    
    /**
     * دریافت مجموع فروش
     */
    suspend fun getTotalSales(startDate: Date, endDate: Date): Result<Long>
}
