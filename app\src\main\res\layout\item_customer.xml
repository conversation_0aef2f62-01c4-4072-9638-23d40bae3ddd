<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/iv_customer_avatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/bg_circle_avatar"
            android:contentDescription="@string/customer_avatar"
            android:padding="8dp"
            android:src="@android:drawable/ic_menu_myplaces"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/tv_customer_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tv_debt_amount"
            app:layout_constraintStart_toEndOf="@+id/iv_customer_avatar"
            app:layout_constraintTop_toTopOf="@+id/iv_customer_avatar"
            tools:text="علی محمدی" />

        <TextView
            android:id="@+id/tv_customer_phone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="@+id/tv_customer_name"
            app:layout_constraintStart_toStartOf="@+id/tv_customer_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_customer_name"
            tools:text="۰۹۱۲۳۴۵۶۷۸۹" />

        <TextView
            android:id="@+id/tv_customer_address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@+id/tv_customer_name"
            app:layout_constraintStart_toStartOf="@+id/tv_customer_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_customer_phone"
            tools:text="تهران، خیابان ولیعصر" />

        <!-- Badges -->
        <TextView
            android:id="@+id/badge_debtor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_badge_red"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:text="بدهکار"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_customer_name"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/badge_vip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_badge_gold"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:text="VIP"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/badge_debtor"
            tools:visibility="visible" />

        <!-- Financial Info -->
        <TextView
            android:id="@+id/tv_total_purchases"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/tv_customer_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_customer_address"
            tools:text="کل خرید: ۵,۰۰۰,۰۰۰ تومان" />

        <TextView
            android:id="@+id/tv_total_debt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:textColor="@color/error"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@+id/tv_total_purchases"
            app:layout_constraintTop_toTopOf="@+id/tv_total_purchases"
            tools:text="بدهی: ۱,۵۰۰,۰۰۰ تومان" />

        <TextView
            android:id="@+id/tv_credit_limit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/tv_total_purchases"
            app:layout_constraintTop_toBottomOf="@+id/tv_total_purchases"
            tools:text="حد اعتبار: ۳,۰۰۰,۰۰۰ تومان" />

        <TextView
            android:id="@+id/tv_remaining_credit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:textColor="@color/success"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@+id/tv_credit_limit"
            app:layout_constraintTop_toTopOf="@+id/tv_credit_limit"
            tools:text="اعتبار باقی: ۱,۵۰۰,۰۰۰ تومان" />

        <TextView
            android:id="@+id/tv_credit_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@+id/tv_credit_limit"
            app:layout_constraintTop_toBottomOf="@+id/tv_credit_limit"
            tools:text="وضعیت: خوب"
            tools:textColor="@color/success" />

        <TextView
            android:id="@+id/tv_last_purchase"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@+id/tv_credit_status"
            app:layout_constraintTop_toTopOf="@+id/tv_credit_status"
            tools:text="آخرین خرید: ۲ روز پیش" />

        <TextView
            android:id="@+id/tv_member_since"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/tv_credit_status"
            app:layout_constraintTop_toBottomOf="@+id/tv_credit_status"
            tools:text="عضو از: ۱۴۰۲/۰۱/۰۱" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@+id/tv_member_since">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_call"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/call"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_menu_call"
                app:iconSize="16dp"
                app:iconTint="@color/primary" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_message"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/message"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_dialog_email"
                app:iconSize="16dp"
                app:iconTint="@color/primary" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/edit"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_menu_edit"
                app:iconSize="16dp"
                app:iconTint="@color/primary" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_new_invoice"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/new_invoice"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_menu_add"
                app:iconSize="16dp"
                app:iconTint="@color/primary" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>