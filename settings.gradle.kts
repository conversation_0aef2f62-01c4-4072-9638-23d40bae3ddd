pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
        
        // Aliyun Mirrors (HTTPS)
        maven { 
            url = uri("https://maven.aliyun.com/repository/public")
        }
        maven { 
            url = uri("https://maven.aliyun.com/repository/google")
        }
        maven { 
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
        }
        maven { 
            url = uri("https://maven.aliyun.com/repository/jcenter")
        }
        maven { 
            url = uri("https://maven.aliyun.com/repository/central")
        }
        
        // Additional repositories
        maven { 
            url = uri("https://jitpack.io")
        }
        maven { 
            url = uri("https://maven.pkg.jetbrains.space/public/p/compose/dev")
        }
        maven { 
            url = uri("https://s01.oss.sonatype.org/content/repositories/snapshots/")
        }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { 
            url = uri("https://jitpack.io")
        }
        maven { 
            url = uri("https://s01.oss.sonatype.org/content/repositories/snapshots/")
        }
    }
}

rootProject.name = "sharen"
include(":app")
