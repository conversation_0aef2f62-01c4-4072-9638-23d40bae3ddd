package com.example.sharen.ui.installment.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.data.model.Installment
import com.example.sharen.databinding.ItemInstallmentBinding
import java.text.SimpleDateFormat
import java.util.Locale

class InstallmentAdapter(
    private val onItemClick: (Installment) -> Unit
) : ListAdapter<Installment, InstallmentAdapter.InstallmentViewHolder>(InstallmentDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InstallmentViewHolder {
        val binding = ItemInstallmentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return InstallmentViewHolder(binding)
    }

    override fun onBindViewHolder(holder: InstallmentViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class InstallmentViewHolder(
        private val binding: ItemInstallmentBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        private val dateFormat = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())

        init {
            binding.root.setOnClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(installment: Installment) {
            binding.apply {
                textViewTotalAmount.text = installment.totalAmount.toString()
                textViewPaidAmount.text = installment.paidAmount.toString()
                textViewDueDate.text = dateFormat.format(installment.dueDate)
                chipStatus.text = installment.status.name
                textViewNotes.text = installment.notes ?: "-"
            }
        }
    }

    private class InstallmentDiffCallback : DiffUtil.ItemCallback<Installment>() {
        override fun areItemsTheSame(oldItem: Installment, newItem: Installment): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Installment, newItem: Installment): Boolean {
            return oldItem == newItem
        }
    }
} 