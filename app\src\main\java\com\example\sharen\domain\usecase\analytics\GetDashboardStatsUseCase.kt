package com.example.sharen.domain.usecase.analytics

import com.example.sharen.domain.model.DashboardStats
import com.example.sharen.domain.repository.CustomerRepository
import com.example.sharen.domain.repository.ProductRepository
import com.example.sharen.domain.repository.InvoiceRepository
import com.example.sharen.domain.repository.PaymentRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import java.util.Calendar
import java.util.Date
import javax.inject.Inject

/**
 * Use Case برای دریافت آمار داشبورد
 */
class GetDashboardStatsUseCase @Inject constructor(
    private val customerRepository: CustomerRepository,
    private val productRepository: ProductRepository,
    private val invoiceRepository: InvoiceRepository,
    private val paymentRepository: PaymentRepository
) {
    operator fun invoke(): Flow<DashboardStats> {
        val today = Date()
        val calendar = Calendar.getInstance()
        
        // محاسبه تاریخ شروع ماه جاری
        calendar.time = today
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val monthStart = calendar.time
        
        // محاسبه تاریخ شروع هفته جاری
        calendar.time = today
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY) // شنبه شروع هفته
        val weekStart = calendar.time
        
        return combine(
            customerRepository.getAllCustomers(),
            productRepository.getAllProducts(),
            invoiceRepository.getInvoicesByDateRange(monthStart, today),
            paymentRepository.getPaymentsByDateRange(monthStart, today),
            customerRepository.getCustomersByDebtStatus(hasDebt = true),
            productRepository.getLowStockProducts()
        ) { customers, products, monthlyInvoices, monthlyPayments, debtorCustomers, lowStockProducts ->
            
            // محاسبه آمار فروش ماهانه
            val monthlySales = monthlyInvoices.sumOf { it.finalAmount }
            val monthlyInvoiceCount = monthlyInvoices.size
            
            // محاسبه آمار پرداخت ماهانه
            val monthlyPayments = monthlyPayments.sumOf { it.amount }
            val monthlyPaymentCount = monthlyPayments.size
            
            // محاسبه کل بدهی‌ها
            val totalDebt = debtorCustomers.sumOf { it.totalDebt }
            
            // محاسبه ارزش کل موجودی
            val totalInventoryValue = products.sumOf { it.stock * it.purchasePrice }
            
            // محاسبه سود ماهانه (تخمینی)
            val monthlyProfit = monthlyInvoices.sumOf { invoice ->
                invoice.items.sumOf { item ->
                    // فرض: سود = (قیمت فروش - قیمت خرید) * تعداد
                    // اینجا فقط تخمین زده می‌شود
                    (item.unitPrice * 0.3).toLong() * item.quantity
                }
            }
            
            DashboardStats(
                totalCustomers = customers.size,
                totalProducts = products.size,
                monthlySales = monthlySales,
                monthlyInvoices = monthlyInvoiceCount,
                monthlyPayments = monthlyPayments,
                monthlyPaymentCount = monthlyPaymentCount,
                totalDebt = totalDebt,
                debtorCustomersCount = debtorCustomers.size,
                lowStockProductsCount = lowStockProducts.size,
                totalInventoryValue = totalInventoryValue,
                monthlyProfit = monthlyProfit,
                averageInvoiceAmount = if (monthlyInvoiceCount > 0) monthlySales / monthlyInvoiceCount else 0,
                averagePaymentAmount = if (monthlyPaymentCount > 0) monthlyPayments / monthlyPaymentCount else 0
            )
        }
    }
}
