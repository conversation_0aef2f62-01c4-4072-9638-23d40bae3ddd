package com.example.sharen.ui.dashboard

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Transaction
import com.example.sharen.data.model.TransactionStatus
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.Date
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class DashboardViewModel @Inject constructor(
    // TODO: Inject repositories here
) : ViewModel() {

    // Stats
    private val _totalSales = MutableLiveData<Long>(0)
    val totalSales: LiveData<Long> = _totalSales

    private val _totalCustomers = MutableLiveData<Int>(0)
    val totalCustomers: LiveData<Int> = _totalCustomers

    private val _totalProducts = MutableLiveData<Int>(0)
    val totalProducts: LiveData<Int> = _totalProducts

    private val _totalInvoices = MutableLiveData<Int>(0)
    val totalInvoices: LiveData<Int> = _totalInvoices

    // Recent Transactions
    private val _recentTransactions = MutableLiveData<List<Transaction>>(emptyList())
    val recentTransactions: LiveData<List<Transaction>> = _recentTransactions

    // Loading state
    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    init {
        loadDashboardData()
    }

    fun loadDashboardData() {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                // TODO: Replace with actual repository calls
                loadStats()
                loadRecentTransactions()
            } catch (e: Exception) {
                // Handle error
            } finally {
                _isLoading.value = false
            }
        }
    }

    private suspend fun loadStats() {
        // TODO: Replace with actual repository calls
        _totalSales.value = 12500000
        _totalCustomers.value = 48
        _totalProducts.value = 120
        _totalInvoices.value = 65
    }

    private suspend fun loadRecentTransactions() {
        // TODO: Replace with actual repository calls
        // For now, we'll use mock data
        val mockTransactions = listOf(
            Transaction(
                id = UUID.randomUUID().toString(),
                customerId = "cust-001",
                orderId = "INV-1001", // Assuming invoiceNumber maps to orderId
                amount = 3500000,
                type = TransactionType.SALE, // Assuming PAID status means a SALE transaction
                createdAt = Date()
            ),
            Transaction(
                id = UUID.randomUUID().toString(),
                customerId = "cust-002",
                orderId = "INV-1002",
                amount = 1800000,
                type = TransactionType.PAYMENT, // Assuming PENDING status means a PAYMENT transaction
                createdAt = Date(System.currentTimeMillis() - 86400000) // yesterday
            ),
            Transaction(
                id = UUID.randomUUID().toString(),
                customerId = "cust-003",
                orderId = "INV-1003",
                amount = 5200000,
                type = TransactionType.SALE, // Assuming PARTIAL status means a SALE transaction
                createdAt = Date(System.currentTimeMillis() - 172800000) // 2 days ago
            ),
        )
        
        _recentTransactions.value = mockTransactions
    }
}
