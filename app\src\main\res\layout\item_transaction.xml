<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <TextView
            android:id="@+id/tv_transaction_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="INV-12345" />

        <TextView
            android:id="@+id/tv_transaction_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="1402/08/12" />

        <TextView
            android:id="@+id/tv_customer_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_transaction_number"
            tools:text="علی محمدی" />

        <TextView
            android:id="@+id/tv_transaction_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/success"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_transaction_date"
            tools:text="۱۲,۰۰۰,۰۰۰ تومان" />

        <TextView
            android:id="@+id/tv_transaction_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_status_paid"
            android:paddingStart="8dp"
            android:paddingTop="2dp"
            android:paddingEnd="8dp"
            android:paddingBottom="2dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_customer_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_customer_name"
            android:layout_marginTop="8dp"
            tools:text="پرداخت شده" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 