package com.example.sharen.ui.profile

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.bumptech.glide.Glide
import com.example.sharen.R
import com.example.sharen.databinding.ActivityProfileBinding
import com.example.sharen.ui.auth.LoginActivity
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@AndroidEntryPoint
class ProfileActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProfileBinding
    private val viewModel: ProfileViewModel by viewModels()
    private lateinit var dateFormatter: SimpleDateFormat
    
    private val pickImageLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                updateProfilePicture(uri)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProfileBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa", "IR"))
        
        setupToolbar()
        setupUI()
        setupObservers()
        
        viewModel.loadUserProfile()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.profile)
    }
    
    private fun setupUI() {
        // تنظیم دکمه تغییر تصویر پروفایل
        binding.ivProfileImage.setOnClickListener {
            pickProfileImage()
        }
        
        // تنظیم دکمه‌های ویرایش
        binding.btnEditName.setOnClickListener {
            showEditNameDialog()
        }
        
        binding.btnEditPhone.setOnClickListener {
            showEditPhoneDialog()
        }
        
        binding.btnChangePassword.setOnClickListener {
            navigateToChangePassword()
        }
        
        binding.btnNotificationSettings.setOnClickListener {
            navigateToNotificationSettings()
        }
        
        binding.btnDisplaySettings.setOnClickListener {
            navigateToDisplaySettings()
        }
        
        binding.btnLogout.setOnClickListener {
            showLogoutConfirmation()
        }
    }
    
    private fun setupObservers() {
        viewModel.user.observe(this) { user ->
            user?.let {
                // نمایش اطلاعات کاربر
                binding.tvName.text = it.name
                binding.tvEmail.text = it.email
                binding.tvPhone.text = it.phone
                binding.tvRole.text = it.role
                binding.tvJoinDate.text = dateFormatter.format(Date(it.createdAt))
                
                // نمایش تصویر پروفایل
                it.imageUrl?.let { imageUrl ->
                    Glide.with(this)
                        .load(imageUrl)
                        .placeholder(R.drawable.placeholder_profile)
                        .error(R.drawable.placeholder_profile)
                        .circleCrop()
                        .into(binding.ivProfileImage)
                }
            }
        }
        
        viewModel.error.observe(this) { error ->
            Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
        }
        
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        }
    }
    
    private fun pickProfileImage() {
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "image/*"
        }
        pickImageLauncher.launch(Intent.createChooser(intent, getString(R.string.select_image)))
    }
    
    private fun updateProfilePicture(uri: Uri) {
        viewModel.updateProfilePicture(uri)
    }
    
    private fun showEditNameDialog() {
        val editText = android.widget.EditText(this).apply {
            setText(viewModel.user.value?.name)
            hint = getString(R.string.enter_name)
        }
        
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.edit_name))
            .setView(editText)
            .setPositiveButton(getString(R.string.save)) { _, _ ->
                val newName = editText.text.toString().trim()
                if (newName.isNotEmpty()) {
                    viewModel.updateUserName(newName)
                }
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    private fun showEditPhoneDialog() {
        val editText = android.widget.EditText(this).apply {
            setText(viewModel.user.value?.phone)
            hint = getString(R.string.enter_phone)
            inputType = android.text.InputType.TYPE_CLASS_PHONE
        }
        
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.edit_phone))
            .setView(editText)
            .setPositiveButton(getString(R.string.save)) { _, _ ->
                val newPhone = editText.text.toString().trim()
                if (newPhone.isNotEmpty()) {
                    viewModel.updateUserPhone(newPhone)
                }
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    private fun navigateToChangePassword() {
        // انتقال به صفحه تغییر رمز عبور
        startActivity(Intent(this, PasswordChangeActivity::class.java))
    }
    
    private fun navigateToNotificationSettings() {
        // انتقال به صفحه تنظیمات اعلان‌ها
        startActivity(Intent(this, NotificationSettingsActivity::class.java))
    }
    
    private fun navigateToDisplaySettings() {
        // انتقال به صفحه تنظیمات نمایش
        startActivity(Intent(this, DisplaySettingsActivity::class.java))
    }
    
    private fun showLogoutConfirmation() {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.logout_confirmation))
            .setMessage(getString(R.string.logout_confirmation_message))
            .setPositiveButton(getString(R.string.yes)) { _, _ ->
                logout()
            }
            .setNegativeButton(getString(R.string.no), null)
            .show()
    }
    
    private fun logout() {
        viewModel.logout()
        // انتقال به صفحه ورود
        startActivity(Intent(this, LoginActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        })
        finish()
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 