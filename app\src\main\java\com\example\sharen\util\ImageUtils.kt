package com.example.sharen.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.widget.ImageView
import androidx.activity.result.ActivityResultLauncher
import androidx.core.content.FileProvider
import com.bumptech.glide.Glide
import com.example.sharen.R
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.math.max
import kotlin.math.min

/**
 * کلاس کمکی برای مدیریت تصاویر
 */
object ImageUtils {

    private const val MAX_IMAGE_DIMENSION = 1200
    private const val DEFAULT_COMPRESSION_QUALITY = 80

    // بارگذاری تصویر با استفاده از Glide
    fun loadImage(context: Context, imageUrl: String?, imageView: ImageView) {
        if (imageUrl.isNullOrEmpty()) {
            Glide.with(context)
                .load(R.drawable.ic_product_placeholder) // یک تصویر پیش‌فرض
                .centerCrop()
                .into(imageView)
        } else {
            Glide.with(context)
                .load(imageUrl)
                .placeholder(R.drawable.ic_product_placeholder)
                .error(R.drawable.ic_product_placeholder)
                .centerCrop()
                .into(imageView)
        }
    }

    // ایجاد فایل تصویر موقت
    @Throws(IOException::class)
    fun createImageFile(context: Context): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "JPEG_${timeStamp}_"
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            imageFileName,
            ".jpg",
            storageDir
        )
    }

    // ایجاد Intent برای گرفتن عکس از دوربین
    fun getCameraIntent(context: Context, photoFile: File): Intent {
        return Intent(MediaStore.ACTION_IMAGE_CAPTURE).also { takePictureIntent ->
            takePictureIntent.resolveActivity(context.packageManager)?.also {
                val photoURI = FileProvider.getUriForFile(
                    context,
                    "com.example.sharen.fileprovider",
                    photoFile
                )
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
            }
        }
    }

    // ایجاد Intent برای انتخاب تصویر از گالری
    fun getGalleryIntent(): Intent {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        intent.type = "image/*"
        return intent
    }

    // نمایش دیالوگ انتخاب تصویر
    fun showImagePickerDialog(
        context: Context,
        onCameraSelected: () -> Unit,
        onGallerySelected: () -> Unit
    ) {
        val options = arrayOf(
            context.getString(R.string.camera),
            context.getString(R.string.gallery)
        )
        android.app.AlertDialog.Builder(context)
            .setTitle(context.getString(R.string.image_select_source))
            .setItems(options) { _, which ->
                when (which) {
                    0 -> onCameraSelected()
                    1 -> onGallerySelected()
                }
            }
            .show()
    }

    // نمایش دیالوگ حذف تصویر
    fun showRemoveImageDialog(
        context: Context,
        onRemoveConfirmed: () -> Unit
    ) {
        android.app.AlertDialog.Builder(context)
            .setTitle(context.getString(R.string.remove_image))
            .setMessage(context.getString(R.string.long_press_to_remove))
            .setPositiveButton(context.getString(android.R.string.yes)) { _, _ -> onRemoveConfirmed() }
            .setNegativeButton(context.getString(android.R.string.no), null)
            .show()
    }

    // تبدیل Uri به مسیر فایل
    fun getPathFromUri(context: Context, uri: Uri): String? {
        val projection = arrayOf(MediaStore.Images.Media.DATA)
        val cursor = context.contentResolver.query(uri, projection, null, null, null)
        cursor?.use {
            val columnIndex = it.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
            it.moveToFirst()
            return it.getString(columnIndex)
        }
        return uri.path
    }
    
    // فشرده‌سازی تصویر
    fun compressImage(context: Context, imageUri: Uri, quality: Int = DEFAULT_COMPRESSION_QUALITY): Uri? {
        try {
            // تبدیل Uri به Bitmap
            val inputStream = context.contentResolver.openInputStream(imageUri) ?: return null
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()
            
            // تغییر اندازه تصویر در صورت نیاز
            val resizedBitmap = resizeBitmap(originalBitmap)
            
            // فشرده‌سازی تصویر
            val outputStream = ByteArrayOutputStream()
            resizedBitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
            
            // ذخیره تصویر فشرده‌سازی شده
            val compressedFile = createImageFile(context)
            val fileOutputStream = FileOutputStream(compressedFile)
            fileOutputStream.write(outputStream.toByteArray())
            fileOutputStream.close()
            
            // ایجاد Uri جدید برای فایل فشرده‌سازی شده
            return FileProvider.getUriForFile(
                context,
                "com.example.sharen.fileprovider",
                compressedFile
            )
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    // تغییر اندازه تصویر به ابعاد مناسب
    private fun resizeBitmap(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        // اگر تصویر کوچکتر از ابعاد حداکثر است، آن را بدون تغییر برگردان
        if (width <= MAX_IMAGE_DIMENSION && height <= MAX_IMAGE_DIMENSION) {
            return bitmap
        }
        
        // محاسبه نسبت ابعاد جدید
        val ratio = min(
            MAX_IMAGE_DIMENSION.toFloat() / width.toFloat(),
            MAX_IMAGE_DIMENSION.toFloat() / height.toFloat()
        )
        
        val newWidth = (width * ratio).toInt()
        val newHeight = (height * ratio).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
} 