package com.example.sharen.domain.usecase.auth

import com.example.sharen.domain.model.User
import com.example.sharen.domain.repository.AuthRepository
import javax.inject.Inject

/**
 * Use Case برای ورود کاربر
 */
class LoginUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {

    suspend operator fun invoke(email: String, password: String): Result<User> {
        // اعتبارسنجی ورودی‌ها
        if (email.isBlank()) {
            return Result.failure(Exception("ایمیل نمی‌تواند خالی باشد"))
        }

        if (password.isBlank()) {
            return Result.failure(Exception("رمز عبور نمی‌تواند خالی باشد"))
        }

        if (!isValidEmail(email)) {
            return Result.failure(Exception("فرمت ایمیل صحیح نیست"))
        }

        if (password.length < 6) {
            return Result.failure(Exception("رمز عبور باید حداقل 6 کاراکتر باشد"))
        }

        // انجام ورود
        return try {
            authRepository.login(email, password)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
