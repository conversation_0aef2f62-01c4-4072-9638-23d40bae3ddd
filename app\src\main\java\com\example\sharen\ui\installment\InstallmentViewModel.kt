package com.example.sharen.ui.installment

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.R
import com.example.sharen.data.model.Installment
import com.example.sharen.data.model.InstallmentStatus
import com.example.sharen.data.repository.InstallmentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class InstallmentViewModel @Inject constructor(
    private val repository: InstallmentRepository
) : ViewModel() {

    private val _installments = MutableStateFlow<List<Installment>>(emptyList())
    val installments: StateFlow<List<Installment>> = _installments.asStateFlow()

    private val _loading = MutableStateFlow(false)
    val loading: StateFlow<Boolean> = _loading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        loadInstallments()
    }

    fun loadInstallments() {
        viewModelScope.launch {
            _loading.value = true
            try {
                val result = repository.getInstallments()
                _installments.value = result
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }

    fun getInstallment(id: Long) {
        viewModelScope.launch {
            _loading.value = true
            try {
                val result = repository.getInstallment(id)
                _installments.update { currentList ->
                    val index = currentList.indexOfFirst { it.id == id }
                    if (index != -1) {
                        currentList.toMutableList().apply {
                            set(index, result)
                        }
                    } else {
                        currentList + result
                    }
                }
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }

    fun createInstallment(installment: Installment) {
        viewModelScope.launch {
            _loading.value = true
            try {
                val result = repository.createInstallment(installment)
                _installments.update { it + result }
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }

    fun updateInstallment(installment: Installment) {
        viewModelScope.launch {
            _loading.value = true
            try {
                val result = repository.updateInstallment(installment)
                _installments.update { currentList ->
                    currentList.map { if (it.id == result.id) result else it }
                }
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }

    fun deleteInstallment(id: Long) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.deleteInstallment(id)
                _installments.update { it.filter { installment -> installment.id != id } }
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }

    fun payInstallment(id: Long, amount: Double) {
        viewModelScope.launch {
            _loading.value = true
            try {
                val result = repository.payInstallment(id, amount)
                _installments.update { currentList ->
                    currentList.map { if (it.id == result.id) result else it }
                }
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }

    fun sendReminder(id: Long) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.sendReminder(id)
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }

    fun getInstallmentsByCustomer(customerId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.getInstallmentsByCustomer(customerId)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { installments ->
                        _installments.value = installments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getInstallmentsByDateRange(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.getInstallmentsByDateRange(startDate, endDate)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { installments ->
                        _installments.value = installments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getInstallmentsByStatus(status: InstallmentStatus) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.getInstallmentsByStatus(status)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { installments ->
                        _installments.value = installments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getUpcomingInstallments(customerId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.getUpcomingInstallments(customerId)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { installments ->
                        _installments.value = installments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getOverdueInstallments() {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.getOverdueInstallments()
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { installments ->
                        _installments.value = installments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getInstallmentStatistics(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.getInstallmentStatistics(startDate, endDate)
                    .onSuccess { statistics ->
                        // Handle statistics
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun calculateRemainingAmount(installmentId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.calculateRemainingAmount(installmentId)
                    .onSuccess { amount ->
                        // Handle remaining amount
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getCustomerInstallmentHistory(customerId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.getCustomerInstallmentHistory(customerId)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { installments ->
                        _installments.value = installments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateInstallmentSchedule(
        totalAmount: Double,
        numberOfInstallments: Int,
        startDate: Date,
        interestRate: Double
    ) {
        viewModelScope.launch {
            _loading.value = true
            try {
                repository.generateInstallmentSchedule(
                    totalAmount,
                    numberOfInstallments,
                    startDate,
                    interestRate
                ).onSuccess { schedule ->
                    // Handle installment schedule
                }.onFailure { e ->
                    _error.value = e.message
                }
            } finally {
                _loading.value = false
            }
        }
    }

    fun clearError() {
        _error.value = null
    }
} 