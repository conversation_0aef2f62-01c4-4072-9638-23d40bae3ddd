package com.example.sharen.presentation.ui.auth

import android.content.Intent
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.sharen.core.base.BaseActivity
import com.example.sharen.core.utils.gone
import com.example.sharen.core.utils.visible
import com.example.sharen.databinding.ActivityForgotPasswordBinding
import com.example.sharen.presentation.viewmodel.AuthViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * صفحه فراموشی رمز عبور
 */
@AndroidEntryPoint
class ForgotPasswordActivity : BaseActivity<ActivityForgotPasswordBinding>() {

    private val authViewModel: AuthViewModel by viewModels()

    override fun getViewBinding(): ActivityForgotPasswordBinding {
        return ActivityForgotPasswordBinding.inflate(layoutInflater)
    }

    override fun setupViews() {
        setupClickListeners()
        setupUI()
    }

    override fun observeData() {
        observeLoadingState()
        observeMessages()
    }

    private fun setupClickListeners() {
        binding.apply {
            btnSendResetLink.setOnClickListener {
                sendResetLink()
            }

            tvBackToLogin.setOnClickListener {
                navigateToLogin()
            }

            ivBack.setOnClickListener {
                finish()
            }
        }
    }

    private fun setupUI() {
        binding.apply {
            // فوکوس روی فیلد ایمیل
            etEmail.requestFocus()
        }
    }

    private fun observeLoadingState() {
        lifecycleScope.launch {
            authViewModel.isLoading.collect { isLoading ->
                if (isLoading) {
                    binding.progressBar.visible()
                    binding.btnSendResetLink.isEnabled = false
                } else {
                    binding.progressBar.gone()
                    binding.btnSendResetLink.isEnabled = true
                }
            }
        }
    }

    private fun observeMessages() {
        lifecycleScope.launch {
            authViewModel.error.collect { error ->
                error?.let {
                    showError(it)
                    authViewModel.clearError()
                }
            }
        }

        lifecycleScope.launch {
            authViewModel.success.collect { success ->
                success?.let {
                    showSuccess(it)
                    authViewModel.clearSuccess()
                    // بعد از موفقیت، به صفحه ورود برگردیم
                    kotlinx.coroutines.delay(2000)
                    navigateToLogin()
                }
            }
        }
    }

    private fun sendResetLink() {
        val email = binding.etEmail.text.toString().trim()

        // اعتبارسنجی
        if (email.isEmpty()) {
            binding.etEmail.error = "ایمیل را وارد کنید"
            return
        }

        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.error = "ایمیل معتبر نیست"
            return
        }

        // ارسال لینک بازیابی
        authViewModel.resetPassword(email)
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        startActivity(intent)
        finish()
    }

    override fun showLoading() {
        binding.progressBar.visible()
        binding.btnSendResetLink.isEnabled = false
    }

    override fun hideLoading() {
        binding.progressBar.gone()
        binding.btnSendResetLink.isEnabled = true
    }
}
