package com.example.sharen.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Domain Model برای سفارش
 */
@Parcelize
data class Order(
    val id: String,
    val customerId: String,
    val sellerId: String,
    val orderNumber: String,
    val totalAmount: Double,
    val status: OrderStatus = OrderStatus.PENDING,
    val orderDate: Date = Date(),
    val deliveryDate: Date? = null,
    val notes: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) : Parcelable

/**
 * وضعیت سفارش
 */
enum class OrderStatus(val displayName: String) {
    PENDING("در انتظار"),
    CONFIRMED("تأیید شده"),
    PROCESSING("در حال پردازش"),
    SHIPPED("ارسال شده"),
    DELIVERED("تحویل داده شده"),
    CANCELLED("لغو شده"),
    RETURNED("مرجوع شده")
}
