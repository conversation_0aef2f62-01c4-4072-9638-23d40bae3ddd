package com.example.sharen.ui.profile

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.example.sharen.databinding.ActivityDisplaySettingsBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DisplaySettingsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDisplaySettingsBinding
    private val viewModel: DisplaySettingsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDisplaySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupUI()
        setupObservers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.display_settings)
    }

    private fun setupUI() {
        // TODO: Implement display settings UI
    }

    private fun setupObservers() {
        // TODO: Implement observers for display settings
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 