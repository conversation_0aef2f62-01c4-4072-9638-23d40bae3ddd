package com.example.sharen.ui.admin

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.User
import com.example.sharen.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class UserManagementViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    private val _users = MutableLiveData<List<User>>()
    val users: LiveData<List<User>> = _users
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    private val _operationSuccess = MutableLiveData<String>()
    val operationSuccess: LiveData<String> = _operationSuccess
    
    private var allUsers: List<User> = listOf()
    private var currentSearchQuery: String = ""
    private var currentRoleFilter: String? = null

    fun loadUsers() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userRepository.getAllUsers().collect { userList ->
                    allUsers = userList
                    applyFilters()
                    _isLoading.value = false
                }
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در بارگذاری لیست کاربران"
                _isLoading.value = false
            }
        }
    }
    
    fun searchUsers(query: String) {
        currentSearchQuery = query
        applyFilters()
    }
    
    fun filterByRole(role: String?) {
        currentRoleFilter = role
        applyFilters()
    }
    
    private fun applyFilters() {
        var filteredUsers = allUsers
        
        // اعمال فیلتر نقش
        if (currentRoleFilter != null) {
            filteredUsers = filteredUsers.filter { it.role == currentRoleFilter }
        }
        
        // اعمال جستجو
        if (currentSearchQuery.isNotBlank()) {
            filteredUsers = filteredUsers.filter {
                it.name.contains(currentSearchQuery, ignoreCase = true) ||
                it.email.contains(currentSearchQuery, ignoreCase = true) ||
                it.phone.contains(currentSearchQuery, ignoreCase = true)
            }
        }
        
        _users.value = filteredUsers
    }
    
    fun approveUser(userId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = userRepository.approveUser(userId)
                if (result.isSuccess) {
                    _operationSuccess.value = "کاربر با موفقیت تأیید شد"
                    loadUsers()
                } else {
                    _error.value = "خطا در تأیید کاربر"
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در تأیید کاربر"
                _isLoading.value = false
            }
        }
    }
    
    fun deleteUser(userId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = userRepository.deleteUser(userId)
                if (result.isSuccess) {
                    _operationSuccess.value = "کاربر با موفقیت حذف شد"
                    loadUsers()
                } else {
                    _error.value = "خطا در حذف کاربر"
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در حذف کاربر"
                _isLoading.value = false
            }
        }
    }
    
    fun changeUserRole(userId: String, role: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = userRepository.changeUserRole(userId, role)
                if (result.isSuccess) {
                    _operationSuccess.value = "نقش کاربر با موفقیت تغییر کرد"
                    loadUsers()
                } else {
                    _error.value = "خطا در تغییر نقش کاربر"
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در تغییر نقش کاربر"
                _isLoading.value = false
            }
        }
    }
    
    fun createUser() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = userRepository.createUser(
                    name = "",
                    email = "",
                    phone = "",
                    role = "USER"
                )
                if (result.isSuccess) {
                    _operationSuccess.value = "کاربر با موفقیت ایجاد شد"
                    loadUsers()
                } else {
                    _error.value = "خطا در ایجاد کاربر"
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در ایجاد کاربر"
                _isLoading.value = false
            }
        }
    }
} 