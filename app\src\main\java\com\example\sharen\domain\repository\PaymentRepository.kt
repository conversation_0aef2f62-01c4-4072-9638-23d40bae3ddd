package com.example.sharen.domain.repository

import com.example.sharen.domain.model.Payment
import com.example.sharen.domain.model.PaymentMethod
import com.example.sharen.domain.model.PaymentStatus
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Repository Interface برای مدیریت پرداخت‌ها
 */
interface PaymentRepository {
    
    /**
     * دریافت تمام پرداخت‌ها
     */
    fun getAllPayments(): Flow<List<Payment>>
    
    /**
     * دریافت پرداخت با شناسه
     */
    suspend fun getPaymentById(paymentId: String): Result<Payment?>
    
    /**
     * دریافت پرداخت‌های مشتری
     */
    fun getPaymentsByCustomer(customerId: String): Flow<List<Payment>>
    
    /**
     * دریافت پرداخت‌های فاکتور
     */
    fun getPaymentsByInvoice(invoiceId: String): Flow<List<Payment>>
    
    /**
     * دریافت پرداخت‌ها در بازه زمانی
     */
    fun getPaymentsByDateRange(startDate: Date, endDate: Date): Flow<List<Payment>>
    
    /**
     * دریافت پرداخت‌ها بر اساس روش پرداخت
     */
    fun getPaymentsByMethod(method: PaymentMethod): Flow<List<Payment>>
    
    /**
     * دریافت پرداخت‌ها بر اساس وضعیت
     */
    fun getPaymentsByStatus(status: PaymentStatus): Flow<List<Payment>>
    
    /**
     * افزودن پرداخت جدید
     */
    suspend fun addPayment(payment: Payment): Result<Payment>
    
    /**
     * بروزرسانی پرداخت
     */
    suspend fun updatePayment(payment: Payment): Result<Payment>
    
    /**
     * حذف پرداخت
     */
    suspend fun deletePayment(paymentId: String): Result<Unit>
    
    /**
     * تأیید پرداخت
     */
    suspend fun confirmPayment(paymentId: String): Result<Payment>
    
    /**
     * رد پرداخت
     */
    suspend fun rejectPayment(paymentId: String, reason: String): Result<Payment>
    
    /**
     * دریافت مجموع پرداخت‌های مشتری
     */
    suspend fun getTotalPaymentsByCustomer(customerId: String): Result<Long>
    
    /**
     * دریافت مجموع پرداخت‌ها در بازه زمانی
     */
    suspend fun getTotalPaymentsByDateRange(startDate: Date, endDate: Date): Result<Long>
    
    /**
     * دریافت آمار پرداخت‌ها
     */
    suspend fun getPaymentStatistics(startDate: Date, endDate: Date): Result<Map<String, Any>>
    
    /**
     * دریافت پرداخت‌های اخیر
     */
    fun getRecentPayments(limit: Int = 10): Flow<List<Payment>>
}
