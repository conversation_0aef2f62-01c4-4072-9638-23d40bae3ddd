package com.example.sharen.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.sharen.data.local.entity.PaymentEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface PaymentDao {
    @Query("SELECT * FROM payments ORDER BY createdAt DESC")
    fun getAllPayments(): Flow<List<PaymentEntity>>

    @Query("SELECT * FROM payments WHERE id = :paymentId")
    suspend fun getPaymentById(paymentId: String): PaymentEntity?

    @Query("SELECT * FROM payments WHERE customerId = :customerId ORDER BY createdAt DESC")
    fun getPaymentsByCustomer(customerId: String): Flow<List<PaymentEntity>>

    @Query("SELECT * FROM payments WHERE invoiceId = :invoiceId ORDER BY createdAt DESC")
    fun getPaymentsByInvoice(invoiceId: String): Flow<List<PaymentEntity>>

    @Query("SELECT * FROM payments WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    fun getPaymentsByDateRange(startDate: Long, endDate: Long): Flow<List<PaymentEntity>>

    @Query("SELECT * FROM payments WHERE method = :method ORDER BY createdAt DESC")
    fun getPaymentsByMethod(method: String): Flow<List<PaymentEntity>>

    @Query("SELECT * FROM payments WHERE status = :status ORDER BY createdAt DESC")
    fun getPaymentsByStatus(status: String): Flow<List<PaymentEntity>>

    @Query("SELECT SUM(amount) FROM payments WHERE customerId = :customerId")
    suspend fun getTotalPaymentsByCustomer(customerId: String): Long?

    @Query("SELECT SUM(amount) FROM payments WHERE createdAt BETWEEN :startDate AND :endDate")
    suspend fun getTotalPaymentsByDateRange(startDate: Long, endDate: Long): Long?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPayment(payment: PaymentEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPayments(payments: List<PaymentEntity>)

    @Update
    suspend fun updatePayment(payment: PaymentEntity)

    @Delete
    suspend fun deletePayment(payment: PaymentEntity)

    @Query("DELETE FROM payments")
    suspend fun deleteAllPayments()
}