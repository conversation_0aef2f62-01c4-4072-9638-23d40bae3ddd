package com.example.sharen.ui.product

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Product
import com.example.sharen.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProductDetailsViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {

    // اطلاعات محصول
    private val _product = MutableLiveData<Product?>()
    val product: LiveData<Product?> = _product

    // وضعیت بارگذاری
    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    // پیام خطا
    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    fun loadProduct(productId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            productRepository.getProductById(productId)
                .catch { e ->
                    _error.postValue(e.message ?: "خطا در بارگذاری اطلاعات محصول")
                    _isLoading.postValue(false)
                }
                .collectLatest { product ->
                    _product.postValue(product)
                    _isLoading.postValue(false)
                }
        }
    }

    fun updateStock(productId: String, newStock: Int, onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            productRepository.updateProductStock(productId, newStock)
                .onSuccess { updatedProduct ->
                    _product.value = updatedProduct
                    _isLoading.value = false
                    onSuccess()
                }
                .onFailure { e ->
                    _error.value = e.message ?: "خطا در بروزرسانی موجودی محصول"
                    _isLoading.value = false
                }
        }
    }

    fun deleteProduct(productId: String, onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            productRepository.deleteProduct(productId)
                .onSuccess {
                    _isLoading.value = false
                    onSuccess()
                }
                .onFailure { e ->
                    _error.value = e.message ?: "خطا در حذف محصول"
                    _isLoading.value = false
                }
        }
    }
} 