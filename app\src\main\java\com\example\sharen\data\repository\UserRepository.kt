package com.example.sharen.data.repository

import com.example.sharen.domain.model.User
import kotlinx.coroutines.flow.Flow

/**
 * Repository Interface برای مدیریت کاربران
 */
interface UserRepository {
    
    /**
     * دریافت تمام کاربران
     */
    fun getAllUsers(): Flow<List<User>>
    
    /**
     * دریافت کاربر با شناسه
     */
    suspend fun getUserById(userId: String): Result<User?>
    
    /**
     * دریافت کاربر فعلی
     */
    fun getCurrentUser(): Flow<User?>
    
    /**
     * دریافت شناسه کاربر فعلی
     */
    suspend fun getCurrentUserId(): String?
    
    /**
     * افزودن کاربر جدید
     */
    suspend fun addUser(user: User): Result<User>
    
    /**
     * بروزرسانی کاربر
     */
    suspend fun updateUser(user: User): Result<User>
    
    /**
     * حذف کاربر
     */
    suspend fun deleteUser(userId: String): Result<Unit>
    
    /**
     * جستجوی کاربران
     */
    fun searchUsers(query: String): Flow<List<User>>
}
