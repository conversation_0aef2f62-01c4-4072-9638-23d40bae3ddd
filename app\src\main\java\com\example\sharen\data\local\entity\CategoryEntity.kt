package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.data.model.Category
import java.util.Date

@Entity(
    tableName = "categories",
    foreignKeys = [
        ForeignKey(
            entity = CategoryEntity::class,
            parentColumns = ["id"],
            childColumns = ["parentId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index("parentId")]
)
data class CategoryEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String?,
    val parentId: String?,
    val imageUrl: String?,
    val sortOrder: Int,
    val isActive: Boolean,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toCategory(): Category = Category(
        id = id,
        name = name,
        description = description,
        parentId = parentId,
        imageUrl = imageUrl,
        sortOrder = sortOrder,
        isActive = isActive,
        createdAt = Date(this.createdAt),
        updatedAt = Date(this.updatedAt)
    )

    companion object {
        fun fromCategory(category: Category): CategoryEntity = CategoryEntity(
            id = category.id,
            name = category.name,
            description = category.description,
            parentId = category.parentId,
            imageUrl = category.imageUrl,
            sortOrder = category.sortOrder,
            isActive = category.isActive,
            createdAt = category.createdAt.time,
            updatedAt = category.updatedAt.time
        )
    }
}