package com.example.sharen.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.example.sharen.core.base.BaseViewModel
import com.example.sharen.domain.model.User
import com.example.sharen.domain.model.UserRole
import com.example.sharen.domain.usecase.auth.LoginUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای احراز هویت
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase
) : BaseViewModel() {

    private val _loginState = MutableStateFlow<LoginState>(LoginState.Idle)
    val loginState: StateFlow<LoginState> = _loginState.asStateFlow()

    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser.asStateFlow()

    /**
     * ورود کاربر
     */
    fun login(email: String, password: String) {
        launchWithLoading {
            _loginState.value = LoginState.Loading
            
            val result = loginUseCase(email, password)
            
            result.fold(
                onSuccess = { user ->
                    _currentUser.value = user
                    _loginState.value = LoginState.Success(user)
                    showSuccess("خوش آمدید ${user.name}")
                },
                onFailure = { exception ->
                    _loginState.value = LoginState.Error(exception.message ?: "خطا در ورود")
                    showError(exception.message ?: "خطا در ورود")
                }
            )
        }
    }

    /**
     * ثبت‌نام کاربر
     */
    fun register(
        email: String,
        password: String,
        confirmPassword: String,
        name: String,
        phone: String,
        role: UserRole = UserRole.CUSTOMER,
        referrerCode: String? = null
    ) {
        launchWithLoading {
            // اعتبارسنجی
            if (password != confirmPassword) {
                showError("رمز عبور و تکرار آن یکسان نیستند")
                return@launchWithLoading
            }

            if (name.length < 2) {
                showError("نام باید حداقل 2 کاراکتر باشد")
                return@launchWithLoading
            }

            if (!isValidPhone(phone)) {
                showError("شماره موبایل صحیح نیست")
                return@launchWithLoading
            }

            _loginState.value = LoginState.Loading

            // فعلاً فقط یک کاربر نمونه ایجاد می‌کنیم
            // بعداً باید با repository واقعی جایگزین شود
            val newUser = User(
                id = java.util.UUID.randomUUID().toString(),
                email = email,
                name = name,
                phone = phone,
                role = role,
                isApproved = role == UserRole.CUSTOMER
            )

            _currentUser.value = newUser
            _loginState.value = LoginState.Success(newUser)
            showSuccess("ثبت‌نام با موفقیت انجام شد")
        }
    }

    /**
     * خروج کاربر
     */
    fun logout() {
        viewModelScope.launch {
            _currentUser.value = null
            _loginState.value = LoginState.Idle
            showSuccess("با موفقیت خارج شدید")
        }
    }

    /**
     * بازیابی رمز عبور
     */
    fun resetPassword(email: String) {
        launchWithLoading {
            if (!isValidEmail(email)) {
                showError("ایمیل صحیح نیست")
                return@launchWithLoading
            }

            // شبیه‌سازی ارسال ایمیل بازیابی
            kotlinx.coroutines.delay(2000)
            showSuccess("لینک بازیابی رمز عبور به ایمیل شما ارسال شد")
        }
    }

    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    private fun isValidPhone(phone: String): Boolean {
        return phone.matches(Regex("^09\\d{9}$"))
    }
}

/**
 * وضعیت‌های ورود
 */
sealed class LoginState {
    object Idle : LoginState()
    object Loading : LoginState()
    data class Success(val user: User) : LoginState()
    data class Error(val message: String) : LoginState()
}
