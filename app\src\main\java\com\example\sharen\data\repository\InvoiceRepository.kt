package com.example.sharen.data.repository

import com.example.sharen.domain.model.Invoice
import com.example.sharen.domain.model.InvoiceStatus
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Repository Interface برای مدیریت فاکتورها
 */
interface InvoiceRepository {
    
    /**
     * دریافت تمام فاکتورها
     */
    fun getAllInvoices(): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتور با شناسه
     */
    suspend fun getInvoiceById(invoiceId: String): Result<Invoice?>
    
    /**
     * دریافت فاکتورهای مشتری
     */
    fun getInvoicesByCustomer(customerId: String): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتورها در بازه زمانی
     */
    fun getInvoicesByDateRange(startDate: Date, endDate: Date): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتورها بر اساس وضعیت
     */
    fun getInvoicesByStatus(status: InvoiceStatus): Flow<List<Invoice>>
    
    /**
     * افزودن فاکتور جدید
     */
    suspend fun addInvoice(invoice: Invoice): Result<Invoice>
    
    /**
     * بروزرسانی فاکتور
     */
    suspend fun updateInvoice(invoice: Invoice): Result<Invoice>
    
    /**
     * حذف فاکتور
     */
    suspend fun deleteInvoice(invoiceId: String): Result<Unit>
    
    /**
     * جستجوی فاکتورها
     */
    fun searchInvoices(query: String): Flow<List<Invoice>>
    
    /**
     * دریافت مجموع فروش در بازه زمانی
     */
    suspend fun getTotalSalesByDateRange(startDate: Date, endDate: Date): Result<Long>
}
