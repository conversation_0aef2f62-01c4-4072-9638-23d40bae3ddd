package com.example.sharen.ui.invoice

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceStatus
import com.example.sharen.databinding.ItemInvoiceBinding
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale

class InvoiceAdapter(
    private val onInvoiceClick: (Invoice) -> Unit,
    private val numberFormatter: NumberFormat = NumberFormat.getInstance(Locale("fa"))
) : ListAdapter<Invoice, InvoiceAdapter.InvoiceViewHolder>(InvoiceDiffCallback()) {

    private val dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa"))

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InvoiceViewHolder {
        val binding = ItemInvoiceBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return InvoiceViewHolder(binding)
    }

    override fun onBindViewHolder(holder: InvoiceViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class InvoiceViewHolder(
        private val binding: ItemInvoiceBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onInvoiceClick(getItem(position))
                }
            }
        }

        fun bind(invoice: Invoice) {
            // اطلاعات اصلی فاکتور
            binding.tvInvoiceNumber.text = invoice.invoiceNumber
            binding.tvCustomerName.text = invoice.customerName
            binding.tvDate.text = dateFormatter.format(invoice.createdAt)
            binding.tvAmount.text = "${numberFormatter.format(invoice.finalAmount)} تومان"

            // وضعیت فاکتور
            binding.tvStatus.text = getStatusText(invoice.status)
            binding.tvStatus.setTextColor(
                ContextCompat.getColor(
                    binding.root.context,
                    getStatusColor(invoice.status)
                )
            )

            // نشان دادن پرچم پرداخت شده
            binding.ivPaid.visibility = if (invoice.isFullyPaid) View.VISIBLE else View.GONE

            // نمایش نوع پرداخت
            binding.tvPaymentType.text = getPaymentTypeText(invoice.paymentType)
        }

        private fun getStatusText(status: InvoiceStatus): String {
            return when (status) {
                InvoiceStatus.DRAFT -> "پیش‌نویس"
                InvoiceStatus.PENDING -> "در انتظار تأیید"
                InvoiceStatus.APPROVED -> "تأیید شده"
                InvoiceStatus.PAID -> "پرداخت شده"
                InvoiceStatus.PARTIALLY_PAID -> "پرداخت جزئی"
                InvoiceStatus.CANCELLED -> "لغو شده"
            }
        }

        private fun getStatusColor(status: InvoiceStatus): Int {
            return when (status) {
                InvoiceStatus.DRAFT -> R.color.gray
                InvoiceStatus.PENDING -> R.color.orange
                InvoiceStatus.APPROVED -> R.color.blue
                InvoiceStatus.PAID -> R.color.green
                InvoiceStatus.PARTIALLY_PAID -> R.color.yellow
                InvoiceStatus.CANCELLED -> R.color.red
            }
        }

        private fun getPaymentTypeText(paymentType: com.example.sharen.data.model.PaymentType): String {
            return when (paymentType) {
                com.example.sharen.data.model.PaymentType.CASH -> "نقدی"
                com.example.sharen.data.model.PaymentType.CARD -> "کارت بانکی"
                com.example.sharen.data.model.PaymentType.INSTALLMENT -> "اقساطی"
                com.example.sharen.data.model.PaymentType.MIXED -> "ترکیبی"
            }
        }
    }

    /**
     * برای مقایسه آیتم‌های لیست و بهینه‌سازی رندرینگ
     */
    class InvoiceDiffCallback : DiffUtil.ItemCallback<Invoice>() {
        override fun areItemsTheSame(oldItem: Invoice, newItem: Invoice): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Invoice, newItem: Invoice): Boolean {
            return oldItem == newItem
        }
    }
} 