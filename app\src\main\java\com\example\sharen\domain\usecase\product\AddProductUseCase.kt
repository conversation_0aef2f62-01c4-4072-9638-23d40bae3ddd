package com.example.sharen.domain.usecase.product

import com.example.sharen.domain.model.Product
import com.example.sharen.domain.model.ProductType
import com.example.sharen.domain.model.ProductSize
import com.example.sharen.domain.model.ProductColor
import com.example.sharen.domain.model.Season
import com.example.sharen.domain.model.Gender
import com.example.sharen.domain.repository.ProductRepository
import java.util.Date
import java.util.UUID
import javax.inject.Inject

/**
 * Use Case برای افزودن محصول جدید
 */
class AddProductUseCase @Inject constructor(
    private val productRepository: ProductRepository
) {
    suspend operator fun invoke(
        name: String,
        code: String,
        barcode: String? = null,
        description: String? = null,
        categoryId: String,
        brandId: String? = null,
        type: ProductType? = null,
        season: Season? = null,
        gender: Gender? = null,
        sizes: List<ProductSize> = emptyList(),
        colors: List<ProductColor> = emptyList(),
        material: String? = null,
        purchasePrice: Long,
        sellingPrice: Long,
        stock: Int,
        minimumStock: Int = 0,
        imageUrl: String? = null
    ): Result<Product> {

        // اعتبارسنجی ورودی‌ها
        if (name.isBlank()) {
            return Result.failure(IllegalArgumentException("نام محصول نمی‌تواند خالی باشد"))
        }

        if (code.isBlank()) {
            return Result.failure(IllegalArgumentException("کد محصول نمی‌تواند خالی باشد"))
        }

        if (categoryId.isBlank()) {
            return Result.failure(IllegalArgumentException("دسته‌بندی محصول نمی‌تواند خالی باشد"))
        }

        if (purchasePrice < 0) {
            return Result.failure(IllegalArgumentException("قیمت خرید نمی‌تواند منفی باشد"))
        }

        if (sellingPrice < 0) {
            return Result.failure(IllegalArgumentException("قیمت فروش نمی‌تواند منفی باشد"))
        }

        if (stock < 0) {
            return Result.failure(IllegalArgumentException("موجودی نمی‌تواند منفی باشد"))
        }

        if (minimumStock < 0) {
            return Result.failure(IllegalArgumentException("حداقل موجودی نمی‌تواند منفی باشد"))
        }

        if (sellingPrice < purchasePrice) {
            return Result.failure(IllegalArgumentException("قیمت فروش نمی‌تواند کمتر از قیمت خرید باشد"))
        }

        // ایجاد محصول جدید
        val product = Product(
            id = UUID.randomUUID().toString(),
            name = name.trim(),
            code = code.trim().uppercase(),
            barcode = barcode?.trim(),
            description = description?.trim(),
            categoryId = categoryId,
            brandId = brandId,
            purchasePrice = purchasePrice,
            sellingPrice = sellingPrice,
            stock = stock,
            minimumStock = minimumStock,
            imageUrl = imageUrl,
            isActive = true,
            createdAt = Date(),
            updatedAt = Date(),
            sizes = sizes,
            colors = colors,
            season = season,
            gender = gender,
            material = material
        )

        return productRepository.addProduct(product)
    }
}
