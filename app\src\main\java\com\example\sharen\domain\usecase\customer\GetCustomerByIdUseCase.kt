package com.example.sharen.domain.usecase.customer

import com.example.sharen.domain.model.Customer
import com.example.sharen.domain.repository.CustomerRepository
import javax.inject.Inject

/**
 * Use Case برای دریافت مشتری با شناسه
 */
class GetCustomerByIdUseCase @Inject constructor(
    private val customerRepository: CustomerRepository
) {
    suspend operator fun invoke(customerId: String): Result<Customer?> {
        return customerRepository.getCustomerById(customerId)
    }
}
