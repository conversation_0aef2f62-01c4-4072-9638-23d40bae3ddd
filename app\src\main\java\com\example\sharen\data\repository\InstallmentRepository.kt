package com.example.sharen.data.repository

import com.example.sharen.domain.model.Installment
import com.example.sharen.domain.model.InstallmentStatus
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface InstallmentRepository {
    suspend fun createInstallment(installment: Installment): Result<Installment>
    suspend fun updateInstallment(installment: Installment): Result<Installment>
    suspend fun deleteInstallment(id: String): Result<Unit>
    suspend fun getInstallment(id: String): Result<Installment?>
    fun getAllInstallments(): Flow<List<Installment>>
    fun getInstallmentsByCustomer(customerId: String): Flow<List<Installment>>
    fun getInstallmentsByDateRange(startDate: Date, endDate: Date): Flow<List<Installment>>
    fun getInstallmentsByStatus(status: InstallmentStatus): Flow<List<Installment>>
    suspend fun payInstallment(id: String, amount: Double): Result<Installment>
    fun getUpcomingInstallments(): Flow<List<Installment>>
    fun getOverdueInstallments(): Flow<List<Installment>>
    suspend fun getInstallmentStatistics(startDate: Date, endDate: Date): Map<String, Double>
    suspend fun calculateRemainingAmount(id: String): Double
    fun getCustomerInstallmentHistory(customerId: String): Flow<List<Installment>>
    suspend fun generateInstallmentSchedule(
        totalAmount: Double,
        numberOfInstallments: Int,
        startDate: Date,
        intervalInDays: Int
    ): List<Installment>
}