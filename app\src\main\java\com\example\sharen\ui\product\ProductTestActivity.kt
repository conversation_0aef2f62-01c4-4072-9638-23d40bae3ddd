package com.example.sharen.ui.product

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.sharen.R
import com.example.sharen.databinding.ActivityProductTestBinding
import dagger.hilt.android.AndroidEntryPoint

/**
 * فعالیت تست برای آزمودن قابلیت آپلود تصویر محصول
 * این فعالیت فقط برای آزمایش ساخته شده و در نسخه نهایی استفاده نمی‌شود
 */
@AndroidEntryPoint
class ProductTestActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityProductTestBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupButtons()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "تست آپلود تصویر محصول"
    }
    
    private fun setupButtons() {
        // دکمه ایجاد محصول جدید
        binding.btnCreateProduct.setOnClickListener {
            val intent = Intent(this, ProductFormActivity::class.java)
            startActivity(intent)
        }
        
        // دکمه نمایش لیست محصولات
        binding.btnShowProducts.setOnClickListener {
            val intent = Intent(this, ProductListActivity::class.java)
            startActivity(intent)
        }
        
        // دکمه اطلاعات
        binding.btnInfo.setOnClickListener {
            Toast.makeText(
                this,
                "این صفحه برای تست قابلیت آپلود تصویر محصول طراحی شده است",
                Toast.LENGTH_LONG
            ).show()
        }
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 