package com.example.sharen.ui.installment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.example.sharen.R
import com.example.sharen.databinding.FragmentInstallmentEditBinding
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

@AndroidEntryPoint
class InstallmentEditFragment : Fragment() {

    private var _binding: FragmentInstallmentEditBinding? = null
    private val binding get() = _binding!!

    private val viewModel: InstallmentViewModel by viewModels()
    private val args: InstallmentEditFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentInstallmentEditBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupToolbar()
        setupListeners()
        observeViewModel()

        if (args.installmentId != 0L) {
            viewModel.getInstallment(args.installmentId)
        }
    }

    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }
    }

    private fun setupListeners() {
        binding.buttonSave.setOnClickListener {
            if (validateInputs()) {
                saveInstallment()
            }
        }
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.installments.collect { installments ->
                    val installment = installments.find { it.id == args.installmentId }
                    installment?.let { populateFields(it) }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.isLoading.collect { isLoading ->
                    binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
                    binding.buttonSave.isEnabled = !isLoading
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.error.collect { error ->
                    error?.let {
                        Snackbar.make(binding.root, it, Snackbar.LENGTH_LONG).show()
                        viewModel.clearError()
                    }
                }
            }
        }
    }

    private fun populateFields(installment: Installment) {
        binding.apply {
            editTextTotalAmount.setText(installment.totalAmount.toString())
            editTextPaidAmount.setText(installment.paidAmount.toString())
            editTextDueDate.setText(SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())
                .format(installment.dueDate))
            editTextNotes.setText(installment.notes)
            chipGroupStatus.check(
                when (installment.status) {
                    InstallmentStatus.PENDING -> R.id.chipPending
                    InstallmentStatus.PAID -> R.id.chipPaid
                    InstallmentStatus.OVERDUE -> R.id.chipOverdue
                }
            )
        }
    }

    private fun validateInputs(): Boolean {
        var isValid = true

        binding.apply {
            if (editTextTotalAmount.text.isNullOrBlank()) {
                editTextTotalAmount.error = getString(R.string.error_required)
                isValid = false
            }

            if (editTextPaidAmount.text.isNullOrBlank()) {
                editTextPaidAmount.error = getString(R.string.error_required)
                isValid = false
            }

            if (editTextDueDate.text.isNullOrBlank()) {
                editTextDueDate.error = getString(R.string.error_required)
                isValid = false
            }
        }

        return isValid
    }

    private fun saveInstallment() {
        val totalAmount = binding.editTextTotalAmount.text.toString().toDoubleOrNull() ?: 0.0
        val paidAmount = binding.editTextPaidAmount.text.toString().toDoubleOrNull() ?: 0.0
        val dueDate = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())
            .parse(binding.editTextDueDate.text.toString()) ?: Date()
        val notes = binding.editTextNotes.text.toString()
        val status = when (binding.chipGroupStatus.checkedChipId) {
            R.id.chipPending -> InstallmentStatus.PENDING
            R.id.chipPaid -> InstallmentStatus.PAID
            R.id.chipOverdue -> InstallmentStatus.OVERDUE
            else -> InstallmentStatus.PENDING
        }

        if (args.installmentId != 0L) {
            viewModel.updateInstallment(
                args.installmentId,
                totalAmount,
                paidAmount,
                dueDate,
                notes,
                status
            )
        } else {
            viewModel.createInstallment(
                args.customerId,
                totalAmount,
                paidAmount,
                dueDate,
                notes,
                status
            )
        }

        findNavController().navigateUp()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 