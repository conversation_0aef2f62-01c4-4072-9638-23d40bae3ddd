package com.example.sharen.ui.payment

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Payment
import com.example.sharen.data.repository.PaymentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PaymentListViewModel @Inject constructor(
    private val paymentRepository: PaymentRepository
) : ViewModel() {

    private val _payments = MutableStateFlow<List<Payment>>(emptyList())
    val payments: StateFlow<List<Payment>> = _payments.asStateFlow()

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        loadPayments()
    }

    fun refreshPayments() {
        loadPayments()
    }

    private fun loadPayments() {
        viewModelScope.launch {
            _isRefreshing.value = true
            _error.value = null

            paymentRepository.getPayments()
                .catch { e ->
                    _error.value = e.message
                    _isRefreshing.value = false
                }
                .collect { payments ->
                    _payments.value = payments
                    _isRefreshing.value = false
                }
        }
    }

    fun errorShown() {
        _error.value = null
    }
} 