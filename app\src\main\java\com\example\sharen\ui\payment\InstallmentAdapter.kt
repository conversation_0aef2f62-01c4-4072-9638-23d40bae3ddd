package com.example.sharen.ui.payment

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Installment
import com.example.sharen.data.model.InstallmentStatus
import com.example.sharen.databinding.ItemInstallmentBinding
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Date

class InstallmentAdapter(
    private val numberFormatter: NumberFormat,
    private val dateFormatter: SimpleDateFormat,
    private val onInstallmentClick: (Installment) -> Unit,
    private val onPayInstallmentClick: (Installment) -> Unit,
    private val onRemindInstallmentClick: (Installment) -> Unit
) : ListAdapter<Installment, InstallmentAdapter.InstallmentViewHolder>(InstallmentDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InstallmentViewHolder {
        val binding = ItemInstallmentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return InstallmentViewHolder(binding)
    }

    override fun onBindViewHolder(holder: InstallmentViewHolder, position: Int) {
        holder.bind(getItem(position), position)
    }

    inner class InstallmentViewHolder(
        private val binding: ItemInstallmentBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(installment: Installment, position: Int) {
            binding.root.setOnClickListener { onInstallmentClick(installment) }

            // شماره قسط
            binding.tvInstallmentNumber.text = binding.root.context.getString(
                R.string.installment_number, position + 1
            )

            // مبلغ
            binding.tvAmount.text = "${numberFormatter.format(installment.totalAmount)} تومان"

            // تاریخ سررسید
            binding.tvDueDate.text = dateFormatter.format(installment.dueDate)

            // وضعیت
            val (statusText, statusColor) = getStatusInfo(installment.status)
            binding.tvStatus.text = statusText
            binding.tvStatus.setBackgroundColor(
                ContextCompat.getColor(binding.root.context, statusColor)
            )

            // دکمه‌ها
            binding.btnPay.isEnabled = installment.status != InstallmentStatus.PAID
            binding.btnPay.setOnClickListener { onPayInstallmentClick(installment) }

            val currentDate = Date()
            val isDueSoon = installment.dueDate.time - currentDate.time <= 7 * 24 * 60 * 60 * 1000 // 7 روز

            binding.btnRemind.isEnabled = (installment.status == InstallmentStatus.PENDING ||
                                          installment.status == InstallmentStatus.OVERDUE) && isDueSoon
            binding.btnRemind.setOnClickListener { onRemindInstallmentClick(installment) }
        }

        private fun getStatusInfo(status: InstallmentStatus): Pair<String, Int> {
            return when (status) {
                InstallmentStatus.PENDING -> Pair(
                    binding.root.context.getString(R.string.pending_payment),
                    R.color.orange
                )
                InstallmentStatus.PAID -> Pair(
                    binding.root.context.getString(R.string.paid),
                    R.color.green
                )
                InstallmentStatus.OVERDUE -> Pair(
                    binding.root.context.getString(R.string.overdue),
                    R.color.red
                )
                InstallmentStatus.PARTIALLY_PAID -> Pair(
                    binding.root.context.getString(R.string.partially_paid),
                    R.color.blue
                )
            }
        }
    }

    class InstallmentDiffCallback : DiffUtil.ItemCallback<Installment>() {
        override fun areItemsTheSame(oldItem: Installment, newItem: Installment): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Installment, newItem: Installment): Boolean {
            return oldItem == newItem
        }
    }
}