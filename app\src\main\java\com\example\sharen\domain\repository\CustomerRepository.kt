package com.example.sharen.domain.repository

import com.example.sharen.domain.model.Customer
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Repository Interface برای مدیریت مشتریان
 */
interface CustomerRepository {
    
    /**
     * دریافت تمام مشتریان
     */
    fun getAllCustomers(): Flow<List<Customer>>
    
    /**
     * دریافت مشتری با شناسه
     */
    suspend fun getCustomerById(customerId: String): Result<Customer?>
    
    /**
     * دریافت مشتریان فروشنده
     */
    fun getCustomersBySeller(sellerId: String): Flow<List<Customer>>
    
    /**
     * جستجوی مشتریان
     */
    fun searchCustomers(query: String): Flow<List<Customer>>
    
    /**
     * فیلتر مشتریان بر اساس بدهی
     */
    fun getCustomersByDebtStatus(hasDebt: Boolean): Flow<List<Customer>>
    
    /**
     * دریافت مشتریان با اعتبار کم
     */
    fun getCustomersWithLowCredit(): Flow<List<Customer>>
    
    /**
     * افزودن مشتری جدید
     */
    suspend fun addCustomer(customer: Customer): Result<Customer>
    
    /**
     * بروزرسانی مشتری
     */
    suspend fun updateCustomer(customer: Customer): Result<Customer>
    
    /**
     * حذف مشتری
     */
    suspend fun deleteCustomer(customerId: String): Result<Unit>
    
    /**
     * بروزرسانی اعتبار مشتری
     */
    suspend fun updateCustomerCredit(customerId: String, creditLimit: Long): Result<Unit>
    
    /**
     * بروزرسانی بدهی مشتری
     */
    suspend fun updateCustomerDebt(customerId: String, debtAmount: Long): Result<Unit>
    
    /**
     * ثبت خرید مشتری
     */
    suspend fun recordPurchase(customerId: String, amount: Long, date: Date): Result<Unit>
    
    /**
     * ثبت پرداخت مشتری
     */
    suspend fun recordPayment(customerId: String, amount: Long, date: Date): Result<Unit>
    
    /**
     * دریافت آمار مشتری
     */
    suspend fun getCustomerStatistics(customerId: String): Result<CustomerStatistics>
    
    /**
     * دریافت تاریخچه خرید مشتری
     */
    fun getCustomerPurchaseHistory(customerId: String): Flow<List<CustomerPurchase>>
    
    /**
     * دریافت مشتریان بر اساس بازه زمانی ثبت‌نام
     */
    fun getCustomersByDateRange(startDate: Date, endDate: Date): Flow<List<Customer>>
    
    /**
     * دریافت مشتریان فعال (خرید در 30 روز گذشته)
     */
    fun getActiveCustomers(): Flow<List<Customer>>
    
    /**
     * دریافت مشتریان غیرفعال
     */
    fun getInactiveCustomers(daysSinceLastPurchase: Int = 90): Flow<List<Customer>>
}

/**
 * آمار مشتری
 */
data class CustomerStatistics(
    val totalPurchases: Long,
    val totalPayments: Long,
    val totalDebt: Long,
    val averagePurchaseAmount: Long,
    val purchaseCount: Int,
    val paymentCount: Int,
    val lastPurchaseDate: Date?,
    val lastPaymentDate: Date?
)

/**
 * خرید مشتری
 */
data class CustomerPurchase(
    val invoiceId: String,
    val amount: Long,
    val date: Date,
    val status: String
)
