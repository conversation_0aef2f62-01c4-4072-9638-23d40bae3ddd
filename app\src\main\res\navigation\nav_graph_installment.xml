<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/nav_graph_installment"
    app:startDestination="@id/installmentListFragment">

    <fragment
        android:id="@+id/installmentListFragment"
        android:name="com.example.sharen.ui.installment.InstallmentListFragment"
        android:label="@string/installments">
        <action
            android:id="@+id/action_installmentList_to_installmentDetail"
            app:destination="@id/installmentDetailFragment" />
        <action
            android:id="@+id/action_installmentList_to_installmentEdit"
            app:destination="@id/installmentEditFragment" />
    </fragment>

    <fragment
        android:id="@+id/installmentDetailFragment"
        android:name="com.example.sharen.ui.installment.InstallmentDetailFragment"
        android:label="@string/installment_details">
        <argument
            android:name="installmentId"
            app:argType="string" />
        <action
            android:id="@+id/action_installmentDetail_to_installmentEdit"
            app:destination="@id/installmentEditFragment" />
    </fragment>

    <fragment
        android:id="@+id/installmentEditFragment"
        android:name="com.example.sharen.ui.installment.InstallmentEditFragment"
        android:label="@string/add_edit_installment">
        <argument
            android:name="installmentId"
            app:argType="string"
            android:defaultValue="null"
            app:nullable="true" />
    </fragment>

</navigation>
