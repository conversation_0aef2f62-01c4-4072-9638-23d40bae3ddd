package com.example.sharen.presentation.ui.auth

import android.content.Intent
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.sharen.core.base.BaseActivity
import com.example.sharen.core.utils.gone
import com.example.sharen.core.utils.visible
import com.example.sharen.databinding.ActivityLoginBinding
import com.example.sharen.presentation.ui.dashboard.DashboardActivity
import com.example.sharen.presentation.viewmodel.AuthViewModel
import com.example.sharen.presentation.viewmodel.LoginState
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * صفحه ورود کاربر
 */
@AndroidEntryPoint
class LoginActivity : BaseActivity<ActivityLoginBinding>() {

    private val authViewModel: AuthViewModel by viewModels()

    override fun getViewBinding(): ActivityLoginBinding {
        return ActivityLoginBinding.inflate(layoutInflater)
    }

    override fun setupViews() {
        setupClickListeners()
        setupUI()
    }

    override fun observeData() {
        observeLoginState()
        observeLoadingState()
        observeMessages()
    }

    private fun setupClickListeners() {
        binding.apply {
            btnLogin.setOnClickListener {
                performLogin()
            }

            tvRegister.setOnClickListener {
                navigateToRegister()
            }

            tvForgotPassword.setOnClickListener {
                navigateToForgotPassword()
            }
        }
    }

    private fun setupUI() {
        // تنظیم فونت‌ها و استایل‌ها
        binding.apply {
            // فوکوس روی فیلد ایمیل
            etEmail.requestFocus()
        }
    }

    private fun observeLoginState() {
        lifecycleScope.launch {
            authViewModel.loginState.collect { state ->
                when (state) {
                    is LoginState.Idle -> {
                        // حالت عادی
                    }
                    is LoginState.Loading -> {
                        showLoading()
                    }
                    is LoginState.Success -> {
                        hideLoading()
                        navigateToDashboard()
                    }
                    is LoginState.Error -> {
                        hideLoading()
                        showError(state.message)
                    }
                }
            }
        }
    }

    private fun observeLoadingState() {
        lifecycleScope.launch {
            authViewModel.isLoading.collect { isLoading ->
                if (isLoading) {
                    binding.progressBar.visible()
                    binding.btnLogin.isEnabled = false
                } else {
                    binding.progressBar.gone()
                    binding.btnLogin.isEnabled = true
                }
            }
        }
    }

    private fun observeMessages() {
        lifecycleScope.launch {
            authViewModel.error.collect { error ->
                error?.let {
                    showError(it)
                    authViewModel.clearError()
                }
            }
        }

        lifecycleScope.launch {
            authViewModel.success.collect { success ->
                success?.let {
                    showSuccess(it)
                    authViewModel.clearSuccess()
                }
            }
        }
    }

    private fun performLogin() {
        val email = binding.etEmail.text.toString().trim()
        val password = binding.etPassword.text.toString()

        // اعتبارسنجی ساده
        if (email.isEmpty()) {
            binding.etEmail.error = "ایمیل را وارد کنید"
            return
        }

        if (password.isEmpty()) {
            binding.etPassword.error = "رمز عبور را وارد کنید"
            return
        }

        // انجام ورود
        authViewModel.login(email, password)
    }

    private fun navigateToRegister() {
        val intent = Intent(this, RegisterActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToForgotPassword() {
        val intent = Intent(this, ForgotPasswordActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToDashboard() {
        val intent = Intent(this, DashboardActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    override fun showLoading() {
        binding.progressBar.visible()
        binding.btnLogin.isEnabled = false
    }

    override fun hideLoading() {
        binding.progressBar.gone()
        binding.btnLogin.isEnabled = true
    }
}
