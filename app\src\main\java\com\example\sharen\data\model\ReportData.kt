package com.example.sharen.data.model

data class ReportData(
    val type: ReportType = ReportType.SALES,
    val startDate: Long = 0L,
    val endDate: Long = 0L,
    val totalSales: Double = 0.0,
    val revenue: Double = 0.0,
    val expenses: Double = 0.0,
    val profit: Double = 0.0,
    val customerCount: Int = 0,
    val productCount: Int = 0,
    val invoiceCount: Int = 0,
    val salesByDate: Map<String, Double> = emptyMap(),
    val salesByProduct: Map<String, Double> = emptyMap(),
    val salesByCustomer: Map<String, Double> = emptyMap(),
    val inventoryStatus: Map<String, Int> = emptyMap()
) 