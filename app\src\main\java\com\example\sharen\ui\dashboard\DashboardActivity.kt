package com.example.sharen.ui.dashboard

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GravityCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.data.model.Transaction
import com.example.sharen.databinding.ActivityDashboardBinding
import com.example.sharen.ui.auth.LoginActivity
import com.example.sharen.ui.customer.CustomerFormActivity
import com.example.sharen.ui.customer.CustomerListActivity
import com.example.sharen.ui.product.ProductListActivity
import com.example.sharen.ui.product.ProductTestActivity
import com.google.android.material.navigation.NavigationBarView
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.util.Locale

@AndroidEntryPoint
class DashboardActivity : AppCompatActivity(), NavigationBarView.OnItemSelectedListener {

    private lateinit var binding: ActivityDashboardBinding
    private val viewModel: DashboardViewModel by viewModels()
    private lateinit var transactionAdapter: TransactionAdapter
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDashboardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupObservers()
    }

    private fun setupUI() {
        // Set up toolbar
        setSupportActionBar(binding.toolbar)

        // Set up bottom navigation
        binding.bottomNavigation.setOnItemSelectedListener(this)

        // Set up transactions RecyclerView
        setupTransactionsRecyclerView()

        // Set up notification button
        binding.ivNotifications.setOnClickListener {
            // TODO: Navigate to notifications
            Toast.makeText(this, "اعلان‌ها", Toast.LENGTH_SHORT).show()
        }

        // Set up quick action buttons
        binding.cardNewInvoice.setOnClickListener {
            // TODO: Navigate to new invoice
            Toast.makeText(this, "فاکتور جدید", Toast.LENGTH_SHORT).show()
        }
        
        binding.cardAddCustomer.setOnClickListener {
            startActivity(Intent(this, CustomerFormActivity::class.java))
        }
        
        // ایجاد دکمه تست آپلود تصویر محصول
        binding.cardTestImageUpload.setOnClickListener {
            startActivity(Intent(this, ProductTestActivity::class.java))
        }
        
        // Set up floating action button
        binding.fabAdd.setOnClickListener {
            // TODO: Show quick actions menu
            Toast.makeText(this, "افزودن", Toast.LENGTH_SHORT).show()
        }

        // Set up view all transactions
        binding.tvViewAll.setOnClickListener {
            // TODO: Navigate to all transactions
            Toast.makeText(this, "مشاهده همه تراکنش‌ها", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupTransactionsRecyclerView() {
        transactionAdapter = TransactionAdapter { transaction ->
            onTransactionClicked(transaction)
        }

        binding.rvRecentTransactions.apply {
            layoutManager = LinearLayoutManager(this@DashboardActivity)
            adapter = transactionAdapter
        }
    }

    private fun setupObservers() {
        viewModel.totalSales.observe(this) { totalSales ->
            binding.tvTotalSalesValue.text = numberFormatter.format(totalSales)
        }

        viewModel.totalCustomers.observe(this) { totalCustomers ->
            binding.tvTotalCustomersValue.text = numberFormatter.format(totalCustomers)
        }

        viewModel.recentTransactions.observe(this) { transactions ->
            transactionAdapter.submitList(transactions)
        }

        viewModel.isLoading.observe(this) { isLoading ->
            // TODO: Show loading indicator
        }
    }

    private fun onTransactionClicked(transaction: Transaction) {
        // TODO: Navigate to transaction details
        Toast.makeText(this, "تراکنش: ${transaction.invoiceNumber}", Toast.LENGTH_SHORT).show()
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.navigation_dashboard -> {
                // We're already here
                return true
            }
            R.id.navigation_customers -> {
                startActivity(Intent(this, CustomerListActivity::class.java))
                return true
            }
            R.id.navigation_invoices -> {
                // TODO: Navigate to invoices
                Toast.makeText(this, "فاکتورها", Toast.LENGTH_SHORT).show()
                return true
            }
            R.id.navigation_products -> {
                startActivity(Intent(this, ProductListActivity::class.java))
                return true
            }
            R.id.navigation_reports -> {
                // TODO: Navigate to reports
                Toast.makeText(this, "گزارش‌ها", Toast.LENGTH_SHORT).show()
                return true
            }
        }
        return false
    }

    private fun logout() {
        // TODO: Implement logout logic
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
} 