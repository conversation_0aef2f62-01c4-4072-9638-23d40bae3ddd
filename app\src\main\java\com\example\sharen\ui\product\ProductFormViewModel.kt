package com.example.sharen.ui.product

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Product
import com.example.sharen.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProductFormViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {

    // حالت ویرایش یا ایجاد
    private val _isEditMode = MutableLiveData<Boolean>(false)
    val isEditMode: LiveData<Boolean> = _isEditMode

    // اطلاعات محصول
    private val _product = MutableLiveData<Product?>()
    val product: LiveData<Product?> = _product

    // تصویر محصول
    private val _imageUrl = MutableLiveData<String?>(null)

    // وضعیت بارگذاری
    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    // پیام خطا
    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    // وضعیت موفقیت ذخیره
    private val _saveSuccess = MutableLiveData<Boolean>(false)
    val saveSuccess: LiveData<Boolean> = _saveSuccess

    fun loadProduct(productId: String?) {
        productId ?: return
        
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            productRepository.getProductById(productId)
                .catch { e ->
                    _error.postValue(e.message ?: "خطا در بارگذاری اطلاعات محصول")
                    _isLoading.postValue(false)
                }
                .collectLatest { product ->
                    _product.postValue(product)
                    _imageUrl.postValue(product.imageUrl)
                    _isEditMode.postValue(true)
                    _isLoading.postValue(false)
                }
        }
    }

    // به‌روزرسانی آدرس تصویر
    fun updateImageUrl(url: String?) {
        _imageUrl.value = url
    }

    fun saveProduct(
        name: String,
        code: String?,
        barcode: String?,
        description: String?,
        category: String?,
        purchasePrice: Long,
        sellingPrice: Long,
        stock: Int,
        minimumStock: Int,
        imageUrl: String? = null
    ) {
        if (!validateInputs(name, purchasePrice, sellingPrice)) {
            return
        }

        // استفاده از imageUrl از پارامتر یا از مقدار ذخیره شده قبلی
        val finalImageUrl = imageUrl ?: _imageUrl.value

        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                val result = if (_isEditMode.value == true && _product.value != null) {
                    // ویرایش محصول موجود
                    val updatedProduct = _product.value!!.copy(
                        name = name,
                        code = code,
                        barcode = barcode,
                        description = description,
                        category = category,
                        purchasePrice = purchasePrice,
                        sellingPrice = sellingPrice,
                        stock = stock,
                        minimumStock = minimumStock,
                        imageUrl = finalImageUrl
                    )
                    productRepository.updateProduct(updatedProduct)
                } else {
                    // ایجاد محصول جدید
                    val newProduct = Product(
                        name = name,
                        code = code,
                        barcode = barcode,
                        description = description,
                        category = category,
                        purchasePrice = purchasePrice,
                        sellingPrice = sellingPrice,
                        stock = stock,
                        minimumStock = minimumStock,
                        imageUrl = finalImageUrl
                    )
                    productRepository.createProduct(newProduct)
                }

                result.onSuccess {
                    _saveSuccess.value = true
                    _isLoading.value = false
                }.onFailure { e ->
                    _error.value = e.message ?: "خطا در ذخیره اطلاعات محصول"
                    _isLoading.value = false
                }
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در ذخیره اطلاعات محصول"
                _isLoading.value = false
            }
        }
    }

    private fun validateInputs(name: String, purchasePrice: Long, sellingPrice: Long): Boolean {
        var isValid = true

        when {
            name.isBlank() -> {
                _error.value = "نام محصول الزامی است"
                isValid = false
            }
            purchasePrice < 0 -> {
                _error.value = "قیمت خرید نمی‌تواند منفی باشد"
                isValid = false
            }
            sellingPrice < 0 -> {
                _error.value = "قیمت فروش نمی‌تواند منفی باشد"
                isValid = false
            }
        }

        return isValid
    }
} 