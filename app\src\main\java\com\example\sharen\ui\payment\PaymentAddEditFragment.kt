package com.example.sharen.ui.payment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.example.sharen.R
import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentMethod
import com.example.sharen.data.model.PaymentStatus
import com.example.sharen.databinding.FragmentPaymentAddEditBinding
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.util.Date

@AndroidEntryPoint
class PaymentAddEditFragment : Fragment() {

    private var _binding: FragmentPaymentAddEditBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PaymentViewModel by viewModels()
    private val args: PaymentAddEditFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPaymentAddEditBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupSpinners()
        setupObservers()
        setupListeners()

        if (args.paymentId != null) {
            viewModel.getPayment(args.paymentId)
        }
    }

    private fun setupSpinners() {
        // Payment Method Spinner
        val paymentMethods = PaymentMethod.values().map { it.name }
        val paymentMethodAdapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            paymentMethods
        ).apply {
            setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        }
        binding.spinnerPaymentMethod.adapter = paymentMethodAdapter

        // Payment Status Spinner
        val paymentStatuses = PaymentStatus.values().map { it.name }
        val paymentStatusAdapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            paymentStatuses
        ).apply {
            setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        }
        binding.spinnerPaymentStatus.adapter = paymentStatusAdapter
    }

    private fun setupObservers() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.payments.collect { payments ->
                    payments.find { it.id == args.paymentId }?.let { payment ->
                        populateFields(payment)
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.loading.collect { isLoading ->
                    binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.error.collect { error ->
                    error?.let {
                        Snackbar.make(binding.root, it, Snackbar.LENGTH_LONG).show()
                        viewModel.clearError()
                    }
                }
            }
        }
    }

    private fun setupListeners() {
        binding.buttonSave.setOnClickListener {
            if (validateInputs()) {
                savePayment()
            }
        }

        binding.buttonCancel.setOnClickListener {
            findNavController().navigateUp()
        }
    }

    private fun populateFields(payment: Payment) {
        binding.apply {
            editTextAmount.setText(payment.amount.toString())
            editTextReference.setText(payment.referenceNumber)
            editTextNotes.setText(payment.notes)
            spinnerPaymentMethod.setSelection(PaymentMethod.values().indexOf(payment.paymentMethod))
            spinnerPaymentStatus.setSelection(PaymentStatus.values().indexOf(payment.status))
        }
    }

    private fun validateInputs(): Boolean {
        var isValid = true

        binding.apply {
            if (editTextAmount.text.isNullOrBlank()) {
                editTextAmount.error = getString(R.string.error_amount_required)
                isValid = false
            }

            if (spinnerPaymentMethod.selectedItem == null) {
                textViewPaymentMethodError.visibility = View.VISIBLE
                isValid = false
            } else {
                textViewPaymentMethodError.visibility = View.GONE
            }

            if (spinnerPaymentStatus.selectedItem == null) {
                textViewPaymentStatusError.visibility = View.VISIBLE
                isValid = false
            } else {
                textViewPaymentStatusError.visibility = View.GONE
            }
        }

        return isValid
    }

    private fun savePayment() {
        val payment = Payment(
            id = args.paymentId ?: "",
            customerId = args.customerId,
            orderId = args.orderId,
            amount = binding.editTextAmount.text.toString().toDouble(),
            date = Date(),
            status = PaymentStatus.valueOf(binding.spinnerPaymentStatus.selectedItem.toString()),
            paymentMethod = PaymentMethod.valueOf(binding.spinnerPaymentMethod.selectedItem.toString()),
            referenceNumber = binding.editTextReference.text.toString(),
            notes = binding.editTextNotes.text.toString(),
            createdAt = Date(),
            updatedAt = Date(),
            createdBy = "", // Will be set by the server
            updatedBy = "" // Will be set by the server
        )

        if (args.paymentId != null) {
            viewModel.updatePayment(payment)
        } else {
            viewModel.createPayment(payment)
        }

        findNavController().navigateUp()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 