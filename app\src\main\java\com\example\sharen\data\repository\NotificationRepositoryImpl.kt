package com.example.sharen.data.repository

import com.example.sharen.data.model.Notification
import com.example.sharen.data.model.NotificationType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationRepositoryImpl @Inject constructor(
    // در صورت استفاده از Supabase یا Room این پارامترها اضافه می‌شوند
) : NotificationRepository {

    // لیست موقت اعلان‌ها برای پیاده‌سازی اولیه
    private val mockNotifications = mutableListOf<Notification>()

    init {
        // اضافه کردن چند اعلان نمونه
        mockNotifications.add(
            Notification(
                id = "notification1",
                title = "خوش آمدید",
                message = "به سیستم مدیریت فروشگاه شارن خوش آمدید.",
                type = NotificationType.SYSTEM,
                isRead = false,
                createdAt = Date()
            )
        )
        mockNotifications.add(
            Notification(
                id = "notification2",
                title = "فاکتور جدید",
                message = "فاکتور شماره 123456 با موفقیت ثبت شد.",
                type = NotificationType.INVOICE,
                referenceId = "invoice1",
                isRead = false,
                createdAt = Date(System.currentTimeMillis() - 3600000) // یک ساعت قبل
            )
        )
        mockNotifications.add(
            Notification(
                id = "notification3",
                title = "پرداخت موفق",
                message = "پرداخت به مبلغ 1,000,000 تومان با موفقیت انجام شد.",
                type = NotificationType.PAYMENT,
                referenceId = "payment1",
                isRead = true,
                createdAt = Date(System.currentTimeMillis() - 86400000) // یک روز قبل
            )
        )
    }

    override fun getAllNotifications(): Flow<List<Notification>> = flow {
        val notifications = mockNotifications.sortedByDescending { it.createdAt }
        emit(notifications)
    }

    override fun getUnreadNotifications(): Flow<List<Notification>> = flow {
        val unreadNotifications = mockNotifications.filter { !it.isRead }
            .sortedByDescending { it.createdAt }
        emit(unreadNotifications)
    }

    override suspend fun createNotification(notification: Notification): Result<Notification> = withContext(Dispatchers.IO) {
        try {
            val newNotification = notification.copy(
                id = "notification${mockNotifications.size + 1}",
                createdAt = Date(),
                updatedAt = Date()
            )
            mockNotifications.add(newNotification)
            Result.success(newNotification)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun markAsRead(notificationId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val index = mockNotifications.indexOfFirst { it.id == notificationId }
            if (index != -1) {
                val notification = mockNotifications[index]
                mockNotifications[index] = notification.copy(
                    isRead = true,
                    updatedAt = Date()
                )
                Result.success(true)
            } else {
                Result.failure(NoSuchElementException("اعلان با شناسه $notificationId یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun markAsUnread(notificationId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val index = mockNotifications.indexOfFirst { it.id == notificationId }
            if (index != -1) {
                val notification = mockNotifications[index]
                mockNotifications[index] = notification.copy(
                    isRead = false,
                    updatedAt = Date()
                )
                Result.success(true)
            } else {
                Result.failure(NoSuchElementException("اعلان با شناسه $notificationId یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteNotification(notificationId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val removed = mockNotifications.removeIf { it.id == notificationId }
            if (removed) {
                Result.success(true)
            } else {
                Result.failure(NoSuchElementException("اعلان با شناسه $notificationId یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun clearAllNotifications(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            mockNotifications.clear()
            Result.success(true)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
} 