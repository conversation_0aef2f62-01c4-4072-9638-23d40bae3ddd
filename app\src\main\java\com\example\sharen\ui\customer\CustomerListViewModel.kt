package com.example.sharen.ui.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Customer
import com.example.sharen.data.repository.CustomerRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CustomerListViewModel @Inject constructor(
    private val customerRepository: CustomerRepository
) : ViewModel() {

    // Search query
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery

    // Customers
    private val _customers = MutableLiveData<List<Customer>>()
    val customers: LiveData<List<Customer>> = _customers

    // Customer count
    private val _customerCount = MutableLiveData<Int>()
    val customerCount: LiveData<Int> = _customerCount

    // Loading state
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // Error state
    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    init {
        loadCustomerCount()
        setupSearchFlow()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun setupSearchFlow() {
        viewModelScope.launch {
            _isLoading.value = true
            
            searchQuery
                .flatMapLatest { query ->
                    if (query.isBlank()) {
                        customerRepository.getCustomers()
                    } else {
                        customerRepository.searchCustomers(query)
                    }
                }
                .catch { e ->
                    _error.postValue(e.message ?: "Unknown error")
                    _isLoading.postValue(false)
                }
                .collectLatest { customers ->
                    _customers.postValue(customers)
                    _isLoading.postValue(false)
                }
        }
    }

    private fun loadCustomerCount() {
        viewModelScope.launch {
            customerRepository.getCustomerCount()
                .catch { e ->
                    _error.postValue(e.message ?: "Unknown error")
                }
                .collectLatest { count ->
                    _customerCount.postValue(count)
                }
        }
    }

    fun search(query: String) {
        _searchQuery.value = query
    }

    fun refresh() {
        // Reset search and reload
        _searchQuery.value = ""
        loadCustomerCount()
    }
} 