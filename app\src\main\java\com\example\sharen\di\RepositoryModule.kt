package com.example.sharen.di

import com.example.sharen.data.repository.UserRepositoryImpl
import com.example.sharen.data.repository.CustomerRepositoryImpl
import com.example.sharen.data.repository.ProductRepositoryImpl
import com.example.sharen.domain.repository.UserRepository
import com.example.sharen.domain.repository.CustomerRepository
import com.example.sharen.domain.repository.ProductRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt Module برای Repository ها
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindUserRepository(
        userRepositoryImpl: UserRepositoryImpl
    ): UserRepository

    @Binds
    @Singleton
    abstract fun bindCustomerRepository(
        customerRepositoryImpl: CustomerRepositoryImpl
    ): CustomerRepository

    @Binds
    @Singleton
    abstract fun bindProductRepository(
        productRepositoryImpl: ProductRepositoryImpl
    ): ProductRepository
}
