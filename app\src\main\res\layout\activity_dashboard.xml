<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.dashboard.DashboardActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="56dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:titleTextColor="@color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginEnd="16dp">

                <TextView
                    android:id="@+id/tv_welcome"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/dashboard"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="تاریخ امروز"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_notifications"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="end"
                android:layout_marginEnd="16dp"
                android:contentDescription="@string/notifications"
                android:src="@android:drawable/ic_popup_reminder" />

        </androidx.appcompat.widget.Toolbar>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <!-- Stats Cards -->
                <LinearLayout
                    android:id="@+id/layout_stats_row1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_total_sales"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/total_sales"
                                android:src="@android:drawable/ic_menu_sort_by_size" />

                            <TextView
                                android:id="@+id/tv_total_sales"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="0"
                                android:textColor="@color/primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/total_sales"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_customers"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/total_customers"
                                android:src="@android:drawable/ic_menu_myplaces" />

                            <TextView
                                android:id="@+id/tv_customers_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="0"
                                android:textColor="@color/primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_customers_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/total_customers"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_stats_row2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layout_stats_row1">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_products"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/total_products"
                                android:src="@android:drawable/ic_menu_gallery" />

                            <TextView
                                android:id="@+id/tv_products_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="0"
                                android:textColor="@color/primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_products_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/total_products"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_invoices"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/total_invoices"
                                android:src="@android:drawable/ic_menu_agenda" />

                            <TextView
                                android:id="@+id/tv_invoices_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="0"
                                android:textColor="@color/primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_invoices_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/total_invoices"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_stats_row3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layout_stats_row2">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_payments"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/payments"
                                android:src="@android:drawable/ic_menu_today" />

                            <TextView
                                android:id="@+id/tv_payments_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="0"
                                android:textColor="@color/primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_payments_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/payments"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_total_debt"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/total_debt"
                                android:src="@android:drawable/ic_menu_report_image" />

                            <TextView
                                android:id="@+id/tv_total_debt"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="0"
                                android:textColor="@color/primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/total_debt"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <!-- Recent Transactions -->
                <TextView
                    android:id="@+id/tv_recent_transactions_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/recent_transactions"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layout_stats" />

                <TextView
                    android:id="@+id/tv_view_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/view_all"
                    android:textColor="@color/primary"
                    app:layout_constraintBaseline_toBaselineOf="@+id/tv_recent_transactions_header"
                    app:layout_constraintEnd_toEndOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_recent_transactions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_recent_transactions_header"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_transaction" />

                <!-- Quick Actions -->
                <TextView
                    android:id="@+id/tv_quick_actions_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/quick_actions"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/rv_recent_transactions" />

                <LinearLayout
                    android:id="@+id/layout_quick_actions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_quick_actions_header">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_new_invoice"
                        android:layout_width="0dp"
                        android:layout_height="100dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/new_invoice"
                                android:src="@android:drawable/ic_menu_add" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/new_invoice"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_add_customer"
                        android:layout_width="0dp"
                        android:layout_height="100dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/add_customer"
                                android:src="@android:drawable/ic_menu_add" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/add_customer"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                    
                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_test_image_upload"
                        android:layout_width="0dp"
                        android:layout_height="100dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="تست آپلود تصویر"
                                android:src="@android:drawable/ic_menu_camera" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="تست آپلود تصویر"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_quick_actions_row2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layout_quick_actions">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_new_product"
                        android:layout_width="0dp"
                        android:layout_height="100dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/add_new_product"
                                android:src="@android:drawable/ic_menu_add" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/add_new_product"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_reports"
                        android:layout_width="0dp"
                        android:layout_height="100dp"
                        android:layout_margin="4dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:contentDescription="@string/reports"
                                android:src="@android:drawable/ic_menu_report_image" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/reports"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_gravity="bottom"
        app:menu="@menu/bottom_navigation_menu" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center"
        android:layout_marginBottom="28dp"
        android:contentDescription="@string/add"
        android:src="@android:drawable/ic_input_add"
        app:fabSize="normal" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
