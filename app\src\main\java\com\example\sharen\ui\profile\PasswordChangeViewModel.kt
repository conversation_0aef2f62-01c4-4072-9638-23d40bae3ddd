package com.example.sharen.ui.profile

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PasswordChangeViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _success = MutableLiveData<Boolean>()
    val success: LiveData<Boolean> = _success

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    fun changePassword(currentPassword: String, newPassword: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = authRepository.changePassword(currentPassword, newPassword)
                if (result) {
                    _success.value = true
                } else {
                    _error.value = "خطا در تغییر رمز عبور"
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در تغییر رمز عبور"
                _isLoading.value = false
            }
        }
    }
} 