package com.example.sharen.ui.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Product
import com.example.sharen.databinding.ItemProductBinding
import com.example.sharen.util.ImageUtils
import java.text.NumberFormat
import java.util.Locale

/**
 * آداپتر برای نمایش محصولات در RecyclerView
 */
class ProductAdapter(
    private val onProductClick: (Product) -> Unit
) : ListAdapter<Product, ProductAdapter.ProductViewHolder>(ProductDiffCallback()) {

    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductViewHolder {
        val binding = ItemProductBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ProductViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ProductViewHolder, position: Int) {
        val product = getItem(position)
        holder.bind(product)
    }

    inner class ProductViewHolder(
        private val binding: ItemProductBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onProductClick(getItem(position))
                }
            }
        }

        fun bind(product: Product) {
            binding.apply {
                tvProductName.text = product.name
                tvProductCategory.text = product.category ?: ""
                tvProductPrice.text = formatCurrency(product.sellingPrice)
                tvProductCode.text = product.code ?: ""

                // نمایش وضعیت موجودی
                when {
                    product.stock <= 0 -> {
                        tvStockStatus.text = "اتمام موجودی"
                        tvStockStatus.setTextColor(ContextCompat.getColor(root.context, R.color.red_700))
                    }
                    product.isLowStock -> {
                        tvStockStatus.text = "موجودی کم"
                        tvStockStatus.setTextColor(ContextCompat.getColor(root.context, R.color.orange_700))
                    }
                    else -> {
                        tvStockStatus.text = "موجود"
                        tvStockStatus.setTextColor(ContextCompat.getColor(root.context, R.color.green_700))
                    }
                }

                // نمایش تصویر محصول
                ImageUtils.loadImage(root.context, product.imageUrl, ivProductImage)
            }
        }

        private fun formatCurrency(amount: Long): String {
            return "${numberFormatter.format(amount)} تومان"
        }
    }

    /**
     * کلاس مقایسه کننده برای محصولات
     */
    class ProductDiffCallback : DiffUtil.ItemCallback<Product>() {
        override fun areItemsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem == newItem
        }
    }
}