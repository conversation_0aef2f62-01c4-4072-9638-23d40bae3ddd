# ساختار برنامه و وابستگی‌های بین صفحات

## 1. ساختار کلی برنامه

### 1.1. جریان اصلی
```
SplashActivity → LoginActivity → (RegisterActivity) → DashboardActivity → ...
```

### 1.2. ساختار صفحات
- **صفحات احراز هویت**: ورود، ثبت‌نام، بازیابی رمز
- **صفحات داشبورد**: داشبورد مشتری، داشبورد فروشنده، داشبورد مدیر
- **صفحات فروش**: فاکتور جدید، لیست فاکتورها، جزئیات فاکتور
- **صفحات مدیریت مشتری**: افزودن مشتری، لیست مشتریان، جزئیات مشتری
- **صفحات مدیریت محصول**: افزودن محصول، لیست محصولات، جزئیات محصول
- **صفحات مالی**: ثبت پرداخت، لیست پرداخت‌ها، مدیریت اقساط
- **صفحات گزارش‌ها**: گزارش فروش، گزارش مالی، گزارش انبار
- **صفحات تنظیمات**: پروفایل، تنظیمات سیستم، پشتیبان‌گیری

## 2. جزئیات صفحات و وابستگی‌ها

### 2.1. صفحات احراز هویت

#### SplashActivity
- **عملکرد**: نمایش لوگو و بررسی وضعیت ورود کاربر
- **وابستگی‌ها**: 
  * `SharedPreferences` برای بررسی وضعیت ورود
  * `AuthManager` برای تأیید توکن
- **انتقال به**: 
  * `LoginActivity` (اگر کاربر وارد نشده باشد)
  * `DashboardActivity` (اگر کاربر قبلاً وارد شده باشد)

#### LoginActivity
- **عملکرد**: ورود کاربر به سیستم
- **وابستگی‌ها**: 
  * `AuthManager` برای احراز هویت
  * `SupabaseManager` برای ارتباط با سرور
- **انتقال به**:
  * `RegisterActivity` (کلیک روی دکمه "ثبت‌نام")
  * `ForgotPasswordActivity` (کلیک روی "فراموشی رمز عبور")
  * `DashboardActivity` (پس از ورود موفق)

#### RegisterActivity
- **عملکرد**: ثبت کاربر جدید
- **وابستگی‌ها**:
  * `AuthManager` برای ایجاد حساب
  * `SupabaseManager` برای ثبت اطلاعات کاربر
- **انتقال به**:
  * `LoginActivity` (پس از ثبت‌نام موفق یا لغو)

#### ForgotPasswordActivity
- **عملکرد**: بازیابی رمز عبور
- **وابستگی‌ها**:
  * `AuthManager` برای بازیابی رمز
  * `SupabaseManager` برای ارسال ایمیل بازیابی
- **انتقال به**:
  * `LoginActivity` (پس از ارسال ایمیل بازیابی)

### 2.2. صفحات داشبورد

#### DashboardActivity
- **عملکرد**: نمایش داشبورد متناسب با نقش کاربر
- **وابستگی‌ها**:
  * `UserManager` برای دریافت اطلاعات کاربر
  * `ReportManager` برای نمایش آمار
  * `DashboardViewModel` برای مدیریت داده‌ها
- **انتقال به**:
  * تمام صفحات اصلی برنامه از طریق منو یا دکمه‌های سریع
  * `ProfileActivity` (کلیک روی پروفایل)
  * `NotificationActivity` (کلیک روی آیکون نوتیفیکیشن)
  * `SettingsActivity` (کلیک روی آیکون تنظیمات)

### 2.3. صفحات فروش

#### SalesInvoiceActivity
- **عملکرد**: ایجاد فاکتور فروش جدید
- **وابستگی‌ها**:
  * `InvoiceManager` برای مدیریت فاکتور
  * `CustomerManager` برای انتخاب مشتری
  * `ProductManager` برای انتخاب محصول
  * `InvoiceViewModel` برای مدیریت داده‌ها
- **انتقال به**:
  * `CustomerSelectionActivity` (کلیک روی "انتخاب مشتری")
  * `ProductSelectionActivity` (کلیک روی "افزودن محصول")
  * `PaymentSelectionActivity` (کلیک روی "انتخاب روش پرداخت")
  * `InvoiceDetailsActivity` (پس از ثبت فاکتور)

#### InvoiceListActivity
- **عملکرد**: نمایش لیست فاکتورها
- **وابستگی‌ها**:
  * `InvoiceManager` برای دریافت لیست فاکتورها
  * `InvoiceAdapter` برای نمایش فاکتورها
  * `SearchManager` برای جستجو در فاکتورها
- **انتقال به**:
  * `InvoiceDetailsActivity` (کلیک روی هر فاکتور)
  * `SalesInvoiceActivity` (کلیک روی دکمه "فاکتور جدید")

#### InvoiceDetailsActivity
- **عملکرد**: نمایش جزئیات فاکتور
- **وابستگی‌ها**:
  * `InvoiceManager` برای دریافت جزئیات فاکتور
  * `InvoiceItemAdapter` برای نمایش اقلام فاکتور
  * `PrintManager` برای چاپ فاکتور
- **انتقال به**:
  * `PaymentActivity` (کلیک روی "ثبت پرداخت")
  * `CustomerDetailsActivity` (کلیک روی نام مشتری)
  * `InvoiceEditActivity` (کلیک روی "ویرایش" - فقط برای فاکتورهای تأیید نشده)

### 2.4. صفحات مدیریت مشتری

#### CustomerActivity
- **عملکرد**: نمایش لیست مشتریان
- **وابستگی‌ها**:
  * `CustomerManager` برای دریافت لیست مشتریان
  * `CustomerAdapter` برای نمایش مشتریان
  * `SearchManager` برای جستجو در مشتریان
- **انتقال به**:
  * `CustomerDetailsActivity` (کلیک روی هر مشتری)
  * `CustomerAddActivity` (کلیک روی دکمه "افزودن مشتری")

#### CustomerAddActivity
- **عملکرد**: افزودن مشتری جدید
- **وابستگی‌ها**:
  * `CustomerManager` برای افزودن مشتری
  * `ValidationManager` برای اعتبارسنجی اطلاعات
  * `ImageManager` برای آپلود تصویر مشتری
- **انتقال به**:
  * `CustomerActivity` (پس از افزودن مشتری یا لغو)

#### CustomerDetailsActivity
- **عملکرد**: نمایش جزئیات مشتری
- **وابستگی‌ها**:
  * `CustomerManager` برای دریافت جزئیات مشتری
  * `InvoiceManager` برای دریافت فاکتورهای مشتری
  * `PaymentManager` برای دریافت پرداخت‌های مشتری
- **انتقال به**:
  * `CustomerEditActivity` (کلیک روی "ویرایش")
  * `InvoiceListActivity` (فیلتر شده برای این مشتری)
  * `PaymentListActivity` (فیلتر شده برای این مشتری)
  * `InstallmentListActivity` (فیلتر شده برای این مشتری)

### 2.5. صفحات مدیریت محصول

#### ProductActivity
- **عملکرد**: نمایش لیست محصولات
- **وابستگی‌ها**:
  * `ProductManager` برای دریافت لیست محصولات
  * `ProductAdapter` برای نمایش محصولات
  * `SearchManager` برای جستجو در محصولات
- **انتقال به**:
  * `ProductDetailsActivity` (کلیک روی هر محصول)
  * `ProductAddActivity` (کلیک روی دکمه "افزودن محصول")

#### ProductAddActivity
- **عملکرد**: افزودن محصول جدید
- **وابستگی‌ها**:
  * `ProductManager` برای افزودن محصول
  * `ValidationManager` برای اعتبارسنجی اطلاعات
  * `ImageManager` برای آپلود تصویر محصول
  * `CategoryManager` برای انتخاب دسته‌بندی
- **انتقال به**:
  * `ProductActivity` (پس از افزودن محصول یا لغو)

#### ProductDetailsActivity
- **عملکرد**: نمایش جزئیات محصول
- **وابستگی‌ها**:
  * `ProductManager` برای دریافت جزئیات محصول
  * `InventoryManager` برای نمایش موجودی
  * `SalesManager` برای نمایش آمار فروش
- **انتقال به**:
  * `ProductEditActivity` (کلیک روی "ویرایش")
  * `InventoryActivity` (کلیک روی "مدیریت موجودی")
  * `ProductSalesReportActivity` (کلیک روی "گزارش فروش")

### 2.6. صفحات مالی

#### PaymentActivity
- **عملکرد**: ثبت پرداخت جدید
- **وابستگی‌ها**:
  * `PaymentManager` برای ثبت پرداخت
  * `CustomerManager` برای انتخاب مشتری
  * `InvoiceManager` برای انتخاب فاکتور
- **انتقال به**:
  * `CustomerSelectionActivity` (کلیک روی "انتخاب مشتری")
  * `InvoiceSelectionActivity` (کلیک روی "انتخاب فاکتور")
  * `PaymentDetailsActivity` (پس از ثبت پرداخت)

#### PaymentListActivity
- **عملکرد**: نمایش لیست پرداخت‌ها
- **وابستگی‌ها**:
  * `PaymentManager` برای دریافت لیست پرداخت‌ها
  * `PaymentAdapter` برای نمایش پرداخت‌ها
  * `SearchManager` برای جستجو در پرداخت‌ها
- **انتقال به**:
  * `PaymentDetailsActivity` (کلیک روی هر پرداخت)
  * `PaymentActivity` (کلیک روی دکمه "پرداخت جدید")

#### InstallmentActivity
- **عملکرد**: مدیریت اقساط
- **وابستگی‌ها**:
  * `InstallmentManager` برای مدیریت اقساط
  * `InstallmentAdapter` برای نمایش اقساط
  * `ReminderManager` برای ارسال یادآوری
- **انتقال به**:
  * `InstallmentDetailsActivity` (کلیک روی هر قسط)
  * `PaymentActivity` (کلیک روی "ثبت پرداخت")

### 2.7. صفحات گزارش‌ها

#### ReportActivity
- **عملکرد**: نمایش گزارش‌های مختلف
- **وابستگی‌ها**:
  * `ReportManager` برای دریافت گزارش‌ها
  * `ChartManager` برای نمایش نمودارها
  * `ExportManager` برای خروجی گزارش‌ها
- **انتقال به**:
  * `SalesReportActivity` (کلیک روی "گزارش فروش")
  * `FinancialReportActivity` (کلیک روی "گزارش مالی")
  * `InventoryReportActivity` (کلیک روی "گزارش انبار")
  * `CustomerReportActivity` (کلیک روی "گزارش مشتریان")

#### SalesReportActivity
- **عملکرد**: نمایش گزارش فروش
- **وابستگی‌ها**:
  * `SalesManager` برای دریافت آمار فروش
  * `ChartManager` برای نمایش نمودارها
  * `ExportManager` برای خروجی گزارش
- **انتقال به**:
  * `ProductSalesReportActivity` (کلیک روی "گزارش به تفکیک محصول")
  * `SellerSalesReportActivity` (کلیک روی "گزارش به تفکیک فروشنده")
  * `CustomerSalesReportActivity` (کلیک روی "گزارش به تفکیک مشتری")

### 2.8. صفحات تنظیمات

#### ProfileActivity
- **عملکرد**: نمایش و ویرایش پروفایل کاربر
- **وابستگی‌ها**:
  * `UserManager` برای دریافت و ویرایش اطلاعات کاربر
  * `ImageManager` برای تغییر تصویر پروفایل
  * `ValidationManager` برای اعتبارسنجی اطلاعات
- **انتقال به**:
  * `PasswordChangeActivity` (کلیک روی "تغییر رمز عبور")
  * `NotificationSettingsActivity` (کلیک روی "تنظیمات اعلان‌ها")

#### SettingsActivity
- **عملکرد**: تنظیمات کلی برنامه
- **وابستگی‌ها**:
  * `SettingsManager` برای ذخیره و بازیابی تنظیمات
  * `ThemeManager` برای تغییر تم
  * `LocaleManager` برای تغییر زبان
- **انتقال به**:
  * `BackupActivity` (کلیک روی "پشتیبان‌گیری و بازیابی")
  * `SecurityActivity` (کلیک روی "تنظیمات امنیتی")
  * `DisplaySettingsActivity` (کلیک روی "تنظیمات نمایش")

#### NotificationActivity
- **عملکرد**: نمایش اعلان‌های کاربر
- **وابستگی‌ها**:
  * `NotificationManager` برای دریافت اعلان‌ها
  * `NotificationAdapter` برای نمایش اعلان‌ها
- **انتقال به**:
  * صفحات مختلف بر اساس نوع اعلان (فاکتور، پرداخت، تأیید و غیره)
  * `NotificationSettingsActivity` (کلیک روی آیکون تنظیمات)

## 3. وابستگی‌های مهم کلاس‌ها

### 3.1. مدیریت کاربر و احراز هویت
- **AuthManager**: مدیریت ورود، خروج، ثبت‌نام و بازیابی رمز
- **UserManager**: مدیریت اطلاعات کاربر، نقش‌ها و دسترسی‌ها
- **PermissionManager**: کنترل دسترسی‌های کاربر به بخش‌های مختلف

### 3.2. مدیریت داده
- **SupabaseManager**: ارتباط با سرویس Supabase
- **DatabaseManager**: مدیریت پایگاه داده محلی
- **SyncManager**: همگام‌سازی داده‌های آنلاین و آفلاین
- **CacheManager**: مدیریت کش داده‌ها

### 3.3. مدیریت کسب و کار
- **InvoiceManager**: مدیریت فاکتورها
- **CustomerManager**: مدیریت مشتریان
- **ProductManager**: مدیریت محصولات
- **PaymentManager**: مدیریت پرداخت‌ها
- **InstallmentManager**: مدیریت اقساط
- **InventoryManager**: مدیریت موجودی
- **ReportManager**: مدیریت گزارش‌ها
- **SellerManager**: مدیریت فروشندگان
- **SettlementManager**: مدیریت تسویه‌ها

### 3.4. مدیریت رابط کاربری
- **NavigationManager**: مدیریت ناوبری در برنامه
- **ThemeManager**: مدیریت تم و رنگ‌بندی
- **LocaleManager**: مدیریت زبان و محلی‌سازی
- **NotificationManager**: مدیریت اعلان‌ها
- **PrintManager**: مدیریت چاپ
- **ExportManager**: خروجی گرفتن از داده‌ها
- **ChartManager**: نمایش نمودارها
- **ValidationManager**: اعتبارسنجی داده‌ها

## 4. دیاگرام ارتباط بین صفحات اصلی

```
╔════════════════╗      ╔════════════════╗      ╔════════════════╗
║  LoginActivity ║─────>║ DashboardActi. ║─────>║ ProfileActivity║
╚════╤═══════════╝      ╚════╤═══════════╝      ╚════════════════╝
     │                       │
     │                       ├─────────────┬─────────────┬─────────────┐
     │                       │             │             │             │
╔════╧═══════════╗    ╔═════╧════════╗   ╔╧═══════════╗ ╔╧═══════════╗ ╔╧═══════════╗
║RegisterActivity║    ║SalesInvoiceAc.║   ║CustomerAct.║ ║ProductAct. ║ ║ReportAct.  ║
╚════════════════╝    ╚═════╤════════╝   ╚═════╤══════╝ ╚═════╤══════╝ ╚═════╤══════╝
                            │                  │              │              │
                      ╔═════╧════════╗   ╔═════╧══════╗ ╔═════╧══════╗ ╔═════╧══════╗
                      ║InvoiceDetailsA║   ║CustomerDet.║ ║ProductDet. ║ ║SalesReport ║
                      ╚═════╤════════╝   ╚════════════╝ ╚════════════╝ ╚════════════╝
                            │
                      ╔═════╧════════╗
                      ║PaymentActivity║
                      ╚════════════════╝
```

## 5. گردش کار اصلی برنامه

### 5.1. فروش محصول
```
DashboardActivity → SalesInvoiceActivity → CustomerSelectionActivity → ProductSelectionActivity → PaymentSelectionActivity → InvoiceDetailsActivity
```

### 5.2. مدیریت مشتریان
```
DashboardActivity → CustomerActivity → CustomerDetailsActivity → CustomerEditActivity
```

### 5.3. مدیریت پرداخت‌ها
```
DashboardActivity → PaymentListActivity → PaymentActivity → PaymentDetailsActivity
```

### 5.4. گزارش‌گیری
```
DashboardActivity → ReportActivity → SalesReportActivity/FinancialReportActivity/InventoryReportActivity
```

## 6. طراحی گرافیکی و رابط کاربری

### 6.1. تم‌ها و رنگ‌بندی
- **تم اصلی**: Material Design با رنگ‌بندی زیر:
  * رنگ اصلی (Primary): `#2196F3` (آبی)
  * رنگ ثانویه (Secondary): `#FF4081` (صورتی)
  * رنگ پس‌زمینه: `#FFFFFF` (سفید)
  * رنگ سطح: `#F5F5F5` (خاکستری روشن)
  * رنگ متن اصلی: `#212121` (خاکستری تیره)
  * رنگ متن ثانویه: `#757575` (خاکستری متوسط)
  * رنگ تأکید: `#FF9800` (نارنجی)
  * رنگ خطا: `#F44336` (قرمز)
  * رنگ موفقیت: `#4CAF50` (سبز)

- **تم تاریک**:
  * رنگ پس‌زمینه: `#121212` (خاکستری تیره)
  * رنگ سطح: `#1E1E1E` (خاکستری)
  * رنگ متن اصلی: `#FFFFFF` (سفید)
  * رنگ متن ثانویه: `#B0B0B0` (خاکستری روشن)

- **تمپلیت‌های سفارشی**:
  * تم فروشگاهی: تم آبی-نارنجی با تأکید بر قابلیت خوانایی
  * تم مدیریتی: تم آبی-سبز با تأکید بر ساختار گزارش‌ها
  * تم مالی: تم سبز-آبی با تأکید بر نمایش اعداد و مقادیر

### 6.2. فونت‌ها
- **فونت اصلی**: IRANSans
  * وزن‌های موجود: UltraLight, Light, Regular, Medium, Bold
  * اندازه‌های استاندارد:
    - عنوان اصلی: 20sp
    - عنوان فرعی: 18sp
    - عنوان بخش: 16sp
    - متن معمولی: 14sp
    - متن توضیحات: 12sp
    - متن کوچک: 10sp

- **فونت اعداد**: IRANYekan
  * استفاده در مبالغ، تاریخ، شماره و آمار
  * سازگار با فونت اصلی

- **استایل‌های متن**:
  * سرتیتر: Bold، اندازه 20sp
  * عنوان منو: Medium، اندازه 16sp
  * متن دکمه: Medium، اندازه 14sp
  * برچسب فیلد: Regular، اندازه 12sp
  * پیام خطا: Regular، اندازه 12sp، رنگ قرمز

### 6.3. انیمیشن‌ها و افکت‌ها
- **انیمیشن‌های انتقال صفحه**:
  * Slide: انتقال افقی بین صفحات اصلی
  * Fade: انتقال با محوشدگی در دیالوگ‌ها
  * Scale: تغییر اندازه در کارت‌های اطلاعات
  * Shared Element: انتقال با اجزای مشترک بین لیست و جزئیات

- **انیمیشن‌های آیتم**:
  * Ripple Effect: افکت موج‌دار برای لمس دکمه‌ها
  * Elevation Change: تغییر سایه در حالت انتخاب
  * Color Transition: تغییر رنگ تدریجی در وضعیت‌ها
  * Rotate: چرخش آیکون‌ها در حالت بارگذاری

- **انیمیشن‌های بارگذاری**:
  * Circular Progress: نمایش پیشرفت بارگذاری
  * Shimmer Effect: افکت درخشش در حالت اسکلتون لودینگ
  * Pulsing: ضربان برای نمایش وضعیت در انتظار

### 6.4. کلاس‌های کنترل‌کننده رابط کاربری
- **ThemeManager**: مدیریت تم و حالت روز/شب
  * `applyTheme(ThemeType)`: اعمال تم
  * `toggleDarkMode(boolean)`: تغییر حالت شب
  * `getThemePalette()`: دریافت پالت رنگ تم فعلی

- **FontManager**: مدیریت فونت‌ها
  * `applyFont(FontType)`: اعمال فونت
  * `setTextSize(TextSize)`: تنظیم اندازه متن
  * `getFontTypeface(FontWeight)`: دریافت فونت با وزن مشخص

- **AnimationManager**: مدیریت انیمیشن‌ها
  * `playTransitionAnimation(View, AnimType)`: اجرای انیمیشن انتقال
  * `playLoadingAnimation(View)`: اجرای انیمیشن بارگذاری
  * `applyClickEffect(View)`: اعمال افکت کلیک

- **DesignSystemManager**: سیستم طراحی یکپارچه
  * `setupUIComponents()`: راه‌اندازی اجزای رابط کاربری
  * `applyComponentStyles()`: اعمال استایل‌های استاندارد
  * `refreshUIElements()`: بروزرسانی عناصر رابط کاربری

## 7. پیاده‌سازی و اجتناب از خطاها

### 7.1. پیاده‌سازی صحیح منابع رابط کاربری
- **ساختار فایل‌های منابع**:
  * `/res/values/colors.xml`: تعریف تمام رنگ‌های استفاده شده در برنامه
  * `/res/values/themes.xml`: تعریف تم‌های روشن
  * `/res/values-night/themes.xml`: تعریف تم‌های تاریک
  * `/res/values/dimens.xml`: تعریف اندازه‌های استاندارد
  * `/res/values/styles.xml`: تعریف استایل‌های مختلف
  * `/res/values/attrs.xml`: تعریف ویژگی‌های سفارشی
  * `/res/font/`: دایرکتوری فونت‌ها

- **استفاده صحیح از منابع**:
  * استفاده از ارجاع به منابع به جای مقادیر هارد‌کد شده: `@color/primary` به جای `#2196F3`
  * تعریف استایل‌های استفاده مجدد به جای تکرار ویژگی‌ها
  * استفاده از ابعاد (dimens) برای اندازه‌ها به جای مقادیر ثابت

### 7.2. اجتناب از خطاهای رایج
- **خطاهای فونت**:
  * اطمینان از وجود فایل‌های فونت در دایرکتوری `/res/font/`
  * استفاده از Typeface.create به جای بارگذاری مستقیم
  * پشتیبانی از فونت‌های جایگزین در صورت عدم بارگذاری فونت اصلی
  * استفاده از Downloadable Fonts API برای کاهش حجم برنامه

- **خطاهای انیمیشن**:
  * استفاده از AnimatorSet برای انیمیشن‌های پیچیده
  * مدیریت صحیح چرخه حیات انیمیشن‌ها (شروع، توقف، لغو)
  * استفاده از Transition API برای انیمیشن‌های بین صفحه‌ای
  * استفاده از Motion Layout برای انیمیشن‌های پیچیده UI

- **خطاهای تم**:
  * استفاده از AppCompatActivity برای پشتیبانی از تم‌ها در نسخه‌های قدیمی
  * تنظیم صحیح تم در AndroidManifest.xml
  * استفاده از ThemeOverlay برای تغییرات جزئی در تم
  * تنظیم dayNight mode صحیح برای تم تاریک

### 7.3. راهنمای پیاده‌سازی ویژگی‌های خاص

- **پیاده‌سازی تم تاریک**:
```kotlin
// تنظیم تم در onCreate
override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    
    // خواندن تنظیمات کاربر
    val prefs = PreferenceManager.getDefaultSharedPreferences(this)
    when (prefs.getString("theme_mode", "system")) {
        "light" -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        "dark" -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        else -> AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
    }
    
    setContentView(R.layout.activity_main)
}
```

- **بارگذاری فونت سفارشی**:
```kotlin
// تنظیم فونت سفارشی
class FontManager(private val context: Context) {
    private val iranSansRegular by lazy {
        ResourcesCompat.getFont(context, R.font.iransans_regular)
    }
    
    private val iranSansBold by lazy {
        ResourcesCompat.getFont(context, R.font.iransans_bold)
    }
    
    fun applyFontToView(view: TextView, isBold: Boolean = false) {
        view.typeface = if (isBold) iranSansBold else iranSansRegular
    }
    
    fun applyFontToViewGroup(viewGroup: ViewGroup) {
        for (i in 0 until viewGroup.childCount) {
            val child = viewGroup.getChildAt(i)
            when (child) {
                is TextView -> applyFontToView(child)
                is ViewGroup -> applyFontToViewGroup(child)
            }
        }
    }
}
```

- **پیاده‌سازی انیمیشن‌های انتقال**:
```kotlin
// انیمیشن انتقال بین اکتیویتی‌ها
class AnimationHelper {
    companion object {
        fun applyTransitionAnimation(activity: Activity) {
            activity.overridePendingTransition(
                R.anim.slide_in_right,
                R.anim.slide_out_left
            )
        }
        
        fun applyFadeAnimation(activity: Activity) {
            activity.overridePendingTransition(
                R.anim.fade_in,
                R.anim.fade_out
            )
        }
    }
}

// استفاده در اکتیویتی
override fun startActivity(intent: Intent) {
    super.startActivity(intent)
    AnimationHelper.applyTransitionAnimation(this)
}
```

### 7.4. تست رابط کاربری
- **تست سازگاری**:
  * تست در اندازه‌های مختلف صفحه
  * تست در دستگاه‌های مختلف و API‌های مختلف
  * تست در حالت landscape و portrait
  * تست در حالت تم روشن و تاریک

- **تست عملکرد**:
  * تست انیمیشن‌ها برای روان بودن
  * تست بارگذاری فونت‌ها
  * تست تغییر تم در زمان اجرا
  * تست سازگاری با حالت چند زبانه

- **تست دسترسی‌پذیری**:
  * تست با TalkBack برای خوانش صفحه
  * تست کنتراست رنگ‌ها
  * تست افزایش اندازه فونت
  * تست با ابزارهای تحلیل دسترسی‌پذیری

## 8. اجزای رابط کاربری و پیاده‌سازی آنها

### 8.1. اجزای پایه
- **دکمه‌ها**:
  * `PrimaryButton`: دکمه اصلی با رنگ پس‌زمینه آبی و متن سفید
  * `SecondaryButton`: دکمه ثانویه با حاشیه آبی و متن آبی
  * `TextButton`: دکمه متنی بدون پس‌زمینه و حاشیه
  * `IconButton`: دکمه دارای آیکون در کنار متن
  * `FloatingActionButton`: دکمه شناور در گوشه صفحه

- **فیلدهای ورودی**:
  * `DefaultEditText`: فیلد متنی استاندارد
  * `SearchEditText`: فیلد جستجو با آیکون
  * `PasswordEditText`: فیلد رمز عبور با نمایش/مخفی‌سازی
  * `NumberEditText`: فیلد ویژه اعداد با قالب‌بندی خودکار
  * `DateEditText`: فیلد تاریخ با انتخابگر تقویم

- **لیست‌ها و گرید‌ها**:
  * `DefaultRecyclerView`: لیست استاندارد با امکان اسکرول عمودی
  * `HorizontalRecyclerView`: لیست افقی برای نمایش موارد افقی
  * `GridRecyclerView`: نمایش آیتم‌ها در حالت گرید
  * `PaginatedRecyclerView`: لیست با بارگذاری صفحه‌ای
  * `ExpandableRecyclerView`: لیست با امکان بازشوندگی آیتم‌ها

- **کارت‌ها و پنل‌ها**:
  * `InfoCard`: کارت اطلاعات با سایه و گرد‌گوشه
  * `StatusCard`: کارت نمایش وضعیت
  * `StatisticsCard`: کارت آمار با نمایش نمودار
  * `ExpandablePanel`: پنل قابل بازشوندگی
  * `NavigationDrawer`: منوی کشویی کناری

### 8.2. پیاده‌سازی سفارشی کامپوننت‌ها

- **`PrimaryButton.kt`**:
```kotlin
class PrimaryButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatButton(context, attrs, defStyleAttr) {

    init {
        // تنظیم استایل و پیکربندی
        background = ContextCompat.getDrawable(context, R.drawable.bg_primary_button)
        setTextColor(ContextCompat.getColor(context, R.color.white))
        typeface = ResourcesCompat.getFont(context, R.font.iransans_medium)
        isAllCaps = false
        
        // اعمال پدینگ استاندارد
        val horizontalPadding = resources.getDimensionPixelSize(R.dimen.button_horizontal_padding)
        val verticalPadding = resources.getDimensionPixelSize(R.dimen.button_vertical_padding)
        setPadding(horizontalPadding, verticalPadding, horizontalPadding, verticalPadding)
        
        // اعمال افکت ریپل
        elevation = resources.getDimension(R.dimen.button_elevation)
        stateListAnimator = AnimatorInflater.loadStateListAnimator(context, R.animator.button_state_animator)
    }
    
    override fun onDraw(canvas: Canvas?) {
        // برای پشتیبانی از فونت راست به چپ
        if (ViewCompat.getLayoutDirection(this) == ViewCompat.LAYOUT_DIRECTION_RTL) {
            canvas?.scale(-1f, 1f, width.toFloat() / 2f, height.toFloat() / 2f)
        }
        super.onDraw(canvas)
    }
}
```

- **`DefaultEditText.kt`**:
```kotlin
class DefaultEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatEditText(context, attrs, defStyleAttr) {

    init {
        background = ContextCompat.getDrawable(context, R.drawable.bg_edit_text)
        typeface = ResourcesCompat.getFont(context, R.font.iransans_regular)
        
        // تنظیم پدینگ و اندازه متن
        val padding = resources.getDimensionPixelSize(R.dimen.edit_text_padding)
        setPadding(padding, padding, padding, padding)
        textSize = resources.getDimension(R.dimen.text_size_normal)
        
        // اعمال رنگ متن
        setTextColor(ContextCompat.getColor(context, R.color.text_primary))
        setHintTextColor(ContextCompat.getColor(context, R.color.text_hint))
        
        // پشتیبانی از RTL
        textDirection = View.TEXT_DIRECTION_RTL
        textAlignment = View.TEXT_ALIGNMENT_VIEW_START
    }
    
    override fun onTextChanged(text: CharSequence?, start: Int, lengthBefore: Int, lengthAfter: Int) {
        super.onTextChanged(text, start, lengthBefore, lengthAfter)
        // حذف فاصله‌های اضافی در ابتدا و انتها
        if (text.toString().isNotEmpty() && (text.toString().startsWith(" ") || text.toString().endsWith(" "))) {
            val trimmed = text.toString().trim()
            if (trimmed != text.toString()) {
                setText(trimmed)
                setSelection(trimmed.length)
            }
        }
    }
}
```

- **`InfoCard.kt`**:
```kotlin
class InfoCard @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : CardView(context, attrs, defStyleAttr) {

    private val titleTextView: TextView
    private val contentTextView: TextView
    private val iconImageView: ImageView

    init {
        // تنظیم پارامترهای کارت
        radius = resources.getDimension(R.dimen.card_corner_radius)
        cardElevation = resources.getDimension(R.dimen.card_elevation)
        setCardBackgroundColor(ContextCompat.getColor(context, R.color.card_background))
        
        // تورم لایه داخلی
        val view = LayoutInflater.from(context).inflate(R.layout.layout_info_card, this, true)
        
        // یافتن ویوها
        titleTextView = view.findViewById(R.id.tv_card_title)
        contentTextView = view.findViewById(R.id.tv_card_content)
        iconImageView = view.findViewById(R.id.iv_card_icon)
        
        // اعمال فونت‌ها
        titleTextView.typeface = ResourcesCompat.getFont(context, R.font.iransans_bold)
        contentTextView.typeface = ResourcesCompat.getFont(context, R.font.iransans_regular)
        
        // اعمال ویژگی‌های سفارشی
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.InfoCard)
        try {
            val title = typedArray.getString(R.styleable.InfoCard_cardTitle)
            val content = typedArray.getString(R.styleable.InfoCard_cardContent)
            val iconRes = typedArray.getResourceId(R.styleable.InfoCard_cardIcon, 0)
            
            titleTextView.text = title
            contentTextView.text = content
            if (iconRes != 0) {
                iconImageView.setImageResource(iconRes)
                iconImageView.visibility = View.VISIBLE
            } else {
                iconImageView.visibility = View.GONE
            }
        } finally {
            typedArray.recycle()
        }
    }
    
    fun setTitle(title: String) {
        titleTextView.text = title
    }
    
    fun setContent(content: String) {
        contentTextView.text = content
    }
    
    fun setIcon(resId: Int) {
        iconImageView.setImageResource(resId)
        iconImageView.visibility = if (resId != 0) View.VISIBLE else View.GONE
    }
}
```

### 8.3. تعریف منابع UI

- **`colors.xml`**:
```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- رنگ‌های اصلی برنامه -->
    <color name="primary">#2196F3</color>
    <color name="primary_dark">#1976D2</color>
    <color name="primary_light">#BBDEFB</color>
    <color name="accent">#FF4081</color>
    
    <!-- رنگ‌های متن -->
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_white">#FFFFFF</color>
    
    <!-- رنگ‌های پس‌زمینه -->
    <color name="background">#FFFFFF</color>
    <color name="card_background">#FFFFFF</color>
    <color name="surface">#F5F5F5</color>
    
    <!-- رنگ‌های وضعیت -->
    <color name="success">#4CAF50</color>
    <color name="warning">#FF9800</color>
    <color name="error">#F44336</color>
    <color name="info">#2196F3</color>
    
    <!-- رنگ‌های تم تاریک -->
    <color name="background_dark">#121212</color>
    <color name="surface_dark">#1E1E1E</color>
    <color name="text_primary_dark">#FFFFFF</color>
    <color name="text_secondary_dark">#B0B0B0</color>
</resources>
```

- **`dimens.xml`**:
```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- اندازه متن -->
    <dimen name="text_size_headline">20sp</dimen>
    <dimen name="text_size_title">18sp</dimen>
    <dimen name="text_size_subtitle">16sp</dimen>
    <dimen name="text_size_normal">14sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_micro">10sp</dimen>
    
    <!-- فاصله‌ها -->
    <dimen name="spacing_tiny">2dp</dimen>
    <dimen name="spacing_small">4dp</dimen>
    <dimen name="spacing_normal">8dp</dimen>
    <dimen name="spacing_medium">12dp</dimen>
    <dimen name="spacing_large">16dp</dimen>
    <dimen name="spacing_xlarge">24dp</dimen>
    <dimen name="spacing_xxlarge">32dp</dimen>
    
    <!-- اندازه‌های اجزا -->
    <dimen name="button_height">48dp</dimen>
    <dimen name="button_elevation">2dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    <dimen name="button_horizontal_padding">16dp</dimen>
    <dimen name="button_vertical_padding">12dp</dimen>
    
    <dimen name="edit_text_height">48dp</dimen>
    <dimen name="edit_text_padding">12dp</dimen>
    <dimen name="edit_text_corner_radius">8dp</dimen>
    
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_padding">16dp</dimen>
    
    <dimen name="icon_size_small">16dp</dimen>
    <dimen name="icon_size_normal">24dp</dimen>
    <dimen name="icon_size_large">32dp</dimen>
</resources>
```

- **`themes.xml` (روشن)**:
```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.Sharen" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- رنگ‌های اصلی -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/text_white</item>
        
        <!-- رنگ‌های سیستم -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:navigationBarColor">@color/primary_dark</item>
        
        <!-- رنگ‌های پس‌زمینه -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorOnBackground">@color/text_primary</item>
        
        <!-- تنظیمات فونت -->
        <item name="android:fontFamily">@font/iransans_regular</item>
        <item name="fontFamily">@font/iransans_regular</item>
        <item name="android:textDirection">rtl</item>
        <item name="android:layoutDirection">rtl</item>
        
        <!-- اجزای سفارشی -->
        <item name="buttonStyle">@style/Widget.Sharen.Button</item>
        <item name="editTextStyle">@style/Widget.Sharen.EditText</item>
        <item name="cardViewStyle">@style/Widget.Sharen.CardView</item>
    </style>
</resources>
```

## 9. جزئیات اصلی ساختار اپلیکیشن

### 9.1. معماری کلی سیستم
- **معماری MVVM**: اپلیکیشن بر پایه معماری Model-View-ViewModel پیاده‌سازی شده است
  * `Model`: مدیریت داده‌ها و منطق تجاری توسط Repository ها
  * `View`: اکتیویتی‌ها و فرگمنت‌ها
  * `ViewModel`: مدیریت منطق نمایش و ارتباط View با Repository

- **معماری لایه‌ای**:
  * `لایه نمایش`: اکتیویتی‌ها، فرگمنت‌ها و Adapter ها
  * `لایه منطق تجاری`: ViewModel ها، UseCase ها و Service ها
  * `لایه داده`: Repository ها، منابع داده محلی و ریموت
  * `لایه زیرساخت`: ارتباط با API، پایگاه داده محلی، مدیریت کش

- **مدیریت وابستگی**:
  * استفاده از Dagger Hilt برای تزریق وابستگی
  * مدیریت دسترسی‌ها در تمام لایه‌ها از طریق DI

### 9.2. سیستم حسابداری و مالی

- **سیستم فاکتور**:
  * `فاکتور فروش`: ثبت فروش محصولات به مشتریان
  * `فاکتور خرید`: ثبت خرید محصولات از تأمین‌کنندگان
  * `پیش‌فاکتور`: ایجاد پیش‌فاکتور قبل از نهایی‌سازی فروش
  * `فاکتور برگشتی`: ثبت کالای برگشتی از مشتری
  * `صورتحساب‌ها`: گزارش‌های مالی بر اساس فاکتورها

- **سیستم حساب‌ها**:
  * `حساب مشتری`: نگهداری سوابق مالی مشتریان
  * `حساب فروشنده`: نگهداری حساب فروشندگان و کمیسیون‌ها
  * `حساب صندوق`: مدیریت موجودی نقدی
  * `حساب بانکی`: مدیریت حساب‌های بانکی
  * `صورت مغایرت`: کنترل و مغایرت‌گیری حساب‌ها

- **مدیریت پرداخت‌ها**:
  * `پرداخت نقدی`: ثبت پرداخت‌های نقدی
  * `پرداخت اعتباری`: مدیریت پرداخت‌های اعتباری
  * `پرداخت اقساطی`: مدیریت پرداخت‌های قسطی و چک‌ها
  * `تخفیفات`: مدیریت تخفیفات در سطح محصول، فاکتور و مشتری
  * `مالیات و عوارض`: محاسبه خودکار مالیات بر ارزش افزوده

- **گزارش‌های مالی**:
  * `سود و زیان`: محاسبه سود و زیان در بازه‌های زمانی مختلف
  * `گردش حساب`: نمایش گردش مالی حساب‌های مختلف
  * `مانده حساب مشتریان`: گزارش بدهکاری یا بستانکاری مشتریان
  * `وضعیت چک‌ها`: گزارش چک‌های دریافتی و پرداختی
  * `گزارش فروش`: تحلیل فروش بر اساس محصول، دسته‌بندی و فروشنده

### 9.3. ویژگی‌های خاص فروشگاه پوشاک

- **مدیریت محصولات پوشاک**:
  * `سایزبندی`: مدیریت سایزهای مختلف برای هر محصول
  * `رنگ‌بندی`: مدیریت رنگ‌های متنوع برای هر محصول
  * `مدل‌ها`: دسته‌بندی محصولات بر اساس مدل و طرح
  * `برندها`: مدیریت برندهای مختلف
  * `فصل‌ها`: دسته‌بندی محصولات بر اساس فصل فروش

- **انبارداری تخصصی پوشاک**:
  * `کاردکس سایز و رنگ`: نگهداری موجودی به تفکیک سایز و رنگ
  * `سری‌دوزی`: مدیریت سری‌های تولید محصولات
  * `ورود و خروج کالا`: ثبت ورود و خروج به تفکیک سایز و رنگ
  * `جابجایی بین انبارها`: انتقال کالا بین انبارهای مختلف
  * `کنترل موجودی`: هشدار کمبود موجودی و موجودی بحرانی

- **فرآیندهای فروش پوشاک**:
  * `پکیج محصولات`: فروش مجموعه‌ای از محصولات مرتبط
  * `تخفیفات فصلی`: مدیریت حراج‌های فصلی
  * `تخفیفات پلکانی`: تخفیف براساس تعداد خرید
  * `هدایای تبلیغاتی`: مدیریت محصولات هدیه همراه خرید
  * `فروش ویژه`: مدیریت فروش‌های ویژه مناسبتی

- **بازاریابی و مشتری‌مداری**:
  * `باشگاه مشتریان`: امتیازدهی و پاداش برای مشتریان وفادار
  * `کارت تخفیف`: صدور کارت‌های تخفیف برای مشتریان خاص
  * `سیستم ارجاع`: پاداش برای معرفی مشتریان جدید
  * `کمپین‌های تبلیغاتی`: مدیریت کمپین‌های تخفیف و تبلیغات
  * `پیام‌های اطلاع‌رسانی`: ارسال پیامک تبلیغاتی به مشتریان

### 9.4. انواع کاربران و سطوح دسترسی

- **مدیر سیستم**:
  * دسترسی کامل به تمام بخش‌های سیستم
  * مدیریت کاربران و تعیین سطوح دسترسی
  * دسترسی به گزارش‌های مالی و مدیریتی
  * تنظیمات کلی سیستم و پیکربندی
  * پشتیبان‌گیری و بازیابی اطلاعات

- **مدیر فروشگاه**:
  * مدیریت محصولات، قیمت‌گذاری و موجودی
  * بررسی عملکرد فروشندگان
  * مدیریت تخفیفات و کمپین‌های فروش
  * دسترسی به گزارش‌های فروش و موجودی
  * تأیید نهایی فاکتورهای فروش ویژه

- **حسابدار**:
  * ثبت و مدیریت تراکنش‌های مالی
  * دسترسی به حساب‌های مشتریان و فروشندگان
  * مدیریت چک‌ها و اقساط
  * تهیه گزارش‌های مالی و صورت‌حساب‌ها
  * تسویه حساب با فروشندگان

- **فروشنده**:
  * ثبت فاکتور فروش به مشتریان
  * مشاهده موجودی محصولات
  * مدیریت مشتریان خود
  * ثبت سفارش و پیش‌فاکتور
  * مشاهده کمیسیون و عملکرد فروش خود

- **انباردار**:
  * مدیریت ورود و خروج کالا
  * کنترل موجودی انبار
  * ثبت درخواست موجودی
  * جابجایی کالا بین انبارها
  * ثبت مرجوعی کالا

- **کاربر مهمان**:
  * مشاهده کاتالوگ محصولات
  * امکان ثبت پیش‌سفارش
  * مشاهده وضعیت سفارش خود
  * دسترسی به باشگاه مشتریان
  * مشاهده تخفیف‌ها و پیشنهادات ویژه

### 9.5. قابلیت‌های کلیدی سیستم

- **فروش و ارتباط با مشتری**:
  * فروش آنلاین و حضوری یکپارچه
  * امکان پیش‌سفارش محصولات
  * ارتباط با سیستم پیامکی برای اطلاع‌رسانی
  * استفاده از بارکدخوان برای ثبت سریع محصولات
  * سیستم CRM برای مدیریت مشتریان

- **حسابداری و مالی**:
  * اتصال به سیستم مالیاتی و ارسال خودکار صورتحساب‌ها
  * اتصال به دستگاه‌های کارتخوان
  * پشتیبانی از شعب متعدد فروشگاه
  * حساب متمرکز برای شعب مختلف
  * تسویه خودکار با فروشندگان

- **مدیریت انبار**:
  * سیستم FIFO و LIFO در انبارداری
  * اعلان برای کالاهای دارای موجودی کم
  * تنظیم سفارش خودکار برای موجودی کم
  * پیش‌بینی موجودی بر اساس تحلیل فروش
  * بارکدگذاری خودکار محصولات

- **گزارش‌گیری و داشبورد**:
  * نمودارهای تعاملی برای تحلیل فروش
  * گزارش‌های زمان‌بندی شده و خودکار
  * سیستم هشدار برای وضعیت‌های خاص
  * اکسپورت گزارش‌ها به فرمت‌های مختلف
  * ارسال خودکار گزارش‌های دوره‌ای

- **امنیت و کارایی**:
  * همگام‌سازی خودکار داده‌ها با سرور
  * امکان کار آفلاین و همگام‌سازی بعدی
  * رمزگذاری داده‌های حساس
  * ثبت تاریخچه تغییرات (Audit Log)
  * پشتیبان‌گیری خودکار از داده‌ها

### 9.6. معماری فنی اپلیکیشن

- **فناوری‌های اصلی**:
  * `زبان برنامه‌نویسی`: Kotlin
  * `پلتفرم`: Android (API Level 21+)
  * `پایگاه داده محلی`: Room (SQLite)
  * `پایگاه داده ابری`: Supabase (PostgreSQL)
  * `مدیریت وابستگی‌ها`: Gradle، Dagger Hilt

- **کتابخانه‌های اصلی**:
  * `Jetpack`: LiveData، ViewModel، Room، WorkManager
  * `ارتباط شبکه`: Retrofit، OkHttp
  * `Reactive`: Kotlin Coroutines، Flow
  * `UI`: Material Components، MPAndroidChart، Glide
  * `تست`: JUnit، Espresso، Mockito

- **معماری چندلایه**:
```
┌─────────────────────────────────────────────────────┐
│                  لایه‌های اپلیکیشن                  │
├─────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────┐ │
│ │             UI Layer (لایه رابط کاربری)        │ │
│ │                                                 │ │
│ │  Activities / Fragments / Adapters / Composables│ │
│ └─────────────────┬───────────────────────────────┘ │
│                   │                                 │
│ ┌─────────────────▼───────────────────────────────┐ │
│ │      Domain Layer (لایه منطق تجاری)            │ │
│ │                                                 │ │
│ │         ViewModels / UseCases / Services        │ │
│ └─────────────────┬───────────────────────────────┘ │
│                   │                                 │
│ ┌─────────────────▼───────────────────────────────┐ │
│ │         Data Layer (لایه داده)                 │ │
│ │                                                 │ │
│ │               Repositories                      │ │
│ └─────────────────┬───────────────────────────────┘ │
│                   │                                 │
│ ┌─────────────────▼───────────────────────────────┐ │
│ │      Data Sources (منابع داده)                 │ │
│ │                                                 │ │
│ │  Local DB (Room) / Remote API (Supabase) / Cache│ │
│ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────┘
```

- **الگوهای طراحی**:
  * `Repository Pattern`: جداسازی منطق دسترسی به داده
  * `Factory Pattern`: ساخت اشیاء پیچیده
  * `Observer Pattern`: واکنش به تغییرات داده با LiveData/Flow
  * `Adapter Pattern`: تبدیل اینترفیس‌ها در سیستم بازیافت‌پذیر
  * `Singleton Pattern`: منابع مشترک مانند دیتابیس

- **استراتژی مدیریت خطا**:
  * `خطایابی جامع`: ثبت و مدیریت خطاها در تمام لایه‌ها
  * `خطاهای شبکه`: مدیریت قطعی اینترنت و تلاش مجدد
  * `خطاهای بانک اطلاعاتی`: بازیابی در صورت خرابی دیتابیس
  * `خطاهای کاربری`: نمایش پیام‌های کاربرپسند
  * `سیستم گزارش خطا`: ارسال خودکار گزارش خطا به سرور