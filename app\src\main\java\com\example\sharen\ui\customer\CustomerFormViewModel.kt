package com.example.sharen.ui.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Customer
import com.example.sharen.data.repository.CustomerRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CustomerFormViewModel @Inject constructor(
    private val customerRepository: CustomerRepository
) : ViewModel() {

    // Form data
    private val _isEditMode = MutableLiveData<Boolean>(false)
    val isEditMode: LiveData<Boolean> = _isEditMode

    private val _customer = MutableLiveData<Customer?>()
    val customer: LiveData<Customer?> = _customer

    // UI state
    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    private val _saveSuccess = MutableLiveData<Boolean>(false)
    val saveSuccess: LiveData<Boolean> = _saveSuccess

    fun loadCustomer(customerId: String?) {
        customerId ?: return

        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            customerRepository.getCustomerById(customerId)
                .catch { e ->
                    _error.postValue(e.message ?: "خطا در بارگذاری اطلاعات مشتری")
                    _isLoading.postValue(false)
                }
                .collectLatest { customer ->
                    _customer.postValue(customer)
                    _isEditMode.postValue(true)
                    _isLoading.postValue(false)
                }
        }
    }

    fun saveCustomer(
        name: String,
        phone: String,
        address: String?,
        notes: String?
    ) {
        if (!validateInputs(name, phone)) {
            return
        }

        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                val result = if (_isEditMode.value == true && _customer.value != null) {
                    // Update existing customer
                    val updatedCustomer = _customer.value!!.copy(
                        name = name,
                        phone = phone,
                        address = address,
                        notes = notes
                    )
                    customerRepository.updateCustomer(updatedCustomer)
                } else {
                    // Create new customer
                    val newCustomer = Customer(
                        id = "", // Repository will generate ID
                        userId = "", // Repository will set current user ID
                        name = name,
                        phone = phone,
                        address = address,
                        notes = notes
                    )
                    customerRepository.createCustomer(newCustomer)
                }

                result.onSuccess {
                    _saveSuccess.value = true
                    _isLoading.value = false
                }.onFailure { e ->
                    _error.value = e.message ?: "خطا در ذخیره اطلاعات مشتری"
                    _isLoading.value = false
                }
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در ذخیره اطلاعات مشتری"
                _isLoading.value = false
            }
        }
    }

    private fun validateInputs(name: String, phone: String): Boolean {
        var isValid = true

        if (name.isBlank()) {
            _error.value = "نام مشتری الزامی است"
            isValid = false
        }

        if (phone.isBlank()) {
            _error.value = "شماره تلفن مشتری الزامی است"
            isValid = false
        }

        return isValid
    }
}