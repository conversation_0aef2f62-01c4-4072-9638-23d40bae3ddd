package com.example.sharen.di

import android.content.Context
import androidx.room.Room
import com.example.sharen.data.local.SharenDatabase
import com.example.sharen.data.local.dao.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideSharenDatabase(
        @ApplicationContext context: Context
    ): SharenDatabase {
        return SharenDatabase.getDatabase(context)
    }

    @Provides
    @Singleton
    fun provideUserDao(database: SharenDatabase): UserDao {
        return database.userDao()
    }

    @Provides
    @Singleton
    fun provideCustomerDao(database: SharenDatabase): CustomerDao {
        return database.customerDao()
    }

    @Provides
    @Singleton
    fun provideSellerDao(database: SharenDatabase): SellerDao {
        return database.sellerDao()
    }

    @Provides
    @Singleton
    fun provideProductDao(database: SharenDatabase): ProductDao {
        return database.productDao()
    }

    @Provides
    @Singleton
    fun provideCategoryDao(database: SharenDatabase): CategoryDao {
        return database.categoryDao()
    }

    @Provides
    @Singleton
    fun provideInvoiceDao(database: SharenDatabase): InvoiceDao {
        return database.invoiceDao()
    }

    @Provides
    @Singleton
    fun provideInvoiceItemDao(database: SharenDatabase): InvoiceItemDao {
        return database.invoiceItemDao()
    }

    @Provides
    @Singleton
    fun providePaymentDao(database: SharenDatabase): PaymentDao {
        return database.paymentDao()
    }

    @Provides
    @Singleton
    fun provideInstallmentDao(database: SharenDatabase): InstallmentDao {
        return database.installmentDao()
    }
}