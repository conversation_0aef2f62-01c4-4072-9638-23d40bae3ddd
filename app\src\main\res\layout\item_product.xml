<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <ImageView
            android:id="@+id/ivProductImage"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:contentDescription="@string/product_image"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@android:drawable/ic_menu_gallery" />

        <TextView
            android:id="@+id/tvProductName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivProductImage"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="نام محصول" />

        <TextView
            android:id="@+id/tvProductCategory"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvProductName"
            app:layout_constraintTop_toBottomOf="@+id/tvProductName"
            tools:text="دسته‌بندی محصول" />

        <TextView
            android:id="@+id/tvProductCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/tvProductName"
            app:layout_constraintTop_toBottomOf="@+id/tvProductCategory"
            tools:text="کد: SH-001" />

        <TextView
            android:id="@+id/tvProductPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@android:color/holo_green_dark"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@+id/tvProductName"
            app:layout_constraintTop_toBottomOf="@+id/tvProductCode"
            tools:text="250,000 تومان" />

        <TextView
            android:id="@+id/tvStockStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/holo_green_light"
            android:paddingStart="8dp"
            android:paddingTop="2dp"
            android:paddingEnd="8dp"
            android:paddingBottom="2dp"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/tvProductPrice"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvProductPrice"
            tools:text="موجود" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView> 