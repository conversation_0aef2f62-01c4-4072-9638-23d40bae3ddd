package com.example.sharen.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.example.sharen.core.base.BaseViewModel
import com.example.sharen.domain.model.Customer
import com.example.sharen.domain.usecase.customer.GetAllCustomersUseCase
import com.example.sharen.domain.usecase.customer.GetCustomerByIdUseCase
import com.example.sharen.domain.usecase.customer.SearchCustomersUseCase
import com.example.sharen.domain.usecase.customer.AddCustomerUseCase
import com.example.sharen.domain.usecase.customer.UpdateCustomerUseCase
import com.example.sharen.domain.usecase.customer.DeleteCustomerUseCase
import com.example.sharen.domain.usecase.customer.GetDebtorCustomersUseCase
import com.example.sharen.domain.usecase.customer.UpdateCustomerCreditUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای مدیریت مشتریان
 */
@HiltViewModel
class CustomerViewModel @Inject constructor(
    private val getAllCustomersUseCase: GetAllCustomersUseCase,
    private val getCustomerByIdUseCase: GetCustomerByIdUseCase,
    private val searchCustomersUseCase: SearchCustomersUseCase,
    private val addCustomerUseCase: AddCustomerUseCase,
    private val updateCustomerUseCase: UpdateCustomerUseCase,
    private val deleteCustomerUseCase: DeleteCustomerUseCase,
    private val getDebtorCustomersUseCase: GetDebtorCustomersUseCase,
    private val updateCustomerCreditUseCase: UpdateCustomerCreditUseCase
) : BaseViewModel() {

    private val _customers = MutableStateFlow<List<Customer>>(emptyList())
    val customers: StateFlow<List<Customer>> = _customers.asStateFlow()

    private val _selectedCustomer = MutableStateFlow<Customer?>(null)
    val selectedCustomer: StateFlow<Customer?> = _selectedCustomer.asStateFlow()

    init {
        loadCustomers()
    }

    /**
     * بارگذاری لیست مشتریان
     */
    fun loadCustomers() {
        viewModelScope.launch {
            getAllCustomersUseCase().collect { customerList ->
                _customers.value = customerList
            }
        }
    }

    /**
     * جستجوی مشتریان
     */
    fun searchCustomers(query: String) {
        viewModelScope.launch {
            searchCustomersUseCase(query).collect { customerList ->
                _customers.value = customerList
            }
        }
    }

    /**
     * دریافت مشتری با شناسه
     */
    fun getCustomerById(customerId: String) {
        launchWithLoading {
            val customer = _customers.value.find { it.id == customerId }
            _selectedCustomer.value = customer

            if (customer == null) {
                showError("مشتری یافت نشد")
            }
        }
    }

    /**
     * افزودن مشتری جدید
     */
    fun addCustomer(
        userId: String,
        name: String,
        phone: String,
        address: String? = null,
        creditLimit: Long = 0,
        notes: String? = null
    ) {
        launchWithLoading {
            val result = addCustomerUseCase(
                userId = userId,
                name = name,
                phone = phone,
                address = address,
                creditLimit = creditLimit,
                notes = notes
            )

            result.fold(
                onSuccess = { customer ->
                    showSuccess("مشتری جدید با موفقیت اضافه شد")
                    loadCustomers() // بارگذاری مجدد لیست
                },
                onFailure = { error ->
                    showError(error.message ?: "خطا در افزودن مشتری")
                }
            )
        }
    }

    /**
     * بروزرسانی مشتری
     */
    fun updateCustomer(customer: Customer) {
        launchWithLoading {
            // شبیه‌سازی بروزرسانی در دیتابیس
            kotlinx.coroutines.delay(1000)

            val currentList = _customers.value.toMutableList()
            val index = currentList.indexOfFirst { it.id == customer.id }

            if (index != -1) {
                currentList[index] = customer.copy(updatedAt = Date())
                _customers.value = currentList
                _selectedCustomer.value = currentList[index]
                showSuccess("اطلاعات مشتری با موفقیت بروزرسانی شد")
            } else {
                showError("مشتری یافت نشد")
            }
        }
    }

    /**
     * حذف مشتری
     */
    fun deleteCustomer(customerId: String) {
        launchWithLoading {
            // شبیه‌سازی حذف از دیتابیس
            kotlinx.coroutines.delay(1000)

            val currentList = _customers.value.toMutableList()
            val removed = currentList.removeAll { it.id == customerId }

            if (removed) {
                _customers.value = currentList
                showSuccess("مشتری با موفقیت حذف شد")
            } else {
                showError("مشتری یافت نشد")
            }
        }
    }

    /**
     * بروزرسانی اعتبار مشتری
     */
    fun updateCustomerCredit(customerId: String, newCreditLimit: Long) {
        launchWithLoading {
            val currentList = _customers.value.toMutableList()
            val index = currentList.indexOfFirst { it.id == customerId }

            if (index != -1) {
                val updatedCustomer = currentList[index].copy(
                    creditLimit = newCreditLimit,
                    updatedAt = Date()
                )
                currentList[index] = updatedCustomer
                _customers.value = currentList
                _selectedCustomer.value = updatedCustomer
                showSuccess("حد اعتبار مشتری بروزرسانی شد")
            } else {
                showError("مشتری یافت نشد")
            }
        }
    }

    /**
     * بروزرسانی داده‌ها
     */
    fun refreshCustomers() {
        loadCustomers()
    }

    /**
     * فیلتر مشتریان بدهکار
     */
    fun getDebtorCustomers() {
        launchWithLoading(showLoading = false) {
            val allCustomers = _customers.value
            val debtorCustomers = allCustomers.filter { it.totalDebt > 0 }
            _customers.value = debtorCustomers
        }
    }

    /**
     * فیلتر مشتریان بدون بدهی
     */
    fun getClearCustomers() {
        launchWithLoading(showLoading = false) {
            val allCustomers = _customers.value
            val clearCustomers = allCustomers.filter { it.totalDebt == 0L }
            _customers.value = clearCustomers
        }
    }
}
