package com.example.sharen.data.model

import java.util.Date
import java.util.UUID

/**
 * مدل داده آیتم سفارش
 */
data class OrderItem(
    val id: String = UUID.randomUUID().toString(),
    val orderId: String,                  // شناسه سفارش
    val productId: String,                // شناسه محصول
    val quantity: Int = 1,                // تعداد
    val unitPrice: Long = 0,              // قیمت واحد
    val discountAmount: Long = 0,         // مبلغ تخفیف
    val totalAmount: Long = 0,            // مبلغ کل
    val notes: String? = null,            // توضیحات
    val createdAt: Date = Date(),         // تاریخ ایجاد
    val updatedAt: Date = Date()          // تاریخ بروزرسانی
) {
    val discountPercentage: Int get() = 
        if (unitPrice > 0) ((discountAmount * 100) / (unitPrice * quantity)).toInt() else 0
        
    val finalUnitPrice: Long get() = unitPrice - (discountAmount / quantity)
} 