package com.example.sharen.domain.usecase.payment

import com.example.sharen.domain.model.Payment
import com.example.sharen.domain.model.PaymentMethod
import com.example.sharen.domain.model.PaymentStatus
import com.example.sharen.domain.repository.PaymentRepository
import com.example.sharen.domain.repository.CustomerRepository
import java.util.Date
import java.util.UUID
import javax.inject.Inject

/**
 * Use Case برای ایجاد پرداخت جدید
 */
class CreatePaymentUseCase @Inject constructor(
    private val paymentRepository: PaymentRepository,
    private val customerRepository: CustomerRepository
) {
    suspend operator fun invoke(
        customerId: String,
        amount: Double,
        method: PaymentMethod,
        invoiceId: String? = null,
        description: String? = null,
        referenceNumber: String? = null
    ): Result<Payment> {

        // اعتبارسنجی ورودی‌ها
        if (customerId.isBlank()) {
            return Result.failure(IllegalArgumentException("شناسه مشتری نمی‌تواند خالی باشد"))
        }

        if (amount <= 0) {
            return Result.failure(IllegalArgumentException("مبلغ پرداخت باید مثبت باشد"))
        }

        // بررسی وجود مشتری
        val customerResult = customerRepository.getCustomerById(customerId)
        if (customerResult.isFailure || customerResult.getOrNull() == null) {
            return Result.failure(IllegalArgumentException("مشتری یافت نشد"))
        }

        // ایجاد پرداخت
        val payment = Payment(
            id = UUID.randomUUID().toString(),
            customerId = customerId,
            invoiceId = invoiceId,
            amount = amount,
            date = Date(),
            method = method,
            status = PaymentStatus.COMPLETED,
            referenceNumber = referenceNumber?.trim(),
            notes = description?.trim()
        )

        // ذخیره پرداخت
        val saveResult = paymentRepository.addPayment(payment)
        if (saveResult.isFailure) {
            return saveResult
        }

        // بروزرسانی آمار مشتری (اختیاری)
        try {
            customerRepository.recordPayment(customerId, amount.toLong(), Date())
        } catch (e: Exception) {
            // در صورت خطا در بروزرسانی آمار، پرداخت را باز هم برگردان
        }

        return Result.success(payment)
    }
}
