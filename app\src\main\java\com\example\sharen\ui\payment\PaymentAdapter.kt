package com.example.sharen.ui.payment

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentMethod
import com.example.sharen.data.model.PaymentStatus
import com.example.sharen.databinding.ItemPaymentBinding
import com.example.sharen.util.DateUtils
import com.example.sharen.util.NumberUtils

class PaymentAdapter(
    private val onItemClick: (Payment) -> Unit
) : ListAdapter<Payment, PaymentAdapter.PaymentViewHolder>(PaymentDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PaymentViewHolder {
        val binding = ItemPaymentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PaymentViewHolder(binding)
    }

    override fun onBindViewHolder(holder: PaymentViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class PaymentViewHolder(
        private val binding: ItemPaymentBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(payment: Payment) {
            binding.apply {
                // Set payment amount
                amountTextView.text = root.context.getString(
                    R.string.payment_amount,
                    NumberUtils.formatCurrency(payment.amount)
                )

                // Set payment date
                dateTextView.text = root.context.getString(
                    R.string.payment_date,
                    DateUtils.formatDate(payment.date)
                )

                // Set payment method
                methodTextView.text = root.context.getString(
                    R.string.payment_method,
                    getPaymentMethodText(payment.method)
                )

                // Set reference number if available
                referenceTextView.text = payment.referenceNumber?.let {
                    root.context.getString(R.string.payment_reference, it)
                } ?: ""

                // Set notes if available
                notesTextView.text = payment.notes?.let {
                    root.context.getString(R.string.payment_notes, it)
                } ?: ""

                // Set status chip
                statusChip.text = getPaymentStatusText(payment.status)
                statusChip.setChipBackgroundColorResource(getPaymentStatusColor(payment.status))
            }
        }

        private fun getPaymentMethodText(method: PaymentMethod): String {
            return when (method) {
                PaymentMethod.CASH -> binding.root.context.getString(R.string.payment_method_cash)
                PaymentMethod.CARD -> binding.root.context.getString(R.string.payment_method_card)
                PaymentMethod.TRANSFER -> binding.root.context.getString(R.string.payment_method_transfer)
                PaymentMethod.CHECK -> binding.root.context.getString(R.string.payment_method_check)
                PaymentMethod.INSTALLMENT -> binding.root.context.getString(R.string.payment_method_installment)
                PaymentMethod.OTHER -> binding.root.context.getString(R.string.payment_method_other)
            }
        }

        private fun getPaymentStatusText(status: PaymentStatus): String {
            return when (status) {
                PaymentStatus.PENDING -> binding.root.context.getString(R.string.payment_status_pending)
                PaymentStatus.CONFIRMED -> binding.root.context.getString(R.string.payment_status_confirmed)
                PaymentStatus.REJECTED -> binding.root.context.getString(R.string.payment_status_rejected)
                PaymentStatus.COMPLETED -> binding.root.context.getString(R.string.payment_status_completed)
                PaymentStatus.FAILED -> binding.root.context.getString(R.string.payment_status_failed)
            }
        }

        private fun getPaymentStatusColor(status: PaymentStatus): Int {
            return when (status) {
                PaymentStatus.PENDING -> R.color.payment_status_pending
                PaymentStatus.CONFIRMED -> R.color.payment_status_confirmed
                PaymentStatus.REJECTED -> R.color.payment_status_rejected
                PaymentStatus.COMPLETED -> R.color.payment_status_completed
                PaymentStatus.FAILED -> R.color.payment_status_failed
            }
        }
    }

    private class PaymentDiffCallback : DiffUtil.ItemCallback<Payment>() {
        override fun areItemsTheSame(oldItem: Payment, newItem: Payment): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Payment, newItem: Payment): Boolean {
            return oldItem == newItem
        }
    }
} 