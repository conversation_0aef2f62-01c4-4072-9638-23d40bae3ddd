<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/navigation"
    app:startDestination="@id/dashboardFragment">

    <fragment
        android:id="@+id/dashboardFragment"
        android:name="com.example.sharen.ui.dashboard.DashboardFragment"
        android:label="@string/dashboard">
        <action
            android:id="@+id/action_dashboard_to_customers"
            app:destination="@id/customersFragment" />
        <action
            android:id="@+id/action_dashboard_to_products"
            app:destination="@id/productsFragment" />
        <action
            android:id="@+id/action_dashboard_to_invoices"
            app:destination="@id/invoicesFragment" />
    </fragment>

    <fragment
        android:id="@+id/customersFragment"
        android:name="com.example.sharen.ui.customers.CustomersFragment"
        android:label="@string/customers">
        <action
            android:id="@+id/action_customers_to_addCustomer"
            app:destination="@id/addCustomerFragment" />
        <action
            android:id="@+id/action_customers_to_customerDetails"
            app:destination="@id/customerDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/productsFragment"
        android:name="com.example.sharen.ui.products.ProductsFragment"
        android:label="@string/products">
        <action
            android:id="@+id/action_products_to_addProduct"
            app:destination="@id/addProductFragment" />
        <action
            android:id="@+id/action_products_to_productDetails"
            app:destination="@id/productDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/invoicesFragment"
        android:name="com.example.sharen.ui.invoices.InvoicesFragment"
        android:label="@string/invoices">
        <action
            android:id="@+id/action_invoices_to_addInvoice"
            app:destination="@id/addInvoiceFragment" />
        <action
            android:id="@+id/action_invoices_to_invoiceDetails"
            app:destination="@id/invoiceDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/addCustomerFragment"
        android:name="com.example.sharen.ui.customers.AddCustomerFragment"
        android:label="@string/add_new_customer" />

    <fragment
        android:id="@+id/customerDetailsFragment"
        android:name="com.example.sharen.ui.customers.CustomerDetailsFragment"
        android:label="@string/customer_details" />

    <fragment
        android:id="@+id/addProductFragment"
        android:name="com.example.sharen.ui.products.AddProductFragment"
        android:label="@string/add_new_product" />

    <fragment
        android:id="@+id/productDetailsFragment"
        android:name="com.example.sharen.ui.products.ProductDetailsFragment"
        android:label="@string/product_details" />

    <fragment
        android:id="@+id/addInvoiceFragment"
        android:name="com.example.sharen.ui.invoices.AddInvoiceFragment"
        android:label="@string/add_invoice" />

    <fragment
        android:id="@+id/invoiceDetailsFragment"
        android:name="com.example.sharen.ui.invoices.InvoiceDetailsFragment"
        android:label="@string/invoice_details" />

</navigation> 