package com.example.sharen.ui.profile

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class SecurityViewModel @Inject constructor() : ViewModel() {
    private val _biometricEnabled = MutableLiveData<Boolean>()
    val biometricEnabled: LiveData<Boolean> = _biometricEnabled

    private val _pinEnabled = MutableLiveData<Boolean>()
    val pinEnabled: LiveData<Boolean> = _pinEnabled

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    fun setBiometricEnabled(enabled: Boolean) {
        _biometricEnabled.value = enabled
    }

    fun setPinEnabled(enabled: Boolean) {
        _pinEnabled.value = enabled
    }
} 