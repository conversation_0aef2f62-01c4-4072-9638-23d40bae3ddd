package com.example.sharen.ui.admin

import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.databinding.ActivityUserManagementBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class UserManagementActivity : AppCompatActivity() {

    private lateinit var binding: ActivityUserManagementBinding
    private val viewModel: UserManagementViewModel by viewModels()
    private lateinit var adapter: UserAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUserManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupUI()
        setupObservers()
        
        viewModel.loadUsers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.user_management)
    }

    private fun setupUI() {
        // تنظیم RecyclerView
        adapter = UserAdapter(
            onUserClick = { user -> showUserDetails(user) },
            onApproveClick = { userId -> approveUser(userId) },
            onDeleteClick = { userId -> showDeleteConfirmation(userId) },
            onRoleClick = { userId -> showRoleSelectionDialog(userId) }
        )
        
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
        
        // تنظیم جستجو
        binding.etSearch.doAfterTextChanged { text ->
            viewModel.searchUsers(text.toString())
        }
        
        // تنظیم فیلتر نقش‌ها
        binding.chipAll.setOnClickListener {
            viewModel.filterByRole(null)
        }
        
        binding.chipAdmin.setOnClickListener {
            viewModel.filterByRole("ADMIN")
        }
        
        binding.chipManager.setOnClickListener {
            viewModel.filterByRole("MANAGER")
        }
        
        binding.chipSeller.setOnClickListener {
            viewModel.filterByRole("SELLER")
        }
        
        binding.chipCustomer.setOnClickListener {
            viewModel.filterByRole("CUSTOMER")
        }
        
        // تنظیم دکمه افزودن کاربر
        binding.fabAddUser.setOnClickListener {
            showAddUserDialog()
        }
    }
    
    private fun setupObservers() {
        viewModel.users.observe(this) { users ->
            adapter.submitList(users)
            binding.tvEmptyState.visibility = if (users.isEmpty()) android.view.View.VISIBLE else android.view.View.GONE
        }
        
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        }
        
        viewModel.error.observe(this) { error ->
            Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
        }
        
        viewModel.operationSuccess.observe(this) { message ->
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun showUserDetails(user: User) {
        // نمایش دیالوگ یا صفحه جزئیات کاربر
        val dialog = UserDetailsDialog(this, user)
        dialog.show()
    }
    
    private fun approveUser(userId: String) {
        viewModel.approveUser(userId)
    }
    
    private fun showDeleteConfirmation(userId: String) {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.delete_user))
            .setMessage(getString(R.string.delete_user_confirmation))
            .setPositiveButton(getString(R.string.yes)) { _, _ ->
                viewModel.deleteUser(userId)
            }
            .setNegativeButton(getString(R.string.no), null)
            .show()
    }
    
    private fun showRoleSelectionDialog(userId: String) {
        val roles = arrayOf(
            getString(R.string.admin),
            getString(R.string.manager),
            getString(R.string.seller),
            getString(R.string.customer)
        )
        
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.select_role))
            .setItems(roles) { _, which ->
                val selectedRole = when(which) {
                    0 -> "ADMIN"
                    1 -> "MANAGER"
                    2 -> "SELLER"
                    3 -> "CUSTOMER"
                    else -> "CUSTOMER"
                }
                viewModel.changeUserRole(userId, selectedRole)
            }
            .show()
    }
    
    private fun showAddUserDialog() {
        val dialog = UserFormDialog(this) { name, email, phone, role ->
            viewModel.createUser(name, email, phone, role)
        }
        dialog.show()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 