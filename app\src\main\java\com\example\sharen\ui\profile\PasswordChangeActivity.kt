package com.example.sharen.ui.profile

import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.example.sharen.R
import com.example.sharen.databinding.ActivityPasswordChangeBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PasswordChangeActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPasswordChangeBinding
    private val viewModel: PasswordChangeViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPasswordChangeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupUI()
        setupObservers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.change_password)
    }

    private fun setupUI() {
        binding.btnChangePassword.setOnClickListener {
            changePassword()
        }
    }

    private fun setupObservers() {
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
            binding.btnChangePassword.isEnabled = !isLoading
        }

        viewModel.success.observe(this) { success ->
            if (success) {
                Toast.makeText(this, getString(R.string.password_changed_successfully), Toast.LENGTH_SHORT).show()
                finish()
            }
        }

        viewModel.error.observe(this) { error ->
            Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
        }
    }

    private fun changePassword() {
        val currentPassword = binding.etCurrentPassword.text.toString()
        val newPassword = binding.etNewPassword.text.toString()
        val confirmPassword = binding.etConfirmPassword.text.toString()

        if (currentPassword.isEmpty()) {
            binding.etCurrentPassword.error = getString(R.string.required_field)
            return
        }

        if (newPassword.isEmpty()) {
            binding.etNewPassword.error = getString(R.string.required_field)
            return
        }

        if (newPassword.length < 6) {
            binding.etNewPassword.error = getString(R.string.error_password_short)
            return
        }

        if (confirmPassword.isEmpty()) {
            binding.etConfirmPassword.error = getString(R.string.required_field)
            return
        }

        if (newPassword != confirmPassword) {
            binding.etConfirmPassword.error = getString(R.string.error_passwords_not_match)
            return
        }

        viewModel.changePassword(currentPassword, newPassword)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 