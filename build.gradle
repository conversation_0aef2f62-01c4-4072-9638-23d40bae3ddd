buildscript {
    ext {
        compose_version = '1.5.4'
        kotlin_version = '1.9.22'
        room_version = '2.6.1'
        hilt_version = '2.50'
        nav_version = '2.7.7'
    }
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:$nav_version"
    }
}

plugins {
    id 'com.android.application' version '8.2.2' apply false
    id 'com.android.library' version '8.2.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.22' apply false
    id 'com.google.dagger.hilt.android' version '2.50' apply false
    id 'androidx.navigation.safeargs' version '2.7.7' apply false
}

// Remove all plugin applications from root build.gradle
// apply plugin: 'com.android.application'
// apply plugin: 'kotlin-kapt'
// apply plugin: 'dagger.hilt.android.plugin'
// apply plugin: 'androidx.navigation.safeargs'

// Remove android block and dependencies from root build.gradle
// android {
//     namespace 'com.example.sharen'
//     compileSdk 34
//     ...
// }

// dependencies {
//     ...
// } 





