package com.example.sharen.data.repository.impl

import com.example.sharen.data.local.dao.UserDao
import com.example.sharen.data.local.entity.UserEntity
import com.example.sharen.data.repository.UserRepository
import com.example.sharen.domain.model.User
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepositoryImpl @Inject constructor(
    private val userDao: UserDao
) : UserRepository {

    override fun getAllUsers(): Flow<List<User>> {
        return userDao.getAllUsers().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun getUserById(userId: String): Result<User?> {
        return try {
            val userEntity = userDao.getUserById(userId)
            Result.success(userEntity?.toDomainModel())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getCurrentUser(): Flow<User?> {
        return userDao.getCurrentUser().map { it?.toDomainModel() }
    }

    override suspend fun getCurrentUserId(): String? {
        return try {
            userDao.getUserById("current")?.id
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun addUser(user: User): Result<User> {
        return try {
            val userEntity = UserEntity.fromDomainModel(user)
            userDao.insertUser(userEntity)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateUser(user: User): Result<User> {
        return try {
            val userEntity = UserEntity.fromDomainModel(user)
            userDao.updateUser(userEntity)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteUser(userId: String): Result<Unit> {
        return try {
            userDao.deleteUser(userId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun searchUsers(query: String): Flow<List<User>> {
        return userDao.searchUsers(query).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
}
