package com.example.sharen.domain.repository

import com.example.sharen.domain.model.User
import com.example.sharen.domain.model.UserRole
import kotlinx.coroutines.flow.Flow

/**
 * Repository Interface برای مدیریت کاربران
 */
interface UserRepository {
    
    /**
     * ورود کاربر
     */
    suspend fun login(email: String, password: String): Result<User>
    
    /**
     * ثبت‌نام کاربر جدید
     */
    suspend fun register(
        email: String,
        password: String,
        name: String,
        phone: String,
        role: UserRole,
        referrerCode: String? = null
    ): Result<User>
    
    /**
     * خروج کاربر
     */
    suspend fun logout(): Result<Unit>
    
    /**
     * دریافت کاربر فعلی
     */
    suspend fun getCurrentUser(): Result<User?>
    
    /**
     * دریافت کاربر با شناسه
     */
    suspend fun getUserById(userId: String): Result<User?>
    
    /**
     * دریافت تمام کاربران
     */
    fun getAllUsers(): Flow<List<User>>
    
    /**
     * دریافت کاربران با نقش خاص
     */
    fun getUsersByRole(role: UserRole): Flow<List<User>>
    
    /**
     * جستجوی کاربران
     */
    fun searchUsers(query: String): Flow<List<User>>
    
    /**
     * بروزرسانی پروفایل کاربر
     */
    suspend fun updateProfile(user: User): Result<User>
    
    /**
     * تغییر رمز عبور
     */
    suspend fun changePassword(oldPassword: String, newPassword: String): Result<Unit>
    
    /**
     * تأیید کاربر (توسط ادمین)
     */
    suspend fun approveUser(userId: String): Result<Unit>
    
    /**
     * رد کاربر (توسط ادمین)
     */
    suspend fun rejectUser(userId: String, reason: String): Result<Unit>
    
    /**
     * حذف کاربر
     */
    suspend fun deleteUser(userId: String): Result<Unit>
    
    /**
     * بازیابی رمز عبور
     */
    suspend fun resetPassword(email: String): Result<Unit>
    
    /**
     * آپلود تصویر پروفایل
     */
    suspend fun uploadProfileImage(userId: String, imageData: ByteArray): Result<String>
    
    /**
     * دریافت کاربران در انتظار تأیید
     */
    fun getPendingUsers(): Flow<List<User>>
    
    /**
     * تولید کد معرف
     */
    suspend fun generateReferrerCode(userId: String): Result<String>
    
    /**
     * اعتبارسنجی کد معرف
     */
    suspend fun validateReferrerCode(code: String): Result<User?>
}
