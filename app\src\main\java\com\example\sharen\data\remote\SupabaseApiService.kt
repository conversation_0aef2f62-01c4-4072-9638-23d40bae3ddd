package com.example.sharen.data.remote

import com.example.sharen.data.model.*
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.*

interface SupabaseApiService {
    // Authentication
    @POST("auth/v1/signup")
    suspend fun signUp(@Body request: SignUpRequest): Response<AuthResponse>
    
    @POST("auth/v1/signin")
    suspend fun signIn(@Body request: SignInRequest): Response<AuthResponse>
    
    @POST("auth/v1/logout")
    suspend fun logout(): Response<Unit>
    
    // Users
    @GET("rest/v1/users")
    suspend fun getUsers(): Response<List<User>>
    
    @GET("rest/v1/users/{id}")
    suspend fun getUser(@Path("id") userId: String): Response<User>
    
    @PUT("rest/v1/users/{id}")
    suspend fun updateUser(@Path("id") userId: String, @Body user: User): Response<User>
    
    // Products
    @GET("rest/v1/products")
    suspend fun getProducts(): Response<List<Product>>
    
    @GET("rest/v1/products/{id}")
    suspend fun getProduct(@Path("id") productId: String): Response<Product>
    
    @POST("rest/v1/products")
    suspend fun createProduct(@Body product: Product): Response<Product>
    
    @PUT("rest/v1/products/{id}")
    suspend fun updateProduct(@Path("id") productId: String, @Body product: Product): Response<Product>
    
    @DELETE("rest/v1/products/{id}")
    suspend fun deleteProduct(@Path("id") productId: String): Response<Unit>
    
    // Customers
    @GET("rest/v1/customers")
    suspend fun getCustomers(): Response<List<Customer>>
    
    @GET("rest/v1/customers/{id}")
    suspend fun getCustomer(@Path("id") customerId: String): Response<Customer>
    
    @POST("rest/v1/customers")
    suspend fun createCustomer(@Body customer: Customer): Response<Customer>
    
    @PUT("rest/v1/customers/{id}")
    suspend fun updateCustomer(@Path("id") customerId: String, @Body customer: Customer): Response<Customer>
    
    // Orders
    @GET("rest/v1/orders")
    suspend fun getOrders(): Response<List<Order>>
    
    @GET("rest/v1/orders/{id}")
    suspend fun getOrder(@Path("id") orderId: String): Response<Order>
    
    @POST("rest/v1/orders")
    suspend fun createOrder(@Body order: Order): Response<Order>
    
    @PUT("rest/v1/orders/{id}")
    suspend fun updateOrder(@Path("id") orderId: String, @Body order: Order): Response<Order>
    
    // Payments
    @GET("rest/v1/payments")
    suspend fun getPayments(): Response<List<Payment>>
    
    @GET("rest/v1/payments/{id}")
    suspend fun getPayment(@Path("id") paymentId: String): Response<Payment>
    
    @POST("rest/v1/payments")
    suspend fun createPayment(@Body payment: Payment): Response<Payment>
    
    @PUT("rest/v1/payments/{id}")
    suspend fun updatePayment(@Path("id") paymentId: String, @Body payment: Payment): Response<Payment>

    @DELETE("rest/v1/payments/{id}")
    suspend fun deletePayment(@Path("id") paymentId: String): Response<Unit>
    
    // Installments
    @GET("rest/v1/installments")
    suspend fun getInstallments(): Response<List<Installment>>
    
    @GET("rest/v1/installments/{id}")
    suspend fun getInstallment(@Path("id") installmentId: String): Response<Installment>
    
    @POST("rest/v1/installments")
    suspend fun createInstallment(@Body installment: Installment): Response<Installment>
    
    @PUT("rest/v1/installments/{id}")
    suspend fun updateInstallment(@Path("id") installmentId: String, @Body installment: Installment): Response<Installment>
    
    // Reports
    @GET("rest/v1/reports")
    suspend fun getReports(): Response<List<Report>>
    
    @GET("rest/v1/reports/{id}")
    suspend fun getReport(@Path("id") reportId: String): Response<Report>
    
    @POST("rest/v1/reports")
    suspend fun createReport(@Body report: Report): Response<Report>

    @DELETE("rest/v1/reports/{id}")
    suspend fun deleteReport(@Path("id") reportId: String): Response<Unit>

    @GET("rest/v1/reports/{id}/export/pdf")
    suspend fun exportReportToPdf(@Path("id") reportId: String): Response<String>

    @GET("rest/v1/reports/{id}/export/excel")
    suspend fun exportReportToExcel(@Path("id") reportId: String): Response<String>

    @GET("rest/v1/reports/{id}/export/csv")
    suspend fun exportReportToCsv(@Path("id") reportId: String): Response<String>

    @POST("rest/v1/report_templates")
    suspend fun saveReportTemplate(@Body template: Report): Response<Report>

    @GET("rest/v1/report_templates/{id}")
    suspend fun getReportTemplate(@Path("id") templateId: String): Response<Report>

    @GET("rest/v1/report_templates")
    suspend fun getAllReportTemplates(): Response<List<Report>>

    @DELETE("rest/v1/report_templates/{id}")
    suspend fun deleteReportTemplate(@Path("id") templateId: String): Response<Unit>

    @PUT("rest/v1/reports/{id}")
    suspend fun updateReport(@Path("id") reportId: String, @Body report: Report): Response<Report>

    @GET("rest/v1/report_data")
    suspend fun getReportData(@Query("startDate") startDate: Long, @Query("endDate") endDate: Long): Response<ReportData>

    // Storage
    @Multipart
    @POST("storage/v1/object/{bucket}")
    suspend fun uploadFile(
        @Path("bucket") bucket: String,
        @Part("file") file: MultipartBody.Part
    ): Response<StorageResponse>
    
    @GET("storage/v1/object/{bucket}/{path}")
    suspend fun downloadFile(
        @Path("bucket") bucket: String,
        @Path("path") path: String
    ): Response<StorageResponse>
    
    @DELETE("storage/v1/object/{bucket}/{path}")
    suspend fun deleteFile(
        @Path("bucket") bucket: String,
        @Path("path") path: String
    ): Response<Unit>
}
