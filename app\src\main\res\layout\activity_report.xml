<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabMode="fixed"
            app:tabGravity="fill">

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/sales_report" />

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/financial_report" />

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/inventory_report" />

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/customer_report" />

        </com.google.android.material.tabs.TabLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvDateRange"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:textSize="16sp"
                android:layout_marginBottom="16dp" />

            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none">

                <com.google.android.material.chip.ChipGroup
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipToday"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/today" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipThisWeek"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/this_week" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipThisMonth"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/this_month" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipThisYear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/this_year" />

                </com.google.android.material.chip.ChipGroup>

            </HorizontalScrollView>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSelectDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/select_custom_date" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_marginTop="16dp">

                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/lineChart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone" />

                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/pieChart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone" />

                <com.github.mikephil.charting.charts.BarChart
                    android:id="@+id/barChart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone" />

                <com.github.mikephil.charting.charts.RadarChart
                    android:id="@+id/radarChart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone" />

            </FrameLayout>

            <TableLayout
                android:id="@+id/tableLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:stretchColumns="*" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnExport"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/export_report" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:menu="@menu/bottom_navigation_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 