package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.data.model.InvoiceItem

@Entity(
    tableName = "invoice_items",
    foreignKeys = [
        ForeignKey(
            entity = InvoiceEntity::class,
            parentColumns = ["id"],
            childColumns = ["invoiceId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.RESTRICT
        )
    ],
    indices = [
        Index("invoiceId"),
        Index("productId")
    ]
)
data class InvoiceItemEntity(
    @PrimaryKey
    val id: String,
    val invoiceId: String,
    val productId: String,
    val productName: String,
    val productCode: String?,
    val quantity: Int,
    val unitPrice: Long,
    val discount: Long,
    val tax: Long,
    val notes: String?,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toInvoiceItem(): InvoiceItem = InvoiceItem(
        id = id,
        invoiceId = invoiceId,
        productId = productId,
        productName = productName,
        productCode = productCode,
        quantity = quantity,
        unitPrice = unitPrice,
        discount = discount,
        tax = tax,
        notes = notes
    )
    
    companion object {
        fun fromInvoiceItem(invoiceItem: InvoiceItem): InvoiceItemEntity = InvoiceItemEntity(
            id = invoiceItem.id,
            invoiceId = invoiceItem.invoiceId,
            productId = invoiceItem.productId,
            productName = invoiceItem.productName,
            productCode = invoiceItem.productCode,
            quantity = invoiceItem.quantity,
            unitPrice = invoiceItem.unitPrice,
            discount = invoiceItem.discount,
            tax = invoiceItem.tax,
            notes = invoiceItem.notes,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
} 