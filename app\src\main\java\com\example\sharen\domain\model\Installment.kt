package com.example.sharen.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Domain Model برای قسط
 */
@Parcelize
data class Installment(
    val id: String,
    val invoiceId: String,
    val customerId: String,
    val installmentNumber: Int,
    val totalAmount: Double,
    val paidAmount: Double = 0.0,
    val remainingAmount: Double,
    val dueDate: Date,
    val paidDate: Date? = null,
    val status: InstallmentStatus = InstallmentStatus.PENDING,
    val notes: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) : Parcelable {
    
    /**
     * آیا قسط پرداخت شده؟
     */
    val isPaid: Boolean
        get() = status == InstallmentStatus.PAID
    
    /**
     * آیا قسط جزئی پرداخت شده؟
     */
    val isPartiallyPaid: Boolean
        get() = status == InstallmentStatus.PARTIALLY_PAID
    
    /**
     * آیا قسط معوق است؟
     */
    val isOverdue: Boolean
        get() = !isPaid && dueDate.before(Date())
    
    /**
     * تعداد روزهای معوقه
     */
    val overdueDays: Int
        get() {
            if (!isOverdue) return 0
            val today = Date()
            val diffTime = today.time - dueDate.time
            return (diffTime / (1000 * 60 * 60 * 24)).toInt()
        }
    
    /**
     * آیا قسط امروز سررسید است؟
     */
    val isDueToday: Boolean
        get() {
            val today = Date()
            val todayStr = android.text.format.DateFormat.format("yyyy-MM-dd", today)
            val dueDateStr = android.text.format.DateFormat.format("yyyy-MM-dd", dueDate)
            return todayStr == dueDateStr
        }
    
    /**
     * آیا قسط در هفته آینده سررسید است؟
     */
    val isDueSoon: Boolean
        get() {
            val today = Date()
            val weekLater = Date(today.time + (7 * 24 * 60 * 60 * 1000))
            return dueDate.after(today) && dueDate.before(weekLater)
        }
}

/**
 * وضعیت قسط
 */
enum class InstallmentStatus(val displayName: String) {
    PENDING("در انتظار پرداخت"),
    PARTIALLY_PAID("پرداخت جزئی"),
    PAID("پرداخت شده"),
    OVERDUE("معوق"),
    CANCELLED("لغو شده")
}
