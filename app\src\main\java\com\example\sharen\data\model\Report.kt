package com.example.sharen.data.model

import java.util.Date

data class Report(
    val id: String,
    val type: ReportType,
    val title: String,
    val description: String,
    val startDate: Date,
    val endDate: Date,
    val createdAt: Date,
    val createdBy: String,
    val data: Map<String, Any>,
    val isTemplate: Boolean = false
)

data class ReportFilter(
    val startDate: Date? = null,
    val endDate: Date? = null,
    val type: ReportType? = null,
    val createdBy: String? = null,
    val isTemplate: Boolean? = null
)

data class ReportExportOptions(
    val format: ExportFormat,
    val includeCharts: Boolean = true,
    val includeDetails: Boolean = true,
    val language: String = "fa"
)

enum class ExportFormat {
    PDF,
    EXCEL,
    CSV
} 