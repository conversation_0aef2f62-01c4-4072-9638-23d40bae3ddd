package com.example.sharen.presentation.ui.customer

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.example.sharen.R
import com.example.sharen.databinding.ActivityCustomerFormBinding
import com.example.sharen.domain.model.Customer
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class CustomerFormActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCustomerFormBinding
    private val viewModel: CustomerFormViewModel by viewModels()
    private var isEditMode = false

    companion object {
        private const val EXTRA_CUSTOMER_ID = "customer_id"

        fun newIntent(context: Context, customerId: String? = null): Intent {
            return Intent(context, CustomerFormActivity::class.java).apply {
                customerId?.let { putExtra(EXTRA_CUSTOMER_ID, it) }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCustomerFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val customerId = intent.getStringExtra(EXTRA_CUSTOMER_ID)
        isEditMode = customerId != null

        setupToolbar()
        setupClickListeners()
        observeState()

        if (isEditMode && customerId != null) {
            viewModel.loadCustomer(customerId)
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(
            if (isEditMode) R.string.edit_customer else R.string.add_customer
        )
    }

    private fun setupClickListeners() {
        binding.btnSave.setOnClickListener {
            saveCustomer()
        }
    }

    private fun saveCustomer() {
        val name = binding.etCustomerName.text.toString().trim()
        val phone = binding.etPhone.text.toString().trim()
        val email = binding.etEmail.text.toString().trim().takeIf { it.isNotEmpty() }
        val address = binding.etAddress.text.toString().trim().takeIf { it.isNotEmpty() }

        // Validation
        if (name.isEmpty()) {
            binding.etCustomerName.error = getString(R.string.required_field)
            return
        }

        if (phone.isEmpty()) {
            binding.etPhone.error = getString(R.string.required_field)
            return
        }

        if (isEditMode) {
            viewModel.updateCustomer(name, phone, email, address)
        } else {
            viewModel.createCustomer(name, phone, email, address)
        }
    }

    private fun observeState() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.state.collect { state ->
                    when (state) {
                        is CustomerFormState.Loading -> {
                            binding.btnSave.isEnabled = false
                            binding.progressBar.visibility = android.view.View.VISIBLE
                        }
                        is CustomerFormState.CustomerLoaded -> {
                            binding.btnSave.isEnabled = true
                            binding.progressBar.visibility = android.view.View.GONE
                            bindCustomerData(state.customer)
                        }
                        is CustomerFormState.Saved -> {
                            setResult(RESULT_OK)
                            finish()
                        }
                        is CustomerFormState.Error -> {
                            binding.btnSave.isEnabled = true
                            binding.progressBar.visibility = android.view.View.GONE
                            Snackbar.make(
                                binding.root,
                                state.message,
                                Snackbar.LENGTH_LONG
                            ).show()
                        }
                        else -> {
                            binding.btnSave.isEnabled = true
                            binding.progressBar.visibility = android.view.View.GONE
                        }
                    }
                }
            }
        }
    }

    private fun bindCustomerData(customer: Customer) {
        binding.apply {
            etCustomerName.setText(customer.name)
            etPhone.setText(customer.phone)
            etEmail.setText(customer.email ?: "")
            etAddress.setText(customer.address ?: "")
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}

// ViewModel State
sealed class CustomerFormState {
    object Initial : CustomerFormState()
    object Loading : CustomerFormState()
    data class CustomerLoaded(val customer: Customer) : CustomerFormState()
    object Saved : CustomerFormState()
    data class Error(val message: String) : CustomerFormState()
}
