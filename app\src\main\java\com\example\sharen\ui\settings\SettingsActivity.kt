package com.example.sharen.ui.settings

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.sharen.R
import com.example.sharen.databinding.ActivitySettingsBinding
import com.example.sharen.ui.profile.DisplaySettingsActivity
import com.example.sharen.ui.profile.NotificationSettingsActivity
import com.example.sharen.ui.profile.SecurityActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SettingsActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySettingsBinding
    private val viewModel: SettingsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupUI()
        setupObservers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.settings)
    }

    private fun setupUI() {
        // تنظیمات نمایش
        binding.layoutDisplaySettings.setOnClickListener {
            startActivity(Intent(this, DisplaySettingsActivity::class.java))
        }

        // تنظیمات اعلان‌ها
        binding.layoutNotificationSettings.setOnClickListener {
            startActivity(Intent(this, NotificationSettingsActivity::class.java))
        }

        // تنظیمات امنیتی
        binding.layoutSecuritySettings.setOnClickListener {
            startActivity(Intent(this, SecurityActivity::class.java))
        }

        // تهیه نسخه پشتیبان
        binding.layoutBackup.setOnClickListener {
            showBackupOptions()
        }

        // بازیابی اطلاعات
        binding.layoutRestore.setOnClickListener {
            showRestoreOptions()
        }
    }

    private fun setupObservers() {
        viewModel.backupSuccess.observe(this) { success ->
            if (success) {
                Toast.makeText(this, getString(R.string.backup_success), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.restoreSuccess.observe(this) { success ->
            if (success) {
                Toast.makeText(this, getString(R.string.restore_success), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.error.observe(this) { error ->
            Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        }
    }

    private fun showBackupOptions() {
        val options = arrayOf(
            getString(R.string.backup_to_device),
            getString(R.string.backup_to_cloud)
        )

        AlertDialog.Builder(this)
            .setTitle(getString(R.string.backup_options))
            .setItems(options) { _, which ->
                when(which) {
                    0 -> viewModel.backupToDevice()
                    1 -> viewModel.backupToCloud()
                }
            }
            .show()
    }

    private fun showRestoreOptions() {
        val options = arrayOf(
            getString(R.string.restore_from_device),
            getString(R.string.restore_from_cloud)
        )

        AlertDialog.Builder(this)
            .setTitle(getString(R.string.restore_options))
            .setItems(options) { _, which ->
                when(which) {
                    0 -> viewModel.restoreFromDevice()
                    1 -> viewModel.restoreFromCloud()
                }
            }
            .show()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}