package com.example.sharen.domain.usecase.payment

import com.example.sharen.domain.model.Payment
import com.example.sharen.domain.repository.PaymentRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use Case برای دریافت پرداخت‌های مشتری
 */
class GetPaymentsByCustomerUseCase @Inject constructor(
    private val paymentRepository: PaymentRepository
) {
    operator fun invoke(customerId: String): Flow<List<Payment>> {
        return paymentRepository.getPaymentsByCustomer(customerId)
    }
}
