package com.example.sharen.presentation.ui.dashboard

import android.content.Intent
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.sharen.core.base.BaseActivity
import com.example.sharen.databinding.ActivityDashboardBinding
import com.example.sharen.presentation.ui.customer.CustomerActivity
import com.example.sharen.presentation.ui.invoice.InvoiceActivity
import com.example.sharen.presentation.ui.product.ProductActivity
import com.example.sharen.presentation.ui.payment.PaymentActivity
import com.example.sharen.presentation.viewmodel.DashboardViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * صفحه داشبورد اصلی
 */
@AndroidEntryPoint
class DashboardActivity : BaseActivity<ActivityDashboardBinding>() {

    private val dashboardViewModel: DashboardViewModel by viewModels()

    override fun getViewBinding(): ActivityDashboardBinding {
        return ActivityDashboardBinding.inflate(layoutInflater)
    }

    override fun setupViews() {
        setupClickListeners()
        setupUI()
    }

    override fun observeData() {
        observeDashboardData()
    }

    private fun setupClickListeners() {
        binding.apply {
            // Stats Cards
            cardCustomers.setOnClickListener { navigateToCustomers() }
            cardProducts.setOnClickListener { navigateToProducts() }
            cardInvoices.setOnClickListener { navigateToInvoices() }
            cardPayments.setOnClickListener { navigateToPayments() }
            cardTotalSales.setOnClickListener { /* No specific navigation for total sales */ }
            cardTotalDebt.setOnClickListener { /* No specific navigation for total debt */ }

            // Quick Actions
            cardNewInvoice.setOnClickListener { navigateToNewInvoice() }
            cardAddCustomer.setOnClickListener { navigateToNewCustomer() }
            cardNewProduct.setOnClickListener { navigateToNewProduct() }
            cardReports.setOnClickListener { navigateToReports() }
        }
    }

    private fun setupUI() {
        // Set welcome text (assuming tvWelcome is in toolbar)
        binding.toolbar.tvWelcome.text = "خوش آمدید"
        
        // Set today's date (assuming tvDate is in toolbar)
        val today = java.text.SimpleDateFormat("yyyy/MM/dd", java.util.Locale("fa", "IR"))
            .format(java.util.Date())
        binding.toolbar.tvDate.text = today
    }

    private fun observeDashboardData() {
        lifecycleScope.launch {
            dashboardViewModel.dashboardStats.collect { stats ->
                updateStatsCards(stats)
            }
        }

        lifecycleScope.launch {
            dashboardViewModel.isLoading.collect { isLoading ->
                if (isLoading) {
                    showLoading()
                } else {
                    hideLoading()
                }
            }
        }
    }

    private fun updateStatsCards(stats: com.example.sharen.domain.model.DashboardStats) {
        binding.apply {
            // Customer Stats
            tvCustomersCount.text = stats.totalCustomers.toString()
            tvCustomersLabel.text = getString(R.string.total_customers)

            // Product Stats
            tvProductsCount.text = stats.totalProducts.toString()
            tvProductsLabel.text = getString(R.string.total_products)

            // Invoice Stats
            tvInvoicesCount.text = stats.monthlyInvoices.toString()
            tvInvoicesLabel.text = getString(R.string.total_invoices)

            // Payment Stats
            tvPaymentsCount.text = stats.monthlyPayments.toString() // This is total amount, not count
            tvPaymentsLabel.text = getString(R.string.payments)

            // Financial Stats
            tvTotalSales.text = stats.monthlySales.toString()
            tvTotalDebt.text = stats.totalDebt.toString()
        }
    }

    private fun navigateToCustomers() {
        val intent = Intent(this, CustomerActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToProducts() {
        val intent = Intent(this, ProductActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToInvoices() {
        val intent = Intent(this, InvoiceActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToPayments() {
        val intent = Intent(this, PaymentActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToNewInvoice() {
        val intent = Intent(this, InvoiceActivity::class.java)
        intent.putExtra("action", "new")
        startActivity(intent)
    }

    private fun navigateToNewCustomer() {
        val intent = Intent(this, CustomerActivity::class.java)
        intent.putExtra("action", "new")
        startActivity(intent)
    }

    private fun navigateToNewProduct() {
        val intent = Intent(this, ProductActivity::class.java)
        intent.putExtra("action", "new")
        startActivity(intent)
    }

    private fun navigateToReports() {
        // TODO: پیاده‌سازی صفحه گزارش‌ها
        showSuccess("صفحه گزارش‌ها به زودی اضافه می‌شود")
    }
}
