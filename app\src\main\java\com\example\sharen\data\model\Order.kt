package com.example.sharen.data.model

import java.util.Date
import java.util.UUID

/**
 * مدل داده سفارش
 */
data class Order(
    val id: String = UUID.randomUUID().toString(),
    val customerId: String,               // شناسه مشتری
    val sellerId: String? = null,         // شناسه فروشنده
    val totalAmount: Long = 0,            // مبلغ کل
    val discountAmount: Long = 0,         // مبلغ تخفیف
    val finalAmount: Long = 0,            // مبلغ نهایی
    val status: OrderStatus = OrderStatus.PENDING,  // وضعیت سفارش
    val notes: String? = null,            // توضیحات
    val createdAt: Date = Date(),         // تاریخ ایجاد
    val updatedAt: Date = Date()          // تاریخ بروزرسانی
) {
    val discountPercentage: Int get() = 
        if (totalAmount > 0) ((discountAmount * 100) / totalAmount).toInt() else 0
}

/**
 * وضعیت‌های مختلف سفارش
 */
enum class OrderStatus {
    PENDING,      // در انتظار پرداخت
    PAID,         // پرداخت شده
    PROCESSING,   // در حال پردازش
    SHIPPED,      // ارسال شده
    DELIVERED,    // تحویل داده شده
    CANCELLED,    // لغو شده
    REFUNDED      // مسترد شده
} 