package com.example.sharen.presentation.ui.auth

import android.content.Intent
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.sharen.core.base.BaseActivity
import com.example.sharen.core.utils.gone
import com.example.sharen.core.utils.visible
import com.example.sharen.databinding.ActivityRegisterBinding
import com.example.sharen.domain.model.UserRole
import com.example.sharen.presentation.viewmodel.AuthViewModel
import com.example.sharen.presentation.viewmodel.LoginState
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * صفحه ثبت‌نام کاربر
 */
@AndroidEntryPoint
class RegisterActivity : BaseActivity<ActivityRegisterBinding>() {

    private val authViewModel: AuthViewModel by viewModels()

    override fun getViewBinding(): ActivityRegisterBinding {
        return ActivityRegisterBinding.inflate(layoutInflater)
    }

    override fun setupViews() {
        setupClickListeners()
        setupUI()
    }

    override fun observeData() {
        observeLoginState()
        observeLoadingState()
        observeMessages()
    }

    private fun setupClickListeners() {
        binding.apply {
            btnRegister.setOnClickListener {
                performRegister()
            }

            tvLogin.setOnClickListener {
                navigateToLogin()
            }

            ivBack.setOnClickListener {
                finish()
            }
        }
    }

    private fun setupUI() {
        binding.apply {
            // فوکوس روی فیلد نام
            etName.requestFocus()
        }
    }

    private fun observeLoginState() {
        lifecycleScope.launch {
            authViewModel.loginState.collect { state ->
                when (state) {
                    is LoginState.Idle -> {
                        // حالت عادی
                    }
                    is LoginState.Loading -> {
                        showLoading()
                    }
                    is LoginState.Success -> {
                        hideLoading()
                        showSuccess("ثبت‌نام با موفقیت انجام شد")
                        navigateToLogin()
                    }
                    is LoginState.Error -> {
                        hideLoading()
                        showError(state.message)
                    }
                }
            }
        }
    }

    private fun observeLoadingState() {
        lifecycleScope.launch {
            authViewModel.isLoading.collect { isLoading ->
                if (isLoading) {
                    binding.progressBar.visible()
                    binding.btnRegister.isEnabled = false
                } else {
                    binding.progressBar.gone()
                    binding.btnRegister.isEnabled = true
                }
            }
        }
    }

    private fun observeMessages() {
        lifecycleScope.launch {
            authViewModel.error.collect { error ->
                error?.let {
                    showError(it)
                    authViewModel.clearError()
                }
            }
        }

        lifecycleScope.launch {
            authViewModel.success.collect { success ->
                success?.let {
                    showSuccess(it)
                    authViewModel.clearSuccess()
                }
            }
        }
    }

    private fun performRegister() {
        val name = binding.etName.text.toString().trim()
        val email = binding.etEmail.text.toString().trim()
        val phone = binding.etPhone.text.toString().trim()
        val password = binding.etPassword.text.toString()
        val confirmPassword = binding.etConfirmPassword.text.toString()
        val referrerCode = binding.etReferrerCode.text.toString().trim()

        // اعتبارسنجی
        if (!validateInputs(name, email, phone, password, confirmPassword)) {
            return
        }

        // انجام ثبت‌نام
        authViewModel.register(
            email = email,
            password = password,
            confirmPassword = confirmPassword,
            name = name,
            phone = phone,
            role = UserRole.CUSTOMER,
            referrerCode = referrerCode.takeIf { it.isNotEmpty() }
        )
    }

    private fun validateInputs(
        name: String,
        email: String,
        phone: String,
        password: String,
        confirmPassword: String
    ): Boolean {
        var isValid = true

        // اعتبارسنجی نام
        if (name.isEmpty()) {
            binding.etName.error = "نام را وارد کنید"
            isValid = false
        } else if (name.length < 2) {
            binding.etName.error = "نام باید حداقل 2 کاراکتر باشد"
            isValid = false
        }

        // اعتبارسنجی ایمیل
        if (email.isEmpty()) {
            binding.etEmail.error = "ایمیل را وارد کنید"
            isValid = false
        } else if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.error = "ایمیل معتبر نیست"
            isValid = false
        }

        // اعتبارسنجی شماره موبایل
        if (phone.isEmpty()) {
            binding.etPhone.error = "شماره موبایل را وارد کنید"
            isValid = false
        } else if (!phone.matches(Regex("^09\\d{9}$"))) {
            binding.etPhone.error = "شماره موبایل معتبر نیست"
            isValid = false
        }

        // اعتبارسنجی رمز عبور
        if (password.isEmpty()) {
            binding.etPassword.error = "رمز عبور را وارد کنید"
            isValid = false
        } else if (password.length < 6) {
            binding.etPassword.error = "رمز عبور باید حداقل 6 کاراکتر باشد"
            isValid = false
        }

        // اعتبارسنجی تکرار رمز عبور
        if (confirmPassword.isEmpty()) {
            binding.etConfirmPassword.error = "تکرار رمز عبور را وارد کنید"
            isValid = false
        } else if (password != confirmPassword) {
            binding.etConfirmPassword.error = "رمز عبور و تکرار آن یکسان نیستند"
            isValid = false
        }

        return isValid
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        startActivity(intent)
        finish()
    }

    override fun showLoading() {
        binding.progressBar.visible()
        binding.btnRegister.isEnabled = false
    }

    override fun hideLoading() {
        binding.progressBar.gone()
        binding.btnRegister.isEnabled = true
    }
}
