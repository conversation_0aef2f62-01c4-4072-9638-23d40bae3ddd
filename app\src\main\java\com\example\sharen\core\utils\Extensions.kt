package com.example.sharen.core.utils

import android.content.Context
import android.view.View
import android.widget.Toast
import androidx.fragment.app.Fragment
import java.text.DecimalFormat
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

/**
 * Extension Functions برای راحتی کار
 */

// View Extensions
fun View.visible() {
    visibility = View.VISIBLE
}

fun View.invisible() {
    visibility = View.INVISIBLE
}

fun View.gone() {
    visibility = View.GONE
}

fun View.isVisible(): Boolean = visibility == View.VISIBLE

fun View.isGone(): Boolean = visibility == View.GONE

// Context Extensions
fun Context.toast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(this, message, duration).show()
}

fun Context.longToast(message: String) {
    Toast.makeText(this, message, Toast.LENGTH_LONG).show()
}

// Fragment Extensions
fun Fragment.toast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    requireContext().toast(message, duration)
}

fun Fragment.longToast(message: String) {
    requireContext().longToast(message)
}

// Number Extensions
fun Long.toCurrency(): String {
    val formatter = DecimalFormat("#,###")
    return "${formatter.format(this)} تومان"
}

fun Double.toCurrency(): String {
    val formatter = DecimalFormat("#,###.##")
    return "${formatter.format(this)} تومان"
}

fun Int.toCurrency(): String {
    val formatter = DecimalFormat("#,###")
    return "${formatter.format(this)} تومان"
}

fun Long.toFormattedNumber(): String {
    val formatter = DecimalFormat("#,###")
    return formatter.format(this)
}

fun Double.toFormattedNumber(): String {
    val formatter = DecimalFormat("#,###.##")
    return formatter.format(this)
}

// Date Extensions
fun Date.toPersianDate(): String {
    val formatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa", "IR"))
    return formatter.format(this)
}

fun Date.toPersianDateTime(): String {
    val formatter = SimpleDateFormat("yyyy/MM/dd HH:mm", Locale("fa", "IR"))
    return formatter.format(this)
}

fun Date.toTime(): String {
    val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
    return formatter.format(this)
}

fun Long.toDate(): Date = Date(this)

fun Date.toTimestamp(): Long = this.time

// String Extensions
fun String.isValidEmail(): Boolean {
    return android.util.Patterns.EMAIL_ADDRESS.matcher(this).matches()
}

fun String.isValidPhone(): Boolean {
    return this.matches(Regex("^09\\d{9}$"))
}

fun String.toEnglishDigits(): String {
    val persianDigits = arrayOf("۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹")
    val englishDigits = arrayOf("0", "1", "2", "3", "4", "5", "6", "7", "8", "9")
    
    var result = this
    for (i in persianDigits.indices) {
        result = result.replace(persianDigits[i], englishDigits[i])
    }
    return result
}

fun String.toPersianDigits(): String {
    val englishDigits = arrayOf("0", "1", "2", "3", "4", "5", "6", "7", "8", "9")
    val persianDigits = arrayOf("۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹")
    
    var result = this
    for (i in englishDigits.indices) {
        result = result.replace(englishDigits[i], persianDigits[i])
    }
    return result
}

// Collection Extensions
fun <T> List<T>.isNotNullOrEmpty(): Boolean = !this.isNullOrEmpty()

fun <T> List<T>?.orEmpty(): List<T> = this ?: emptyList()

// Safe casting
inline fun <reified T> Any?.safeCast(): T? = this as? T
