package com.example.sharen.presentation.ui.customer

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.example.sharen.R
import com.example.sharen.databinding.ActivityCustomerDetailsBinding
import com.example.sharen.domain.model.Customer
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class CustomerDetailsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCustomerDetailsBinding
    private val viewModel: CustomerDetailsViewModel by viewModels()

    companion object {
        private const val EXTRA_CUSTOMER_ID = "customer_id"

        fun newIntent(context: Context, customerId: String): Intent {
            return Intent(context, CustomerDetailsActivity::class.java).apply {
                putExtra(EXTRA_CUSTOMER_ID, customerId)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCustomerDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupClickListeners()
        observeState()

        val customerId = intent.getStringExtra(EXTRA_CUSTOMER_ID)
        if (customerId != null) {
            viewModel.loadCustomer(customerId)
        } else {
            finish()
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.customer_details)
    }

    private fun setupClickListeners() {
        binding.fabEdit.setOnClickListener {
            val customerId = intent.getStringExtra(EXTRA_CUSTOMER_ID)
            if (customerId != null) {
                startActivity(CustomerFormActivity.newIntent(this, customerId))
            }
        }

        binding.fabNewInvoice.setOnClickListener {
            // TODO: Navigate to add payment
        }

        // TODO: Add click listener for viewing invoices if a corresponding view is added
    }

    private fun observeState() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.state.collect { state ->
                    when (state) {
                        is CustomerDetailsState.Loading -> {
                            binding.progressBar.visibility = android.view.View.VISIBLE
                            binding.contentLayout.visibility = android.view.View.GONE
                        }
                        is CustomerDetailsState.Success -> {
                            binding.progressBar.visibility = android.view.View.GONE
                            binding.contentLayout.visibility = android.view.View.VISIBLE
                            bindCustomerData(state.customer)
                        }
                        is CustomerDetailsState.Error -> {
                            binding.progressBar.visibility = android.view.View.GONE
                            Snackbar.make(
                                binding.root,
                                state.message,
                                Snackbar.LENGTH_LONG
                            ).show()
                        }
                    }
                }
            }
        }
    }

    private fun bindCustomerData(customer: Customer) {
        binding.apply {
            tvCustomerName.text = customer.name
            tvCustomerPhone.text = customer.phone
            tvCustomerEmail.text = customer.email ?: getString(R.string.not_available)
            tvCustomerAddress.text = customer.address ?: getString(R.string.not_available)

            // TODO: Load customer statistics
            tvTotalPurchases.text = "0"
            tvTotalPayments.text = "0"
            tvDebtAmount.text = "0"
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}

// ViewModel State
sealed class CustomerDetailsState {
    object Loading : CustomerDetailsState()
    data class Success(val customer: Customer) : CustomerDetailsState()
    data class Error(val message: String) : CustomerDetailsState()
}
