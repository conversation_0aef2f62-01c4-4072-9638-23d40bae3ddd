package com.example.sharen.data.local.dao

import androidx.room.*
import com.example.sharen.data.local.entity.InstallmentEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface InstallmentDao {
    @Query("SELECT * FROM installments")
    fun getAll(): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE id = :id")
    suspend fun getById(id: String): InstallmentEntity?

    @Query("SELECT * FROM installments WHERE customerId = :customerId")
    fun getByCustomerId(customerId: String): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE dueDate BETWEEN :startDate AND :endDate")
    fun getByDateRange(startDate: Long, endDate: Long): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE status = :status")
    fun getByStatus(status: String): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE dueDate > :currentTime ORDER BY dueDate ASC")
    fun getUpcoming(currentTime: Long = System.currentTimeMillis()): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE dueDate < :currentTime AND status != 'PAID' ORDER BY dueDate ASC")
    fun getOverdue(currentTime: Long = System.currentTimeMillis()): Flow<List<InstallmentEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(installment: InstallmentEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(installments: List<InstallmentEntity>)

    @Update
    suspend fun update(installment: InstallmentEntity)

    @Delete
    suspend fun delete(installment: InstallmentEntity)

    @Query("DELETE FROM installments WHERE id = :id")
    suspend fun deleteById(id: String)
}