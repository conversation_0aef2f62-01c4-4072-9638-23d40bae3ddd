<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.payment.PaymentActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="@string/new_payment"
        app:titleTextColor="@color/white" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- اطلاعات فاکتور -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/invoice_info"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"
                        android:background="@color/divider" />

                    <!-- شماره فاکتور -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/invoice_number"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvInvoiceNumber"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            tools:text="INV-123456" />
                    </LinearLayout>

                    <!-- نام مشتری -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/customer_name"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvCustomerName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            tools:text="علی رضایی" />
                    </LinearLayout>

                    <!-- تاریخ فاکتور -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/invoice_date"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvInvoiceDate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            tools:text="1402/10/15" />
                    </LinearLayout>

                    <!-- مبلغ کل -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/total_amount"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvTotalAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            tools:text="1,000,000 تومان" />
                    </LinearLayout>

                    <!-- مبلغ پرداخت شده -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/paid_amount"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvPaidAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            tools:text="500,000 تومان" />
                    </LinearLayout>

                    <!-- مبلغ باقیمانده -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/remaining_amount"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvRemainingAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:textColor="@color/red"
                            tools:text="500,000 تومان" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- فرم پرداخت -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/payment_info"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"
                        android:background="@color/divider" />

                    <!-- مبلغ پرداخت -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="@string/payment_amount">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- روش پرداخت -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/payment_method"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/spinnerPaymentMethod"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                    <!-- شماره مرجع -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="@string/reference_number">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etReferenceNumber"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- توضیحات -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="@string/notes">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etNotes"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:minLines="2" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- دکمه ثبت پرداخت -->
                    <Button
                        android:id="@+id/btnSubmitPayment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/submit_payment" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout> 