package com.example.sharen.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.example.sharen.data.local.entity.CustomerEntity
import com.example.sharen.data.local.relation.CustomerWithUser
import kotlinx.coroutines.flow.Flow

@Dao
interface CustomerDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomer(customer: CustomerEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomers(customers: List<CustomerEntity>)

    @Update
    suspend fun updateCustomer(customer: CustomerEntity)

    @Delete
    suspend fun deleteCustomer(customer: CustomerEntity)

    @Query("DELETE FROM customers WHERE id = :customerId")
    suspend fun deleteCustomer(customerId: String)

    @Query("UPDATE customers SET creditLimit = :creditLimit, updatedAt = :updatedAt WHERE id = :customerId")
    suspend fun updateCustomerCredit(customerId: String, creditLimit: Long, updatedAt: Long = System.currentTimeMillis())

    @Query("UPDATE customers SET totalDebt = :debtAmount, updatedAt = :updatedAt WHERE id = :customerId")
    suspend fun updateCustomerDebt(customerId: String, debtAmount: Long, updatedAt: Long = System.currentTimeMillis())

    @Query("SELECT * FROM customers WHERE id = :customerId")
    suspend fun getCustomerById(customerId: String): CustomerEntity?

    @Query("SELECT * FROM customers WHERE userId = :userId")
    suspend fun getCustomerByUserId(userId: String): CustomerEntity?

    @Query("SELECT * FROM customers ORDER BY name ASC")
    fun getAllCustomers(): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE userId = :sellerId ORDER BY name ASC")
    fun getCustomersBySeller(sellerId: String): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE name LIKE '%' || :query || '%' OR phone LIKE '%' || :query || '%' OR address LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchCustomers(query: String): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE totalDebt > 0 ORDER BY totalDebt DESC")
    fun getCustomersWithDebt(): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE totalDebt = 0 ORDER BY name ASC")
    fun getCustomersWithoutDebt(): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE (creditLimit - totalDebt) < (creditLimit * 0.2) ORDER BY (creditLimit - totalDebt) ASC")
    fun getCustomersWithLowCredit(): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    fun getCustomersByDateRange(startDate: Long, endDate: Long): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE lastPurchaseDate > :cutoffDate ORDER BY lastPurchaseDate DESC")
    fun getActiveCustomers(cutoffDate: Long): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE lastPurchaseDate < :cutoffDate OR lastPurchaseDate IS NULL ORDER BY lastPurchaseDate ASC")
    fun getInactiveCustomers(cutoffDate: Long): Flow<List<CustomerEntity>>

    @Query("SELECT * FROM customers WHERE totalPurchases > 0 ORDER BY totalPurchases DESC LIMIT :limit")
    fun getTopCustomers(limit: Int): Flow<List<CustomerEntity>>

    @Transaction
    @Query("SELECT * FROM customers WHERE id = :customerId")
    fun getCustomerWithUser(customerId: String): Flow<CustomerWithUser>

    @Transaction
    @Query("SELECT * FROM customers")
    fun getCustomersWithUsers(): Flow<List<CustomerWithUser>>

    @Query("DELETE FROM customers")
    suspend fun deleteAllCustomers()

    // برای تاریخچه خرید - این باید با جدول فاکتورها پیاده‌سازی شود
    @Query("SELECT i.id as invoiceId, i.finalAmount as amount, i.createdAt as date, i.status as status FROM invoices i WHERE i.customerId = :customerId ORDER BY i.createdAt DESC")
    fun getCustomerPurchaseHistory(customerId: String): Flow<List<CustomerPurchaseEntity>>
}

// Entity برای تاریخچه خرید
data class CustomerPurchaseEntity(
    val invoiceId: String,
    val amount: Long,
    val date: Long,
    val status: String
)