package com.example.sharen.ui.auth

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import com.example.sharen.R
import com.example.sharen.databinding.ActivitySplashBinding
import com.example.sharen.ui.dashboard.DashboardActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SplashActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySplashBinding
    private val delayTime: Long = 2000 // 2 seconds

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupSplashScreen()
    }

    private fun setupSplashScreen() {
        Handler(Looper.getMainLooper()).postDelayed({
            if (isUserLoggedIn()) {
                navigateToDashboard()
            } else {
                navigateToLogin()
            }
        }, delayTime)
    }

    private fun isUserLoggedIn(): Boolean {
        // Check if user is logged in from SharedPreferences or AuthManager
        // For now, we'll return false to always go to login screen
        return false
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }

    private fun navigateToDashboard() {
        val intent = Intent(this, DashboardActivity::class.java)
        startActivity(intent)
        finish()
    }
} 