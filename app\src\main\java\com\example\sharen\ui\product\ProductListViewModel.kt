package com.example.sharen.ui.product

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Product
import com.example.sharen.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProductListViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {

    // لیست محصولات
    private val _products = MutableLiveData<List<Product>>(emptyList())
    val products: LiveData<List<Product>> = _products

    // تعداد کل محصولات
    private val _productCount = MutableLiveData<Int>(0)
    val productCount: LiveData<Int> = _productCount

    // محصولات با موجودی کم
    private val _lowStockProducts = MutableLiveData<List<Product>>(emptyList())
    val lowStockProducts: LiveData<List<Product>> = _lowStockProducts

    // وضعیت بارگذاری
    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    // پیام خطا
    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    // کوئری جستجو
    private val _searchQuery = MutableLiveData<String>("")
    val searchQuery: LiveData<String> = _searchQuery

    // دسته‌بندی انتخاب شده
    private val _selectedCategory = MutableLiveData<String?>(null)
    val selectedCategory: LiveData<String?> = _selectedCategory

    init {
        loadProducts()
        loadProductCount()
        loadLowStockProducts()
    }

    fun loadProducts() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            when {
                // اگر دسته‌بندی انتخاب شده باشد
                _selectedCategory.value != null -> {
                    productRepository.getProductsByCategory(_selectedCategory.value!!)
                        .catch { e ->
                            _error.postValue(e.message ?: "خطا در بارگذاری محصولات")
                            _isLoading.postValue(false)
                        }
                        .collectLatest { products ->
                            _products.postValue(products)
                            _isLoading.postValue(false)
                        }
                }
                // اگر کوئری جستجو داشته باشیم
                _searchQuery.value?.isNotEmpty() == true -> {
                    productRepository.searchProducts(_searchQuery.value!!)
                        .catch { e ->
                            _error.postValue(e.message ?: "خطا در جستجوی محصولات")
                            _isLoading.postValue(false)
                        }
                        .collectLatest { products ->
                            _products.postValue(products)
                            _isLoading.postValue(false)
                        }
                }
                // حالت پیش‌فرض - نمایش همه محصولات
                else -> {
                    productRepository.getAllProducts()
                        .catch { e ->
                            _error.postValue(e.message ?: "خطا در بارگذاری محصولات")
                            _isLoading.postValue(false)
                        }
                        .collectLatest { products ->
                            _products.postValue(products)
                            _isLoading.postValue(false)
                        }
                }
            }
        }
    }

    private fun loadProductCount() {
        viewModelScope.launch {
            productRepository.getProductCount()
                .catch { /* خطا را نادیده می‌گیریم */ }
                .collectLatest { count ->
                    _productCount.postValue(count)
                }
        }
    }

    private fun loadLowStockProducts() {
        viewModelScope.launch {
            productRepository.getLowStockProducts()
                .catch { /* خطا را نادیده می‌گیریم */ }
                .collectLatest { products ->
                    _lowStockProducts.postValue(products)
                }
        }
    }

    fun searchProducts(query: String) {
        _searchQuery.value = query
        _selectedCategory.value = null
        loadProducts()
    }

    fun filterByCategory(category: String?) {
        _selectedCategory.value = category
        _searchQuery.value = ""
        loadProducts()
    }

    fun clearFilters() {
        _searchQuery.value = ""
        _selectedCategory.value = null
        loadProducts()
    }
} 