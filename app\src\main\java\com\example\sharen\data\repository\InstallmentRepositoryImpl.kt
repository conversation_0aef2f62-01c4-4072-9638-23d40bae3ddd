package com.example.sharen.data.repository

import com.example.sharen.domain.model.Installment
import com.example.sharen.domain.model.InstallmentStatus
import com.example.sharen.data.local.dao.InstallmentDao
import com.example.sharen.data.local.entity.InstallmentEntity
import kotlinx.coroutines.flow.map
import com.example.sharen.data.remote.InstallmentRemoteDataSource
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class InstallmentRepositoryImpl @Inject constructor(
    private val remoteDataSource: InstallmentRemoteDataSource,
    private val localDataSource: InstallmentDao
) : InstallmentRepository {

    override suspend fun createInstallment(installment: Installment): Result<Installment> {
        return remoteDataSource.createInstallment(installment)
    }

    override suspend fun updateInstallment(installment: Installment): Result<Installment> {
        return remoteDataSource.updateInstallment(installment)
    }

    override suspend fun deleteInstallment(id: String): Result<Unit> {
        return remoteDataSource.deleteInstallment(id)
    }

    override suspend fun getInstallment(id: String): Result<Installment?> {
        return remoteDataSource.getInstallment(id)
    }

    override fun getAllInstallments(): Flow<List<Installment>> {
        return remoteDataSource.getAllInstallments()
    }

    override fun getInstallmentsByCustomer(customerId: String): Flow<List<Installment>> {
        return remoteDataSource.getInstallmentsByCustomer(customerId)
    }

    override fun getInstallmentsByDateRange(startDate: Date, endDate: Date): Flow<List<Installment>> {
        return remoteDataSource.getInstallmentsByDateRange(startDate, endDate)
    }

    override fun getInstallmentsByStatus(status: InstallmentStatus): Flow<List<Installment>> {
        return remoteDataSource.getInstallmentsByStatus(status)
    }

    override suspend fun payInstallment(id: String, amount: Double): Result<Installment> {
        return remoteDataSource.payInstallment(id, amount)
    }

    override fun getUpcomingInstallments(): Flow<List<Installment>> {
        return localDataSource.getUpcoming().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getOverdueInstallments(): Flow<List<Installment>> {
        return localDataSource.getOverdue().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun getInstallmentStatistics(startDate: Date, endDate: Date): Map<String, Double> {
        return remoteDataSource.getInstallmentStatistics(startDate, endDate)
    }

    override suspend fun calculateRemainingAmount(id: String): Double {
        return remoteDataSource.calculateRemainingAmount(id)
    }

    override fun getCustomerInstallmentHistory(customerId: String): Flow<List<Installment>> {
        return localDataSource.getByCustomerId(customerId).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun generateInstallmentSchedule(
        totalAmount: Double,
        numberOfInstallments: Int,
        startDate: Date,
        intervalInDays: Int
    ): List<Installment> {
        return remoteDataSource.generateInstallmentSchedule(
            totalAmount,
            numberOfInstallments,
            startDate,
            intervalInDays
        )
    }
}