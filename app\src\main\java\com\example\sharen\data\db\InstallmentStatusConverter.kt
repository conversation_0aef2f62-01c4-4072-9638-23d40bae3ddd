package com.example.sharen.data.db

import androidx.room.TypeConverter
import com.example.sharen.data.model.InstallmentStatus

class InstallmentStatusConverter {
    @TypeConverter
    fun fromInstallmentStatus(status: InstallmentStatus): String {
        return status.name
    }

    @TypeConverter
    fun toInstallmentStatus(status: String): InstallmentStatus {
        return InstallmentStatus.valueOf(status)
    }
} 