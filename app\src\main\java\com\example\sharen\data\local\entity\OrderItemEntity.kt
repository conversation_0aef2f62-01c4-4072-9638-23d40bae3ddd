package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.OrderItem
import java.util.Date

@Entity(
    tableName = "order_items",
    foreignKeys = [
        ForeignKey(
            entity = OrderEntity::class,
            parentColumns = ["id"],
            childColumns = ["orderId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.RESTRICT
        )
    ],
    indices = [
        Index("orderId"),
        Index("productId")
    ]
)
data class OrderItemEntity(
    @PrimaryKey
    val id: String,
    val orderId: String,
    val productId: String,
    val productName: String,
    val quantity: Int,
    val unitPrice: Long, // Store as cents
    val totalPrice: Long // Store as cents
) {
    fun toDomainModel(): OrderItem = OrderItem(
        id = id,
        orderId = orderId,
        productId = productId,
        productName = productName,
        quantity = quantity,
        unitPrice = unitPrice / 100.0, // Convert from cents
        totalPrice = totalPrice / 100.0 // Convert from cents
    )

    companion object {
        fun fromDomainModel(orderItem: OrderItem): OrderItemEntity = OrderItemEntity(
            id = orderItem.id,
            orderId = orderItem.orderId,
            productId = orderItem.productId,
            productName = orderItem.productName,
            quantity = orderItem.quantity,
            unitPrice = (orderItem.unitPrice * 100).toLong(), // Convert to cents
            totalPrice = (orderItem.totalPrice * 100).toLong() // Convert to cents
        )
    }
}