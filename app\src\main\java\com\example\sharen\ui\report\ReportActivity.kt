package com.example.sharen.ui.report

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.data.model.ReportData
import com.example.sharen.data.model.ReportType
import com.example.sharen.databinding.ActivityReportBinding
import com.example.sharen.ui.dashboard.DashboardActivity
import com.example.sharen.ui.customer.CustomerListActivity
import com.example.sharen.ui.invoice.InvoiceListActivity
import com.example.sharen.ui.product.ProductListActivity
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.tabs.TabLayout
import com.alirezaafkar.sundatepicker.DatePicker
import com.alirezaafkar.sundatepicker.interfaces.DateSetListener
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.charts.RadarChart
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.components.XAxis
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

@AndroidEntryPoint
class ReportActivity : AppCompatActivity(), DateSetListener {

    private lateinit var binding: ActivityReportBinding
    private val viewModel: ReportViewModel by viewModels()
    private lateinit var dateFormatter: SimpleDateFormat
    private var currentReportType: ReportType = ReportType.SALES
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReportBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa", "IR"))
        
        setupToolbar()
        setupTabLayout()
        setupObservers()
        setupDateRangeButtons()
        setupExportButton()
        
        // بارگذاری داده‌های گزارش برای بازه زمانی پیش‌فرض (ماه جاری)
        viewModel.loadCurrentMonthData()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.reports)
        
        binding.bottomNavigation.selectedItemId = R.id.navigation_reports
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            onNavigationItemSelected(item)
        }
    }
    
    private fun setupTabLayout() {
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText(R.string.sales_report))
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText(R.string.financial_report))
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText(R.string.inventory_report))
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText(R.string.customer_report))

        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                when (tab?.position) {
                    0 -> currentReportType = ReportType.SALES
                    1 -> currentReportType = ReportType.FINANCIAL
                    2 -> currentReportType = ReportType.INVENTORY
                    3 -> currentReportType = ReportType.CUSTOMER
                }
                updateReportView()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }
    
    private fun setupObservers() {
        // مشاهده تغییر بازه زمانی
        viewModel.dateRange.observe(this) { dateRange ->
            binding.tvDateRange.text = "${dateFormatter.format(dateRange.first)} تا ${dateFormatter.format(dateRange.second)}"
        }
        
        // مشاهده تغییر داده‌های گزارش
        viewModel.reportData.observe(this) { data ->
            updateReportView()
        }
        
        // نمایش خطاها
        viewModel.error.observe(this) { error ->
            Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun setupDateRangeButtons() {
        binding.chipToday.setOnClickListener { viewModel.loadTodayData() }
        binding.chipThisWeek.setOnClickListener { viewModel.loadThisWeekData() }
        binding.chipThisMonth.setOnClickListener { viewModel.loadThisMonthData() }
        binding.chipThisYear.setOnClickListener { viewModel.loadThisYearData() }
        binding.btnSelectDate.setOnClickListener { showDatePicker() }
    }
    
    private fun setupExportButton() {
        binding.btnExport.setOnClickListener {
            viewModel.exportReport()
        }
    }
    
    private fun showDatePicker() {
        DatePicker.Builder()
            .id(1)
            .minDate(1300, 1, 1)
            .maxDate(1500, 12, 29)
            .build()
            .show(supportFragmentManager, "DatePicker")
    }
    
    private fun updateReportView() {
        when (currentReportType) {
            ReportType.SALES -> {
                binding.lineChart.visibility = View.VISIBLE
                binding.pieChart.visibility = View.GONE
                binding.barChart.visibility = View.GONE
                binding.radarChart.visibility = View.GONE
                viewModel.reportData.value?.let { data ->
                    // Update sales line chart
                    updateSalesChart(data)
                }
            }
            ReportType.FINANCIAL -> {
                binding.lineChart.visibility = View.GONE
                binding.pieChart.visibility = View.VISIBLE
                binding.barChart.visibility = View.GONE
                binding.radarChart.visibility = View.GONE
                viewModel.reportData.value?.let { data ->
                    // Update financial pie chart
                    updateFinancialChart(data)
                }
            }
            ReportType.INVENTORY -> {
                binding.lineChart.visibility = View.GONE
                binding.pieChart.visibility = View.GONE
                binding.barChart.visibility = View.VISIBLE
                binding.radarChart.visibility = View.GONE
                viewModel.reportData.value?.let { data ->
                    // Update inventory bar chart
                    updateInventoryChart(data)
                }
            }
            ReportType.CUSTOMER -> {
                binding.lineChart.visibility = View.GONE
                binding.pieChart.visibility = View.GONE
                binding.barChart.visibility = View.GONE
                binding.radarChart.visibility = View.VISIBLE
                viewModel.reportData.value?.let { data ->
                    // Update customer radar chart
                    updateCustomerChart(data)
                }
            }
        }
    }
    
    private fun updateSalesChart(data: ReportData) {
        // TODO: Implement sales chart update logic
        binding.lineChart.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            setDrawGridBackground(false)
            
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
            }
            
            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }
            
            axisRight.isEnabled = false
            
            legend.apply {
                form = Legend.LegendForm.LINE
                textSize = 11f
                verticalAlignment = Legend.LegendVerticalAlignment.BOTTOM
                horizontalAlignment = Legend.LegendHorizontalAlignment.LEFT
                orientation = Legend.LegendOrientation.HORIZONTAL
                setDrawInside(false)
            }
        }
    }

    private fun updateFinancialChart(data: ReportData) {
        // TODO: Implement financial chart update logic
        binding.pieChart.apply {
            description.isEnabled = false
            setUsePercentValues(true)
            setDrawHoleEnabled(true)
            setHoleColor(android.R.color.transparent)
            setTransparentCircleRadius(61f)
            
            legend.apply {
                verticalAlignment = Legend.LegendVerticalAlignment.TOP
                horizontalAlignment = Legend.LegendHorizontalAlignment.RIGHT
                orientation = Legend.LegendOrientation.VERTICAL
                setDrawInside(false)
                xEntrySpace = 7f
                yEntrySpace = 0f
                yOffset = 0f
            }
        }
    }

    private fun updateInventoryChart(data: ReportData) {
        // TODO: Implement inventory chart update logic
        binding.barChart.apply {
            description.isEnabled = false
            setDrawGridBackground(false)
            setDrawBarShadow(false)
            setDrawValueAboveBar(true)
            
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
                granularity = 1f
            }
            
            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }
            
            axisRight.isEnabled = false
            
            legend.apply {
                verticalAlignment = Legend.LegendVerticalAlignment.TOP
                horizontalAlignment = Legend.LegendHorizontalAlignment.RIGHT
                orientation = Legend.LegendOrientation.VERTICAL
                setDrawInside(false)
            }
        }
    }

    private fun updateCustomerChart(data: ReportData) {
        // TODO: Implement customer chart update logic
        binding.radarChart.apply {
            description.isEnabled = false
            webLineWidth = 1f
            webColor = android.graphics.Color.LTGRAY
            webLineWidthInner = 1f
            webColorInner = android.graphics.Color.LTGRAY
            webAlpha = 100
            
            legend.apply {
                verticalAlignment = Legend.LegendVerticalAlignment.TOP
                horizontalAlignment = Legend.LegendHorizontalAlignment.RIGHT
                orientation = Legend.LegendOrientation.VERTICAL
                setDrawInside(false)
            }
        }
    }
    
    override fun onDateSet(id: Int, calendar: Calendar, date: String) {
        viewModel.setCustomDateRange(calendar.timeInMillis)
    }
    
    private fun onNavigationItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.navigation_dashboard -> {
                startActivity(Intent(this, DashboardActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_customers -> {
                startActivity(Intent(this, CustomerListActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_invoices -> {
                startActivity(Intent(this, InvoiceListActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_products -> {
                startActivity(Intent(this, ProductListActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_reports -> {
                // روی صفحه فعلی هستیم
                return true
            }
        }
        return false
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 