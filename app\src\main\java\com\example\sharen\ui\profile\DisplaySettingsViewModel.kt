package com.example.sharen.ui.profile

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class DisplaySettingsViewModel @Inject constructor() : ViewModel() {
    private val _themeMode = MutableLiveData<String>()
    val themeMode: LiveData<String> = _themeMode

    private val _fontSize = MutableLiveData<Int>()
    val fontSize: LiveData<Int> = _fontSize

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    fun setThemeMode(mode: String) {
        _themeMode.value = mode
    }

    fun setFontSize(size: Int) {
        _fontSize.value = size
    }
} 