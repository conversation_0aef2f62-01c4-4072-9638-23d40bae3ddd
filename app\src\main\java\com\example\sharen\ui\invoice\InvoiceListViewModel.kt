package com.example.sharen.ui.invoice

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceStatus
import com.example.sharen.data.repository.InvoiceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InvoiceListViewModel @Inject constructor(
    private val invoiceRepository: InvoiceRepository
) : ViewModel() {

    // لیست فاکتورها برای نمایش
    private val _invoices = MutableLiveData<List<Invoice>>()
    val invoices: LiveData<List<Invoice>> = _invoices

    // نشانگر بارگذاری
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // پیام خطا
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // لیست کامل فاکتورها برای فیلتر کردن
    private var allInvoices: List<Invoice> = emptyList()
    
    // آخرین جستجو
    private var lastSearchQuery: String = ""
    
    // آخرین فیلتر
    private var lastFilterStatus: String? = null

    init {
        loadInvoices()
    }

    /**
     * بارگذاری لیست فاکتورها
     */
    private fun loadInvoices() {
        viewModelScope.launch {
            invoiceRepository.getAllInvoices()
                .onStart { _isLoading.value = true }
                .catch { e -> 
                    _errorMessage.value = e.message
                    _isLoading.value = false
                }
                .collect { list ->
                    allInvoices = list
                    applyFilters()
                    _isLoading.value = false
                }
        }
    }

    /**
     * جستجو در فاکتورها
     */
    fun searchInvoices(query: String) {
        lastSearchQuery = query
        applyFilters()
    }

    /**
     * فیلتر فاکتورها بر اساس وضعیت
     */
    fun filterInvoices(status: String?) {
        lastFilterStatus = status
        applyFilters()
    }

    /**
     * اعمال فیلترها و جستجو
     */
    private fun applyFilters() {
        var filteredList = allInvoices
        
        // اعمال فیلتر وضعیت
        if (!lastFilterStatus.isNullOrEmpty()) {
            val statusEnum = InvoiceStatus.valueOf(lastFilterStatus!!)
            filteredList = filteredList.filter { it.status == statusEnum }
        }
        
        // اعمال جستجو
        if (lastSearchQuery.isNotEmpty()) {
            val searchQuery = lastSearchQuery.trim().lowercase()
            filteredList = filteredList.filter { invoice ->
                invoice.invoiceNumber.lowercase().contains(searchQuery) ||
                invoice.customerName.lowercase().contains(searchQuery)
            }
        }
        
        _invoices.value = filteredList
    }
    
    /**
     * بارگذاری مجدد داده‌ها
     */
    fun refresh() {
        loadInvoices()
    }
} 