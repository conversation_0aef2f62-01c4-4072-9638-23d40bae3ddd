package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Product
import com.example.sharen.domain.model.ProductType
import com.example.sharen.domain.model.Season
import com.example.sharen.domain.model.Gender
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.Date

@Entity(
    tableName = "products",
    foreignKeys = [
        ForeignKey(
            entity = CategoryEntity::class,
            parentColumns = ["id"],
            childColumns = ["categoryId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [Index("categoryId")]
)
data class ProductEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val code: String? = null,
    val barcode: String? = null,
    val description: String? = null,
    val categoryId: String? = null,
    val categoryName: String? = null,
    val brandId: String? = null,
    val brandName: String? = null,
    val purchasePrice: Long,
    val sellingPrice: Long,
    val stock: Int,
    val minimumStock: Int = 0,
    val imageUrl: String? = null,
    val isActive: Boolean = true,
    val material: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    fun toDomainModel(): Product {
        return Product(
            id = id,
            name = name,
            code = code,
            barcode = barcode,
            description = description,
            categoryId = categoryId,
            categoryName = categoryName,
            brandId = brandId,
            brandName = brandName,
            purchasePrice = purchasePrice,
            sellingPrice = sellingPrice,
            stock = stock,
            minimumStock = minimumStock,
            imageUrl = imageUrl,
            isActive = isActive,
            material = material,
            createdAt = Date(createdAt),
            updatedAt = Date(updatedAt)
        )
    }

    companion object {
        fun fromDomainModel(product: Product): ProductEntity {
            return ProductEntity(
                id = product.id,
                name = product.name,
                code = product.code,
                barcode = product.barcode,
                description = product.description,
                categoryId = product.categoryId,
                categoryName = product.categoryName,
                brandId = product.brandId,
                brandName = product.brandName,
                purchasePrice = product.purchasePrice,
                sellingPrice = product.sellingPrice,
                stock = product.stock,
                minimumStock = product.minimumStock,
                imageUrl = product.imageUrl,
                isActive = product.isActive,
                material = product.material,
                createdAt = product.createdAt.time,
                updatedAt = product.updatedAt.time
            )
        }
    }
}