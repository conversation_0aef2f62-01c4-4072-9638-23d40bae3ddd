package com.example.sharen.di

import com.example.sharen.data.repository.InstallmentRepository
import com.example.sharen.ui.installment.InstallmentViewModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.scopes.ViewModelScoped

@Module
@InstallIn(ViewModelComponent::class)
object ViewModelModule {

    @Provides
    @ViewModelScoped
    fun provideInstallmentViewModel(
        repository: InstallmentRepository
    ): InstallmentViewModel {
        return InstallmentViewModel(repository)
    }
} 