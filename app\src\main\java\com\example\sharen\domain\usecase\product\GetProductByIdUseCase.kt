package com.example.sharen.domain.usecase.product

import com.example.sharen.domain.model.Product
import com.example.sharen.domain.repository.ProductRepository
import javax.inject.Inject

/**
 * Use Case برای دریافت محصول با شناسه
 */
class GetProductByIdUseCase @Inject constructor(
    private val productRepository: ProductRepository
) {
    suspend operator fun invoke(productId: String): Result<Product?> {
        return productRepository.getProductById(productId)
    }
}
