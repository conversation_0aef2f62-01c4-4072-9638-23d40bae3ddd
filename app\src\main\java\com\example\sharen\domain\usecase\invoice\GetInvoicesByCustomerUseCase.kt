package com.example.sharen.domain.usecase.invoice

import com.example.sharen.domain.model.Invoice
import com.example.sharen.domain.repository.InvoiceRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use Case برای دریافت فاکتورهای مشتری
 */
class GetInvoicesByCustomerUseCase @Inject constructor(
    private val invoiceRepository: InvoiceRepository
) {
    operator fun invoke(customerId: String): Flow<List<Invoice>> {
        return invoiceRepository.getInvoicesByCustomer(customerId)
    }
}
