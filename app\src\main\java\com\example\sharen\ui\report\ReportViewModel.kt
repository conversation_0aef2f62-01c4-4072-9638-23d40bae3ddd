package com.example.sharen.ui.report

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.repository.InvoiceRepository
import com.example.sharen.data.repository.ProductRepository
import com.example.sharen.data.repository.CustomerRepository
import com.example.sharen.data.model.Report
import com.example.sharen.data.model.ReportType
import com.example.sharen.data.repository.ReportRepository
import com.example.sharen.data.model.ReportData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class ReportViewModel @Inject constructor(
    private val invoiceRepository: InvoiceRepository,
    private val productRepository: ProductRepository,
    private val customerRepository: CustomerRepository,
    private val reportRepository: ReportRepository
) : ViewModel() {

    private val _reportType = MutableLiveData<ReportType>(ReportType.SALES)
    val reportType: LiveData<ReportType> = _reportType
    
    private val _dateRange = MutableLiveData<Pair<Date, Date>>()
    val dateRange: LiveData<Pair<Date, Date>> = _dateRange
    
    private val _reportData = MutableLiveData<ReportData>()
    val reportData: LiveData<ReportData> = _reportData
    
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    private val _reports = MutableStateFlow<List<Report>>(emptyList())
    val reports: StateFlow<List<Report>> = _reports.asStateFlow()

    private val _loading = MutableStateFlow(false)
    val loading: StateFlow<Boolean> = _loading.asStateFlow()

    init {
        loadReports()
    }

    fun setReportType(type: ReportType) {
        _reportType.value = type
        loadReportData()
    }
    
    fun loadTodayData() {
        viewModelScope.launch {
            _loading.value = true
            try {
        val calendar = Calendar.getInstance()
                val startOfDay = calendar.apply {
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }.timeInMillis
                
                val endOfDay = calendar.apply {
                    set(Calendar.HOUR_OF_DAY, 23)
                    set(Calendar.MINUTE, 59)
                    set(Calendar.SECOND, 59)
                    set(Calendar.MILLISECOND, 999)
                }.timeInMillis

                val data = reportRepository.getReportData(startOfDay, endOfDay)
                _reportData.value = data
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }
    
    fun loadThisWeekData() {
        viewModelScope.launch {
            _loading.value = true
            try {
        val calendar = Calendar.getInstance()
                val startOfWeek = calendar.apply {
                    set(Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }.timeInMillis

                val endOfWeek = calendar.apply {
                    add(Calendar.DAY_OF_WEEK, 6)
                    set(Calendar.HOUR_OF_DAY, 23)
                    set(Calendar.MINUTE, 59)
                    set(Calendar.SECOND, 59)
                    set(Calendar.MILLISECOND, 999)
                }.timeInMillis

                val data = reportRepository.getReportData(startOfWeek, endOfWeek)
                _reportData.value = data
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }
    
    fun loadCurrentMonthData() {
        viewModelScope.launch {
            _loading.value = true
            try {
        val calendar = Calendar.getInstance()
                val startOfMonth = calendar.apply {
                    set(Calendar.DAY_OF_MONTH, 1)
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }.timeInMillis

                val endOfMonth = calendar.apply {
                    set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                    set(Calendar.HOUR_OF_DAY, 23)
                    set(Calendar.MINUTE, 59)
                    set(Calendar.SECOND, 59)
                    set(Calendar.MILLISECOND, 999)
                }.timeInMillis

                val data = reportRepository.getReportData(startOfMonth, endOfMonth)
                _reportData.value = data
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }
    
    fun loadThisYearData() {
        viewModelScope.launch {
            _loading.value = true
            try {
                val calendar = Calendar.getInstance()
                val startOfYear = calendar.apply {
                    set(Calendar.DAY_OF_YEAR, 1)
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }.timeInMillis

                val endOfYear = calendar.apply {
                    set(Calendar.DAY_OF_YEAR, calendar.getActualMaximum(Calendar.DAY_OF_YEAR))
                    set(Calendar.HOUR_OF_DAY, 23)
                    set(Calendar.MINUTE, 59)
                    set(Calendar.SECOND, 59)
                    set(Calendar.MILLISECOND, 999)
                }.timeInMillis

                val data = reportRepository.getReportData(startOfYear, endOfYear)
                _reportData.value = data
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }

    fun setCustomDateRange(startDate: Long) {
        viewModelScope.launch {
            _loading.value = true
            try {
                val calendar = Calendar.getInstance()
                val endDate = calendar.timeInMillis

                val data = reportRepository.getReportData(startDate, endDate)
                _reportData.value = data
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }
    
    private fun loadReportData() {
        // Implementation of loadReportData method
    }

    fun loadReports() {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.getAllReports()
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { reports ->
                        _reports.value = reports
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    // Financial Reports
    fun generateDailyFinancialReport(date: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateDailyFinancialReport(date)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateMonthlyFinancialReport(year: Int, month: Int) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateMonthlyFinancialReport(year, month)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateYearlyFinancialReport(year: Int) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateYearlyFinancialReport(year)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateProfitLossReport(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateProfitLossReport(startDate, endDate)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateDebtReport() {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateDebtReport()
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    // Sales Reports
    fun generateDailySalesReport(date: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateDailySalesReport(date)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateMonthlySalesReport(year: Int, month: Int) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateMonthlySalesReport(year, month)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateYearlySalesReport(year: Int) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateYearlySalesReport(year)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateSalesByProductReport(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateSalesByProductReport(startDate, endDate)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateSalesByCustomerReport(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateSalesByCustomerReport(startDate, endDate)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateSalesByCategoryReport(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateSalesByCategoryReport(startDate, endDate)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    // Inventory Reports
    fun generateCurrentInventoryReport() {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateCurrentInventoryReport()
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateLowStockReport() {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateLowStockReport()
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateInventoryMovementReport(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateInventoryMovementReport(startDate, endDate)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun generateInventoryValueReport() {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.generateInventoryValueReport()
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    // Report Management
    fun saveReport(report: Report) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.saveReport(report)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun deleteReport(reportId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.deleteReport(reportId)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getReportsByType(type: ReportType) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.getReportsByType(type)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { reports ->
                        _reports.value = reports
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getReportsByDateRange(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.getReportsByDateRange(startDate, endDate)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { reports ->
                        _reports.value = reports
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    // Report Export
    fun exportReportToPdf(reportId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.exportReportToPdf(reportId)
                    .onSuccess { path ->
                        // Handle exported PDF path
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun exportReportToExcel(reportId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.exportReportToExcel(reportId)
                    .onSuccess { path ->
                        // Handle exported Excel path
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun exportReportToCsv(reportId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.exportReportToCsv(reportId)
                    .onSuccess { path ->
                        // Handle exported CSV path
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    // Report Templates
    fun saveReportTemplate(template: Report) {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.saveReportTemplate(template)
                    .onSuccess {
                        loadReports()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getAllReportTemplates() {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.getAllReportTemplates()
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { templates ->
                        _reports.value = templates
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun clearError() {
        _error.value = null
    }

    fun exportReport() {
        viewModelScope.launch {
            _loading.value = true
            try {
                reportRepository.exportReport(_reportData.value)
            } catch (e: Exception) {
                _error.value = e.message
            } finally {
                _loading.value = false
            }
        }
    }
} 