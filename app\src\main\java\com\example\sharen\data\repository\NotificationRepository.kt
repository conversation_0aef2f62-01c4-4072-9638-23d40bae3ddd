package com.example.sharen.data.repository

import com.example.sharen.data.model.Notification
import kotlinx.coroutines.flow.Flow

interface NotificationRepository {
    fun getAllNotifications(): Flow<List<Notification>>
    fun getUnreadNotifications(): Flow<List<Notification>>
    suspend fun createNotification(notification: Notification): Result<Notification>
    suspend fun markAsRead(notificationId: String): Result<Boolean>
    suspend fun markAsUnread(notificationId: String): Result<Boolean>
    suspend fun deleteNotification(notificationId: String): Result<Boolean>
    suspend fun clearAllNotifications(): Result<Boolean>
} 