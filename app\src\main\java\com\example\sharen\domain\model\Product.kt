package com.example.sharen.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Domain Model برای محصول
 */
@Parcelize
data class Product(
    val id: String,
    val name: String,
    val code: String? = null,
    val barcode: String? = null,
    val description: String? = null,
    val categoryId: String? = null,
    val categoryName: String? = null,
    val brandId: String? = null,
    val brandName: String? = null,
    val purchasePrice: Long,
    val sellingPrice: Long,
    val stock: Int,
    val minimumStock: Int = 0,
    val imageUrl: String? = null,
    val isActive: Boolean = true,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date(),
    // ویژگی‌های مخصوص پوشاک
    val sizes: List<ProductSize> = emptyList(),
    val colors: List<ProductColor> = emptyList(),
    val season: Season? = null,
    val gender: Gender? = null,
    val material: String? = null
) : Parcelable {

    /**
     * آیا محصول موجود است؟
     */
    val isInStock: Boolean
        get() = stock > 0

    /**
     * آیا موجودی کم است؟
     */
    val isLowStock: Boolean
        get() = stock <= minimumStock && stock > 0

    /**
     * آیا موجودی تمام شده؟
     */
    val isOutOfStock: Boolean
        get() = stock <= 0

    /**
     * محاسبه سود
     */
    val profit: Long
        get() = sellingPrice - purchasePrice

    /**
     * درصد سود
     */
    val profitPercentage: Double
        get() = if (purchasePrice > 0) {
            ((sellingPrice - purchasePrice).toDouble() / purchasePrice) * 100
        } else 0.0

    /**
     * وضعیت موجودی
     */
    val stockStatus: StockStatus
        get() = when {
            isOutOfStock -> StockStatus.OUT_OF_STOCK
            isLowStock -> StockStatus.LOW_STOCK
            else -> StockStatus.IN_STOCK
        }
}

/**
 * سایز محصول
 */
@Parcelize
data class ProductSize(
    val id: String,
    val name: String,
    val stock: Int = 0
) : Parcelable

/**
 * رنگ محصول
 */
@Parcelize
data class ProductColor(
    val id: String,
    val name: String,
    val hexCode: String,
    val stock: Int = 0
) : Parcelable

/**
 * نوع محصول
 */
enum class ProductType(val displayName: String) {
    CLOTHING("پوشاک"),
    SHOES("کفش"),
    ACCESSORIES("لوازم جانبی"),
    BAGS("کیف"),
    JEWELRY("جواهرات"),
    COSMETICS("آرایشی"),
    ELECTRONICS("الکترونیک"),
    HOME("خانه"),
    SPORTS("ورزشی"),
    BOOKS("کتاب"),
    OTHER("سایر")
}

/**
 * فصل
 */
enum class Season(val displayName: String) {
    SPRING("بهار"),
    SUMMER("تابستان"),
    AUTUMN("پاییز"),
    WINTER("زمستان"),
    ALL_SEASON("چهارفصل")
}

/**
 * جنسیت
 */
enum class Gender(val displayName: String) {
    MALE("مردانه"),
    FEMALE("زنانه"),
    UNISEX("یونیسکس"),
    KIDS("بچگانه")
}

/**
 * وضعیت موجودی
 */
enum class StockStatus(val displayName: String, val colorRes: Int) {
    IN_STOCK("موجود", android.R.color.holo_green_dark),
    LOW_STOCK("موجودی کم", android.R.color.holo_orange_dark),
    OUT_OF_STOCK("ناموجود", android.R.color.holo_red_dark)
}
