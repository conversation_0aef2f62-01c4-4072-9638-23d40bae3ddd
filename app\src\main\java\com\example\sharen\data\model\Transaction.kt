package com.example.sharen.data.model

import java.util.Date
import java.util.UUID

/**
 * مدل داده تراکنش
 */
data class Transaction(
    val id: String = UUID.randomUUID().toString(),
    val customerId: String,                  // شناسه مشتری
    val orderId: String? = null,             // شناسه سفارش
    val paymentId: String? = null,           // شناسه پرداخت
    val amount: Long = 0,                    // مبلغ
    val type: TransactionType,               // نوع تراکنش
    val description: String? = null,         // توضیحات
    val createdAt: Date = Date(),            // تاریخ ایجاد
    val updatedAt: Date = Date()             // تاریخ بروزرسانی
)

/**
 * انواع تراکنش
 */
enum class TransactionType {
    SALE,           // فروش
    PURCHASE,       // خرید
    PAYMENT,        // پرداخت
    REFUND,         // بازپرداخت
    ADJUSTMENT      // تعدیل
} 