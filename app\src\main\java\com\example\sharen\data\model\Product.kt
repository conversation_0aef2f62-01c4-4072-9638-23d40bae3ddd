package com.example.sharen.data.model

import java.util.Date
import java.util.UUID

/**
 * مدل داده محصول
 */
data class Product(
    val id: String = UUID.randomUUID().toString(),
    val name: String,                     // نام محصول
    val code: String? = null,             // کد محصول
    val barcode: String? = null,          // بارکد محصول
    val description: String? = null,      // توضیحات محصول
    val category: String? = null,         // دسته‌بندی محصول
    val purchasePrice: Long = 0,          // قیمت خرید
    val sellingPrice: Long = 0,           // قیمت فروش
    val stock: Int = 0,                   // موجودی
    val minimumStock: Int = 0,            // حداقل موجودی
    val imageUrl: String? = null,         // آدرس تصویر محصول
    val isActive: Boolean = true,         // وضعیت فعال بودن محصول
    val lastUpdated: Date = Date(),       // آخرین بروزرسانی
    val createdAt: Date = Date()          // تاریخ ایجاد
) {
    val profit: Long get() = sellingPrice - purchasePrice  // سود محصول
    val isLowStock: Boolean get() = stock <= minimumStock  // وضعیت هشدار موجودی
}