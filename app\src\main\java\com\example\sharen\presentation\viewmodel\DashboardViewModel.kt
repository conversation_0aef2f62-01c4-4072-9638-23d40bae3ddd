package com.example.sharen.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.example.sharen.core.base.BaseViewModel
import com.example.sharen.presentation.ui.dashboard.DashboardStats
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای داشبورد
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    // TODO: اضافه کردن Repository ها
) : BaseViewModel() {

    private val _dashboardStats = MutableStateFlow(DashboardStats())
    val dashboardStats: StateFlow<DashboardStats> = _dashboardStats.asStateFlow()

    init {
        loadDashboardData()
    }

    /**
     * بارگذاری داده‌های داشبورد
     */
    private fun loadDashboardData() {
        launchWithLoading {
            // شبیه‌سازی بارگذاری داده‌ها
            kotlinx.coroutines.delay(1000)
            
            // داده‌های نمونه
            val stats = DashboardStats(
                customersCount = 125,
                productsCount = 89,
                invoicesCount = 234,
                paymentsCount = 156,
                totalSales = 45_000_000,
                totalDebt = 8_500_000,
                todaySales = 2_300_000,
                todayPayments = 1_800_000
            )
            
            _dashboardStats.value = stats
        }
    }

    /**
     * بروزرسانی داده‌های داشبورد
     */
    fun refreshDashboard() {
        loadDashboardData()
    }
}
