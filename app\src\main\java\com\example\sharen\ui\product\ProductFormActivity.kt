package com.example.sharen.ui.product

import android.Manifest
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.example.sharen.R
import com.example.sharen.databinding.ActivityProductFormBinding
import com.example.sharen.util.ImageUtils
import dagger.hilt.android.AndroidEntryPoint
import java.io.File
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@AndroidEntryPoint
class ProductFormActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProductFormBinding
    private val viewModel: ProductFormViewModel by viewModels()
    private var productId: String? = null
    private var currentPhotoPath: String? = null
    private var currentPhotoUri: Uri? = null
    private var tempPhotoFile: File? = null
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    // Activity Result API برای دریافت نتیجه دوربین
    private val takePictureLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success) {
            currentPhotoUri?.let { uri ->
                processAndDisplayImage(uri)
            }
        }
    }

    // Activity Result API برای دریافت نتیجه گالری
    private val pickImageLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            processAndDisplayImage(it)
        }
    }

    // درخواست مجوزها
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val cameraPermissionGranted = permissions[Manifest.permission.CAMERA] ?: false
        val storagePermissionGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions[Manifest.permission.READ_MEDIA_IMAGES] ?: false
        } else {
            permissions[Manifest.permission.READ_EXTERNAL_STORAGE] ?: false
        }

        if (cameraPermissionGranted && storagePermissionGranted) {
            // نمایش دیالوگ انتخاب تصویر
            showImagePickerDialog()
        } else {
            Toast.makeText(
                this,
                "برای استفاده از این قابلیت به دسترسی‌های لازم نیاز است",
                Toast.LENGTH_LONG
            ).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        productId = intent.getStringExtra(EXTRA_PRODUCT_ID)
        
        setupToolbar()
        setupClickListeners()
        setupObservers()
        
        // اگر در حالت ویرایش هستیم، اطلاعات محصول را بارگذاری کنیم
        productId?.let {
            viewModel.loadProduct(it)
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        // عنوان به صورت پویا با توجه به حالت ویرایش یا ایجاد تنظیم می‌شود
    }

    private fun setupClickListeners() {
        binding.btnSave.setOnClickListener {
            saveProduct()
        }

        // اضافه کردن کلیک لیستنر برای دکمه افزودن تصویر
        binding.fabAddImage.setOnClickListener {
            checkPermissionsAndShowImagePicker()
        }
        
        // افزودن کلیک لیستنر برای حذف تصویر
        binding.ivProductImage.setOnLongClickListener {
            if (currentPhotoUri != null || viewModel.product.value?.imageUrl != null) {
                ImageUtils.showRemoveImageDialog(this) {
                    removeCurrentImage()
                }
                true
            } else {
                false
            }
        }
    }

    private fun setupObservers() {
        viewModel.isEditMode.observe(this) { isEditMode ->
            supportActionBar?.title = if (isEditMode) {
                getString(R.string.edit_product)
            } else {
                getString(R.string.add_new_product)
            }
        }
        
        viewModel.product.observe(this) { product ->
            product?.let {
                // پر کردن فیلدها با اطلاعات محصول
                binding.etProductName.setText(it.name)
                binding.etCategory.setText(it.category ?: "")
                binding.etDescription.setText(it.description ?: "")
                binding.etProductCode.setText(it.code ?: "")
                binding.etBarcode.setText(it.barcode ?: "")
                binding.etPurchasePrice.setText(it.purchasePrice.toString())
                binding.etSellingPrice.setText(it.sellingPrice.toString())
                binding.etStock.setText(it.stock.toString())
                binding.etMinimumStock.setText(it.minimumStock.toString())
                
                // بارگذاری تصویر محصول اگر وجود داشته باشد
                if (!it.imageUrl.isNullOrEmpty()) {
                    ImageUtils.loadImage(this, it.imageUrl, binding.ivProductImage)
                    currentPhotoPath = it.imageUrl
                }
            }
        }
        
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSave.isEnabled = !isLoading
            
            // غیرفعال کردن همه فیلدها در حالت بارگذاری
            setFieldsEnabled(!isLoading)
        }
        
        viewModel.error.observe(this) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }
        
        viewModel.saveSuccess.observe(this) { success ->
            if (success) {
                val message = if (viewModel.isEditMode.value == true) {
                    getString(R.string.product_updated)
                } else {
                    getString(R.string.product_created)
                }
                Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }
    
    private fun setFieldsEnabled(enabled: Boolean) {
        binding.etProductName.isEnabled = enabled
        binding.etCategory.isEnabled = enabled
        binding.etDescription.isEnabled = enabled
        binding.etProductCode.isEnabled = enabled
        binding.etBarcode.isEnabled = enabled
        binding.etPurchasePrice.isEnabled = enabled
        binding.etSellingPrice.isEnabled = enabled
        binding.etStock.isEnabled = enabled
        binding.etMinimumStock.isEnabled = enabled
        binding.fabAddImage.isEnabled = enabled
    }
    
    private fun saveProduct() {
        val name = binding.etProductName.text.toString().trim()
        val category = binding.etCategory.text.toString().trim().ifEmpty { null }
        val description = binding.etDescription.text.toString().trim().ifEmpty { null }
        val code = binding.etProductCode.text.toString().trim().ifEmpty { null }
        val barcode = binding.etBarcode.text.toString().trim().ifEmpty { null }
        
        val purchasePrice = try {
            binding.etPurchasePrice.text.toString().trim().toLong()
        } catch (e: NumberFormatException) {
            0L
        }
        
        val sellingPrice = try {
            binding.etSellingPrice.text.toString().trim().toLong()
        } catch (e: NumberFormatException) {
            0L
        }
        
        val stock = try {
            binding.etStock.text.toString().trim().toInt()
        } catch (e: NumberFormatException) {
            0
        }
        
        val minimumStock = try {
            binding.etMinimumStock.text.toString().trim().toInt()
        } catch (e: NumberFormatException) {
            0
        }
        
        viewModel.saveProduct(
            name,
            code,
            barcode,
            description,
            category,
            purchasePrice,
            sellingPrice,
            stock,
            minimumStock,
            currentPhotoPath
        )
    }

    private fun checkPermissionsAndShowImagePicker() {
        val cameraPermission = Manifest.permission.CAMERA
        val storagePermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }

        val hasCameraPermission = ContextCompat.checkSelfPermission(
            this, cameraPermission
        ) == PackageManager.PERMISSION_GRANTED

        val hasStoragePermission = ContextCompat.checkSelfPermission(
            this, storagePermission
        ) == PackageManager.PERMISSION_GRANTED

        if (hasCameraPermission && hasStoragePermission) {
            showImagePickerDialog()
        } else {
            requestPermissionLauncher.launch(arrayOf(cameraPermission, storagePermission))
        }
    }

    private fun showImagePickerDialog() {
        ImageUtils.showImagePickerDialog(
            this,
            onCameraSelected = { openCamera() },
            onGallerySelected = { openGallery() }
        )
    }

    private fun openCamera() {
        try {
            tempPhotoFile = ImageUtils.createImageFile(this).also { file ->
                currentPhotoUri = androidx.core.content.FileProvider.getUriForFile(
                    this,
                    "com.example.sharen.fileprovider",
                    file
                )
                takePictureLauncher.launch(currentPhotoUri)
            }
        } catch (ex: Exception) {
            Toast.makeText(
                this,
                "خطا در باز کردن دوربین: ${ex.message}",
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun openGallery() {
        pickImageLauncher.launch("image/*")
    }
    
    private fun processAndDisplayImage(uri: Uri) {
        // نمایش نشانگر بارگذاری
        binding.imageLoadingProgressBar.visibility = View.VISIBLE
        
        coroutineScope.launch(Dispatchers.IO) {
            try {
                // فشرده‌سازی تصویر در یک thread جداگانه
                val compressedUri = ImageUtils.compressImage(this@ProductFormActivity, uri)
                
                withContext(Dispatchers.Main) {
                    // پنهان کردن نشانگر بارگذاری
                    binding.imageLoadingProgressBar.visibility = View.GONE
                    
                    if (compressedUri != null) {
                        // نمایش تصویر فشرده‌سازی شده
                        ImageUtils.loadImage(this@ProductFormActivity, compressedUri.toString(), binding.ivProductImage)
                        // ذخیره مسیر تصویر برای ارسال به ViewModel
                        currentPhotoPath = ImageUtils.getPathFromUri(this@ProductFormActivity, compressedUri)
                        currentPhotoUri = compressedUri
                        // به روزرسانی imageUrl در ViewModel
                        viewModel.updateImageUrl(compressedUri.toString())
                        
                        // نمایش پیام موفقیت
                        Toast.makeText(
                            this@ProductFormActivity,
                            getString(R.string.image_compression_success),
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        // نمایش تصویر اصلی در صورت شکست فشرده‌سازی
                        ImageUtils.loadImage(this@ProductFormActivity, uri.toString(), binding.ivProductImage)
                        currentPhotoPath = ImageUtils.getPathFromUri(this@ProductFormActivity, uri)
                        currentPhotoUri = uri
                        viewModel.updateImageUrl(uri.toString())
                        
                        // نمایش پیام خطا
                        Toast.makeText(
                            this@ProductFormActivity,
                            getString(R.string.image_compression_error),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    // پنهان کردن نشانگر بارگذاری
                    binding.imageLoadingProgressBar.visibility = View.GONE
                    
                    // نمایش پیام خطا
                    Toast.makeText(
                        this@ProductFormActivity,
                        "${getString(R.string.image_compression_error)}: ${e.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }
    
    private fun removeCurrentImage() {
        // پاک کردن تصویر از ImageView
        binding.ivProductImage.setImageResource(R.drawable.ic_product_placeholder)
        
        // پاک کردن مسیرهای تصویر
        currentPhotoPath = null
        currentPhotoUri = null
        
        // به روزرسانی ViewModel
        viewModel.updateImageUrl(null)
        
        // نمایش پیام موفقیت
        Toast.makeText(
            this,
            getString(R.string.image_removed),
            Toast.LENGTH_SHORT
        ).show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            onBackPressed()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    companion object {
        const val EXTRA_PRODUCT_ID = "extra_product_id"
    }
} 