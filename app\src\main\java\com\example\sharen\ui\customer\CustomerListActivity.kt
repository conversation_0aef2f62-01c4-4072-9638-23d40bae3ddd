package com.example.sharen.ui.customer

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.data.model.Customer
import com.example.sharen.databinding.ActivityCustomerListBinding
import com.example.sharen.ui.dashboard.DashboardActivity
import com.google.android.material.navigation.NavigationBarView
import dagger.hilt.android.AndroidEntryPoint
import androidx.core.widget.doAfterTextChanged
import java.text.NumberFormat
import java.util.Locale

@AndroidEntryPoint
class CustomerListActivity : AppCompatActivity(), NavigationBarView.OnItemSelectedListener {

    private lateinit var binding: ActivityCustomerListBinding
    private val viewModel: CustomerListViewModel by viewModels()
    private lateinit var adapter: CustomerAdapter
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCustomerListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupObservers()
    }

    private fun setupUI() {
        // Toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // Bottom navigation
        binding.bottomNavigation.setOnItemSelectedListener(this)
        binding.bottomNavigation.selectedItemId = R.id.navigation_customers

        // RecyclerView
        adapter = CustomerAdapter(
            onCustomerClick = { customer -> navigateToCustomerDetails(customer) },
            onNewInvoiceClick = { customer -> createNewInvoice(customer) }
        )

        binding.rvCustomers.apply {
            layoutManager = LinearLayoutManager(this@CustomerListActivity)
            adapter = <EMAIL>
        }

        // Search
        binding.etSearch.doAfterTextChanged { text ->
            viewModel.search(text.toString())
        }

        // Add customer button
        binding.fabAddCustomer.setOnClickListener {
            navigateToAddCustomer()
        }
    }

    private fun setupObservers() {
        viewModel.customers.observe(this) { customers ->
            adapter.submitList(customers)
            updateEmptyState(customers.isEmpty())
        }

        viewModel.customerCount.observe(this) { count ->
            binding.tvTotalCustomers.text = getString(R.string.customer_count_format, numberFormatter.format(count))
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(this) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty && binding.etSearch.text.isNotEmpty()) {
            // No search results
            binding.tvEmptyState.text = getString(R.string.no_search_results)
            binding.tvEmptyState.visibility = View.VISIBLE
        } else if (isEmpty) {
            // No customers
            binding.tvEmptyState.text = getString(R.string.no_customers_found)
            binding.tvEmptyState.visibility = View.VISIBLE
        } else {
            binding.tvEmptyState.visibility = View.GONE
        }
    }

    private fun navigateToCustomerDetails(customer: Customer) {
        val intent = Intent(this, CustomerDetailsActivity::class.java).apply {
            putExtra(CustomerDetailsActivity.EXTRA_CUSTOMER_ID, customer.id)
        }
        startActivity(intent)
    }

    private fun navigateToAddCustomer() {
        val intent = Intent(this, CustomerFormActivity::class.java)
        startActivity(intent)
    }

    private fun createNewInvoice(customer: Customer) {
        // TODO: Will be implemented later
        Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.navigation_dashboard -> {
                startActivity(Intent(this, DashboardActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_customers -> {
                // We're already here
                return true
            }
            R.id.navigation_invoices -> {
                // TODO: Navigate to invoices
                Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
                return true
            }
            R.id.navigation_products -> {
                // TODO: Navigate to products
                Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
                return true
            }
            R.id.navigation_reports -> {
                // TODO: Navigate to reports
                Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
                return true
            }
        }
        return false
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    companion object {
        private const val TAG = "CustomerListActivity"
    }
} 