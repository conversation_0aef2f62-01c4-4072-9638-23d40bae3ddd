package com.example.sharen.core.constants

/**
 * ثابت‌های برنامه
 */
object AppConstants {
    
    // Database
    const val DATABASE_NAME = "sharen_database"
    const val DATABASE_VERSION = 1
    
    // SharedPreferences
    const val PREF_NAME = "sharen_prefs"
    const val PREF_USER_ID = "user_id"
    const val PREF_USER_ROLE = "user_role"
    const val PREF_IS_LOGGED_IN = "is_logged_in"
    const val PREF_THEME_MODE = "theme_mode"
    const val PREF_LANGUAGE = "language"
    
    // API
    const val BASE_URL = "https://your-supabase-url.supabase.co/"
    const val API_KEY = "your-supabase-anon-key"
    
    // User Roles
    const val ROLE_ADMIN = "admin"
    const val ROLE_MANAGER = "manager"
    const val ROLE_SELLER = "seller"
    const val ROLE_CUSTOMER = "customer"
    
    // Theme Modes
    const val THEME_LIGHT = "light"
    const val THEME_DARK = "dark"
    const val THEME_SYSTEM = "system"
    
    // Languages
    const val LANG_PERSIAN = "fa"
    const val LANG_ENGLISH = "en"
    
    // Date Formats
    const val DATE_FORMAT_PERSIAN = "yyyy/MM/dd"
    const val DATE_FORMAT_PERSIAN_FULL = "yyyy/MM/dd HH:mm"
    const val TIME_FORMAT = "HH:mm"
    
    // Currency
    const val CURRENCY_SYMBOL = "تومان"
    const val CURRENCY_DECIMAL_PLACES = 0
    
    // Pagination
    const val PAGE_SIZE = 20
    const val INITIAL_PAGE = 0
    
    // File Upload
    const val MAX_IMAGE_SIZE_MB = 5
    const val ALLOWED_IMAGE_TYPES = "image/jpeg,image/png,image/jpg"
    
    // Notification Channels
    const val NOTIFICATION_CHANNEL_GENERAL = "general"
    const val NOTIFICATION_CHANNEL_PAYMENT = "payment"
    const val NOTIFICATION_CHANNEL_REMINDER = "reminder"
    
    // Request Codes
    const val REQUEST_CODE_CAMERA = 1001
    const val REQUEST_CODE_GALLERY = 1002
    const val REQUEST_CODE_PERMISSION = 1003
    
    // Bundle Keys
    const val BUNDLE_CUSTOMER_ID = "customer_id"
    const val BUNDLE_INVOICE_ID = "invoice_id"
    const val BUNDLE_PRODUCT_ID = "product_id"
    const val BUNDLE_PAYMENT_ID = "payment_id"
    const val BUNDLE_USER_ID = "user_id"
    
    // Animation Durations
    const val ANIMATION_DURATION_SHORT = 200L
    const val ANIMATION_DURATION_MEDIUM = 300L
    const val ANIMATION_DURATION_LONG = 500L
    
    // Timeouts
    const val NETWORK_TIMEOUT = 30L // seconds
    const val SPLASH_DELAY = 2000L // milliseconds
    
    // Validation
    const val MIN_PASSWORD_LENGTH = 6
    const val MAX_PASSWORD_LENGTH = 50
    const val MIN_NAME_LENGTH = 2
    const val MAX_NAME_LENGTH = 50
    
    // Commission
    const val DEFAULT_COMMISSION_RATE = 0.05 // 5%
    const val MIN_COMMISSION_RATE = 0.0
    const val MAX_COMMISSION_RATE = 0.5 // 50%
    
    // Credit Limit
    const val DEFAULT_CREDIT_LIMIT = 10_000_000L // 10 million tomans
    const val MIN_CREDIT_LIMIT = 0L
    const val MAX_CREDIT_LIMIT = 1_000_000_000L // 1 billion tomans
    
    // Installment
    const val MIN_INSTALLMENT_COUNT = 2
    const val MAX_INSTALLMENT_COUNT = 24
    const val DEFAULT_INSTALLMENT_COUNT = 6
    
    // Stock
    const val MIN_STOCK_WARNING = 5
    const val DEFAULT_MIN_STOCK = 10
    
    // Discount
    const val MAX_DISCOUNT_PERCENTAGE = 100.0
    const val MIN_DISCOUNT_PERCENTAGE = 0.0
}
