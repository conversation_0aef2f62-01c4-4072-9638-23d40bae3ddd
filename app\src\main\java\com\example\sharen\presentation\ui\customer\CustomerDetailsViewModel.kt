package com.example.sharen.presentation.ui.customer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.domain.repository.CustomerRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CustomerDetailsViewModel @Inject constructor(
    private val customerRepository: CustomerRepository
) : ViewModel() {

    private val _state = MutableStateFlow<CustomerDetailsState>(CustomerDetailsState.Loading)
    val state: StateFlow<CustomerDetailsState> = _state.asStateFlow()

    fun loadCustomer(customerId: String) {
        viewModelScope.launch {
            try {
                _state.value = CustomerDetailsState.Loading
                
                val result = customerRepository.getCustomerById(customerId)
                if (result.isSuccess) {
                    val customer = result.getOrNull()
                    if (customer != null) {
                        _state.value = CustomerDetailsState.Success(customer)
                    } else {
                        _state.value = CustomerDetailsState.Error("مشتری یافت نشد")
                    }
                } else {
                    _state.value = CustomerDetailsState.Error(
                        result.exceptionOrNull()?.message ?: "خطا در بارگذاری اطلاعات مشتری"
                    )
                }
            } catch (e: Exception) {
                _state.value = CustomerDetailsState.Error(
                    e.message ?: "خطا در بارگذاری اطلاعات مشتری"
                )
            }
        }
    }
}
