package com.example.sharen.ui.payment

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.databinding.ActivityPaymentListBinding
import com.example.sharen.ui.dashboard.DashboardActivity
import com.example.sharen.ui.customer.CustomerListActivity
import com.example.sharen.ui.invoice.InvoiceListActivity
import com.example.sharen.ui.product.ProductListActivity
import com.example.sharen.ui.report.ReportActivity
import com.google.android.material.navigation.NavigationBarView
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale

@AndroidEntryPoint
class PaymentListActivity : AppCompatActivity(), NavigationBarView.OnItemSelectedListener {

    private lateinit var binding: ActivityPaymentListBinding
    private val viewModel: PaymentListViewModel by viewModels()
    private lateinit var adapter: PaymentListAdapter
    
    private val numberFormatter = NumberFormat.getInstance(Locale("fa", "IR"))
    private val dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa", "IR"))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPaymentListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupUI()
        setupObservers()
        
        viewModel.loadPayments()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.payments)
    }

    private fun setupUI() {
        // Bottom navigation
        binding.bottomNavigation.setOnItemSelectedListener(this)
        
        // RecyclerView
        adapter = PaymentListAdapter(
            numberFormatter = numberFormatter,
            dateFormatter = dateFormatter,
            onPaymentClick = { payment -> navigateToPaymentDetails(payment.id) }
        )
        
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
        
        // Search
        binding.etSearch.doAfterTextChanged { text ->
            viewModel.searchPayments(text.toString())
        }
        
        // Add new payment button
        binding.fabAddPayment.setOnClickListener {
            navigateToNewPayment()
        }
    }

    private fun setupObservers() {
        viewModel.payments.observe(this) { payments ->
            adapter.submitList(payments)
            binding.tvEmptyState.visibility = if (payments.isEmpty()) android.view.View.VISIBLE else android.view.View.GONE
        }
        
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        }
        
        viewModel.error.observe(this) { error ->
            Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun navigateToPaymentDetails(paymentId: String) {
        val intent = Intent(this, PaymentDetailActivity::class.java).apply {
            putExtra("payment_id", paymentId)
        }
        startActivity(intent)
    }
    
    private fun navigateToNewPayment() {
        // Navigate to select invoice first
        val intent = Intent(this, InvoiceListActivity::class.java).apply {
            putExtra("select_for_payment", true)
        }
        startActivity(intent)
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.navigation_dashboard -> {
                startActivity(Intent(this, DashboardActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_customers -> {
                startActivity(Intent(this, CustomerListActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_invoices -> {
                startActivity(Intent(this, InvoiceListActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_products -> {
                startActivity(Intent(this, ProductListActivity::class.java))
                finish()
                return true
            }
            R.id.navigation_reports -> {
                startActivity(Intent(this, ReportActivity::class.java))
                finish()
                return true
            }
        }
        return false
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
} 