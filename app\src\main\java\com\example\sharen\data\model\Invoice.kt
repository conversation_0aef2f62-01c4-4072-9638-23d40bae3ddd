package com.example.sharen.data.model

import java.util.Date

/**
 * مدل داده فاکتور فروش
 */
data class Invoice(
    val id: String = java.util.UUID.randomUUID().toString(),
    val invoiceNumber: String,                // شماره فاکتور
    val customerId: String,                   // شناسه مشتری
    val customerName: String,                 // نام مشتری (برای نمایش سریع)
    val sellerId: String? = null,             // شناسه فروشنده
    val sellerName: String? = null,           // نام فروشنده
    val totalAmount: Long = 0,                // مبلغ کل
    val discount: Long = 0,                   // تخفیف
    val tax: Long = 0,                        // مالیات
    val finalAmount: Long = 0,                // مبلغ نهایی
    val paidAmount: Long = 0,                 // مبلغ پرداخت شده
    val remainingAmount: Long = 0,            // مبلغ باقیمانده
    val status: InvoiceStatus = InvoiceStatus.DRAFT,  // وضعیت فاکتور
    val paymentType: PaymentType = PaymentType.CASH,  // نوع پرداخت
    val dueDate: Date? = null,                // تاریخ سررسید (برای پرداخت اقساطی)
    val notes: String? = null,                // یادداشت‌ها
    val isDeleted: Boolean = false,           // حذف منطقی
    val items: List<InvoiceItem> = emptyList(), // اقلام فاکتور
    val createdAt: Date = Date(),             // تاریخ ایجاد
    val updatedAt: Date = Date(),             // تاریخ به‌روزرسانی
    val approvedAt: Date? = null,             // تاریخ تأیید
    val approvedBy: String? = null            // شناسه کاربر تأیید کننده
) {
    // محاسبه مبلغ نهایی
    val calculatedFinalAmount: Long
        get() = totalAmount - discount + tax
        
    // محاسبه مبلغ باقیمانده
    val calculatedRemainingAmount: Long
        get() = finalAmount - paidAmount
        
    // آیا فاکتور به طور کامل پرداخت شده است؟
    val isFullyPaid: Boolean
        get() = paidAmount >= finalAmount
        
    // آیا فاکتور به صورت اقساطی است؟
    val isInstallment: Boolean
        get() = paymentType == PaymentType.INSTALLMENT
}

/**
 * وضعیت‌های مختلف فاکتور
 */
enum class InvoiceStatus {
    DRAFT,          // پیش‌نویس
    PENDING,        // در انتظار تأیید
    APPROVED,       // تأیید شده
    PAID,           // پرداخت شده
    PARTIALLY_PAID, // پرداخت جزئی
    CANCELLED       // لغو شده
}

/**
 * انواع روش‌های پرداخت
 */
enum class PaymentType {
    CASH,           // نقدی
    CARD,           // کارت بانکی
    INSTALLMENT,    // اقساطی
    MIXED           // ترکیبی
} 