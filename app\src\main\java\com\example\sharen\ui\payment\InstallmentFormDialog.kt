package com.example.sharen.ui.payment

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.example.sharen.R
import com.example.sharen.data.model.Installment
import ir.hamsaa.persiandatepicker.PersianDatePickerDialog
import ir.hamsaa.persiandatepicker.api.PersianPickerDate
import ir.hamsaa.persiandatepicker.api.PersianPickerListener
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class InstallmentFormDialog(
    context: Context,
    private val installment: Installment?,
    private val onSubmit: (amount: Long, dueDate: Date, notes: String?) -> Unit
) : Dialog(context) {

    private lateinit var etAmount: EditText
    private lateinit var tvDueDate: TextView
    private lateinit var etNotes: EditText
    private lateinit var btnSelectDate: Button
    private lateinit var btnCancel: Button
    private lateinit var btnSubmit: Button
    
    private val dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa", "IR"))
    private var selectedDate: Date = Date()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_installment_form)
        
        initViews()
        setListeners()
        
        if (installment != null) {
            populateForm(installment)
        } else {
            // پیش‌فرض تاریخ سررسید یک ماه بعد
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.MONTH, 1)
            selectedDate = calendar.time
            updateDateDisplay()
        }
    }
    
    private fun initViews() {
        etAmount = findViewById(R.id.etAmount)
        tvDueDate = findViewById(R.id.tvDueDate)
        etNotes = findViewById(R.id.etNotes)
        btnSelectDate = findViewById(R.id.btnSelectDate)
        btnCancel = findViewById(R.id.btnCancel)
        btnSubmit = findViewById(R.id.btnSubmit)
    }
    
    private fun setListeners() {
        btnSelectDate.setOnClickListener {
            showDatePicker()
        }
        
        btnCancel.setOnClickListener {
            dismiss()
        }
        
        btnSubmit.setOnClickListener {
            submitForm()
        }
    }
    
    private fun populateForm(installment: Installment) {
        etAmount.setText(installment.amount.toString())
        selectedDate = installment.dueDate
        updateDateDisplay()
        etNotes.setText(installment.notes)
    }
    
    private fun showDatePicker() {
        val persianDatePicker = PersianDatePickerDialog(context)
            .setPositiveButtonString(context.getString(R.string.confirm))
            .setNegativeButtonString(context.getString(R.string.cancel))
            .setTodayButtonVisible(true)
            .setMinYear(1400)
            .setMaxYear(1410)
            .setActionTextColor(context.resources.getColor(R.color.colorPrimary, context.theme))
            .setTitleType(PersianDatePickerDialog.WEEKDAY_DAY_MONTH_YEAR)
            .setShowInBottomSheet(true)
            .setListener(object : PersianPickerListener {
                override fun onDateSelected(persianPickerDate: PersianPickerDate) {
                    selectedDate = Date(persianPickerDate.timestamp)
                    updateDateDisplay()
                }
                
                override fun onDismissed() {}
            })
        
        persianDatePicker.show()
    }
    
    private fun updateDateDisplay() {
        tvDueDate.text = dateFormatter.format(selectedDate)
    }
    
    private fun submitForm() {
        val amountStr = etAmount.text.toString()
        if (amountStr.isEmpty()) {
            etAmount.error = context.getString(R.string.required_field)
            return
        }
        
        val amount = amountStr.toLongOrNull()
        if (amount == null || amount <= 0) {
            etAmount.error = context.getString(R.string.invalid_amount)
            return
        }
        
        val notes = etNotes.text.toString().ifEmpty { null }
        
        onSubmit(amount, selectedDate, notes)
        dismiss()
    }
} 