package com.example.sharen.ui.customer

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.data.model.Customer
import com.example.sharen.databinding.ActivityCustomerDetailsBinding
import com.example.sharen.ui.dashboard.TransactionAdapter
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale

@AndroidEntryPoint
class CustomerDetailsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCustomerDetailsBinding
    private val viewModel: CustomerDetailsViewModel by viewModels()
    private lateinit var transactionAdapter: TransactionAdapter
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))
    private val dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa"))
    
    private var customerId: String? = null
    private var currentCustomer: Customer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCustomerDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        customerId = intent.getStringExtra(EXTRA_CUSTOMER_ID)
        if (customerId == null) {
            Toast.makeText(this, "خطا: شناسه مشتری نامعتبر است", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        setupObservers()
        
        // Load customer data
        viewModel.loadCustomer(customerId!!)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.customer_details)
    }

    private fun setupRecyclerView() {
        transactionAdapter = TransactionAdapter { transaction ->
            // TODO: Navigate to transaction details
            Toast.makeText(this, "جزئیات تراکنش: ${transaction.invoiceNumber}", Toast.LENGTH_SHORT).show()
        }

        binding.rvRecentInvoices.apply {
            layoutManager = LinearLayoutManager(this@CustomerDetailsActivity)
            adapter = transactionAdapter
        }
    }

    private fun setupClickListeners() {
        binding.fabEdit.setOnClickListener {
            currentCustomer?.let { customer ->
                val intent = Intent(this, CustomerFormActivity::class.java).apply {
                    putExtra(CustomerFormActivity.EXTRA_CUSTOMER_ID, customer.id)
                }
                startActivity(intent)
            }
        }

        binding.fabNewInvoice.setOnClickListener {
            // TODO: Navigate to invoice creation
            Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupObservers() {
        viewModel.customer.observe(this) { customer ->
            if (customer != null) {
                currentCustomer = customer
                updateUI(customer)
            }
        }

        viewModel.recentTransactions.observe(this) { transactions ->
            transactionAdapter.submitList(transactions)
            binding.rvRecentInvoices.visibility = if (transactions.isEmpty()) View.GONE else View.VISIBLE
        }

        viewModel.isLoading.observe(this) { isLoading ->
            // TODO: Show loading indicator
        }

        viewModel.error.observe(this) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun updateUI(customer: Customer) {
        // Customer info
        binding.tvCustomerName.text = customer.name
        binding.tvCustomerPhone.text = customer.phone
        binding.tvCustomerAddress.text = customer.address ?: "آدرس ثبت نشده"

        // Financial info
        binding.tvDebtAmount.text = formatCurrency(customer.debtAmount)
        binding.tvTotalPurchases.text = formatCurrency(customer.totalPurchases)
        
        // Dates
        binding.tvLastPurchase.text = customer.lastPurchaseDate?.let { 
            dateFormatter.format(it) 
        } ?: "خریدی انجام نشده"
        
        binding.tvCustomerSince.text = dateFormatter.format(customer.createdAt)
        
        // Update collapsing toolbar title
        binding.collapsingToolbar.title = customer.name
    }
    
    private fun formatCurrency(amount: Long): String {
        return "${numberFormatter.format(amount)} تومان"
    }

    private fun confirmDeleteCustomer() {
        AlertDialog.Builder(this)
            .setTitle("حذف مشتری")
            .setMessage("آیا از حذف این مشتری اطمینان دارید؟ این عمل غیرقابل بازگشت است.")
            .setPositiveButton("بله، حذف شود") { _, _ ->
                customerId?.let { id ->
                    viewModel.deleteCustomer(id) {
                        Toast.makeText(this, "مشتری با موفقیت حذف شد", Toast.LENGTH_SHORT).show()
                        finish()
                    }
                }
            }
            .setNegativeButton("انصراف", null)
            .show()
    }

    private fun callCustomer() {
        currentCustomer?.let { customer ->
            val intent = Intent(Intent.ACTION_DIAL).apply {
                data = Uri.parse("tel:${customer.phone}")
            }
            startActivity(intent)
        }
    }

    private fun messageCustomer() {
        currentCustomer?.let { customer ->
            val intent = Intent(Intent.ACTION_SENDTO).apply {
                data = Uri.parse("sms:${customer.phone}")
            }
            startActivity(intent)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_customer_details, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
            R.id.action_call -> {
                callCustomer()
                return true
            }
            R.id.action_message -> {
                messageCustomer()
                return true
            }
            R.id.action_delete -> {
                confirmDeleteCustomer()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    companion object {
        const val EXTRA_CUSTOMER_ID = "extra_customer_id"
    }
} 