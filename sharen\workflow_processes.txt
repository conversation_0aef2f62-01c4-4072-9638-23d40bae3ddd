# فرآیندها و گردش کار در سیستم شارن

## 1. چرخه فروش

### 1.1. ایجاد فاکتور
1. **فروشنده** وارد بخش "فاکتور جدید" می‌شود
2. مشتری را از لیست انتخاب یا مشتری جدید ثبت می‌کند
3. محصولات را با جستجو به سبد اضافه می‌کند
4. تعداد و قیمت هر محصول را تعیین می‌کند
5. تخفیف (در صورت نیاز) اعمال می‌کند
6. نوع پرداخت را مشخص می‌کند:
   * نقدی
   * اقساطی
   * پیش‌پرداخت + اقساط
7. توضیحات تکمیلی را می‌نویسد
8. دکمه "ثبت فاکتور" را می‌زند
9. سیستم شماره فاکتور یکتا تولید می‌کند
10. **نوتیفیکیشن** به مدیر برای تأیید فاکتور ارسال می‌شود

### 1.2. تأیید فاکتور
1. **مدیر** نوتیفیکیشن را دریافت می‌کند
2. وارد بخش "فاکتورهای در انتظار تأیید" می‌شود
3. جزئیات فاکتور را بررسی می‌کند
4. می‌تواند:
   * تأیید کند
   * رد کند (با ذکر دلیل)
   * درخواست اصلاح کند
5. در صورت تأیید، **نوتیفیکیشن** به فروشنده ارسال می‌شود
6. فاکتور به وضعیت "تأیید شده" تغییر می‌کند
7. محصولات از موجودی انبار کسر می‌شوند
8. بدهی مشتری به روز می‌شود
9. کمیسیون فروشنده محاسبه می‌شود

### 1.3. چاپ و ارسال فاکتور
1. **فروشنده** پس از دریافت نوتیفیکیشن تأیید می‌تواند:
   * فاکتور را چاپ کند
   * فاکتور را به ایمیل مشتری ارسال کند
   * فاکتور را به شماره واتس‌اپ مشتری ارسال کند
2. نسخه PDF فاکتور در سیستم ذخیره می‌شود
3. مشتری می‌تواند از طریق پنل خود فاکتور را مشاهده کند

## 2. چرخه پرداخت

### 2.1. ثبت پرداخت نقدی
1. **فروشنده/مدیر** وارد بخش "ثبت پرداخت" می‌شود
2. مشتری را جستجو و انتخاب می‌کند
3. فاکتور مورد نظر را انتخاب می‌کند
4. مبلغ پرداختی را وارد می‌کند
5. روش پرداخت را مشخص می‌کند:
   * نقدی
   * کارت به کارت
   * دستگاه کارت‌خوان
   * پرداخت آنلاین
6. در صورت استفاده از کارت‌خوان، اطلاعات تراکنش ثبت می‌شود
7. رسید پرداخت صادر می‌شود
8. بدهی مشتری کاهش می‌یابد
9. **نوتیفیکیشن** به مشتری ارسال می‌شود

### 2.2. ثبت پرداخت اقساطی
1. **مدیر** هنگام تأیید فاکتور اقساطی:
   * تعداد اقساط را مشخص می‌کند
   * فاصله زمانی اقساط را تعیین می‌کند
   * مبلغ هر قسط را تعیین می‌کند
   * تاریخ سررسید هر قسط محاسبه می‌شود
2. برنامه زمان‌بندی اقساط در سیستم ثبت می‌شود
3. مشتری می‌تواند از طریق پنل خود برنامه اقساط را مشاهده کند
4. یک روز قبل از سررسید هر قسط، **نوتیفیکیشن** به مشتری ارسال می‌شود
5. **نوتیفیکیشن** به فروشنده برای پیگیری ارسال می‌شود

### 2.3. تأیید پرداخت اقساط
1. **مشتری** قسط را پرداخت می‌کند
2. **فروشنده/مدیر** وارد بخش "مدیریت اقساط" می‌شود
3. قسط مورد نظر را انتخاب می‌کند
4. اطلاعات پرداخت را ثبت می‌کند
5. دکمه "تأیید پرداخت" را می‌زند
6. وضعیت قسط به "پرداخت شده" تغییر می‌کند
7. بدهی مشتری کاهش می‌یابد
8. **نوتیفیکیشن** تأیید به مشتری ارسال می‌شود

### 2.4. مدیریت اقساط معوق
1. سیستم به صورت خودکار اقساط سررسید شده را شناسایی می‌کند
2. اقساط با تأخیر بیش از 3 روز به "معوق" تغییر وضعیت می‌دهند
3. **نوتیفیکیشن** به مشتری، فروشنده و مدیر ارسال می‌شود
4. **مدیر** می‌تواند:
   * جریمه تأخیر اعمال کند
   * برنامه جدید تقسیط تعریف کند
   * اخطار ارسال کند
5. محدودیت خرید اعتباری بیشتر برای مشتری اعمال می‌شود

## 3. آمارها و گزارش‌ها در داشبورد کاربران

### 3.1. داشبورد مشتری
**آمار مشتری** شامل:
1. **خلاصه حساب**:
   * کل خرید انجام شده
   * کل پرداختی‌ها
   * مانده بدهی
   * اعتبار باقی‌مانده
2. **فاکتورها**:
   * آخرین فاکتورها
   * فاکتورهای در انتظار پرداخت
   * فاکتورهای تسویه شده
3. **اقساط**:
   * اقساط سررسید شده
   * اقساط آینده
   * نمودار پرداخت‌ها
4. **تخفیف‌ها**:
   * تخفیف‌های دریافتی
   * امتیازات قابل استفاده
   * کد تخفیف فعال

**عملکرد گزینه آمار در منوی مشتری**:
- با کلیک روی گزینه "آمار" در منو، مشتری می‌تواند تمام گزارش‌های مالی خود را مشاهده کند
- نمودارهای مصور برای درک بهتر روند خرید و پرداخت نمایش داده می‌شود
- امکان فیلتر بر اساس بازه زمانی وجود دارد
- امکان دریافت خروجی PDF از گزارش‌ها وجود دارد

### 3.2. داشبورد فروشنده
**آمار فروشنده** شامل:
1. **عملکرد فروش**:
   * فروش روزانه/هفتگی/ماهانه
   * مقایسه با دوره قبل
   * نمودار روند فروش
   * رتبه در بین سایر فروشندگان
2. **کمیسیون**:
   * کمیسیون ماه جاری
   * کمیسیون پرداخت شده
   * کمیسیون قابل پرداخت
   * نمودار روند کمیسیون
3. **مشتریان**:
   * تعداد مشتریان فعال
   * مشتریان جدید
   * بالاترین خرید
   * لیست بدهکاران
4. **فاکتورها**:
   * فاکتورهای در انتظار تأیید
   * فاکتورهای امروز
   * فاکتورهای این ماه

**عملکرد فروشنده در سیستم**:
- فروشنده مسئول ثبت فاکتور و پیگیری پرداخت‌هاست
- می‌تواند مشتریان جدید ثبت کند (با تأیید مدیر)
- می‌تواند گزارش‌های فروش خود را مشاهده کند
- مسئول پیگیری اقساط معوق مشتریان خود است
- می‌تواند درخواست تسویه کمیسیون خود را ثبت کند

### 3.3. داشبورد مدیر
**آمار مدیر** شامل:
1. **آمار کلی**:
   * مجموع فروش
   * مجموع هزینه‌ها
   * سود خالص
   * مقایسه با دوره قبل
2. **فروشندگان**:
   * عملکرد هر فروشنده
   * رتبه‌بندی فروشندگان
   * کمیسیون‌های پرداختی
   * تسویه‌های در انتظار
3. **مشتریان**:
   * تعداد کل مشتریان
   * مشتریان جدید
   * بالاترین خریدها
   * مجموع بدهی مشتریان
4. **انبار**:
   * محصولات کم‌موجود
   * گردش کالا
   * ارزش موجودی
   * خریدهای در انتظار

**عملکرد مدیر در سیستم**:
- مدیر مسئول تأیید فاکتورها و پرداخت‌هاست
- تنظیم قیمت محصولات و تخفیف‌ها
- مدیریت فروشندگان و کمیسیون‌ها
- تعریف محصولات جدید
- مدیریت موجودی انبار
- تأیید تسویه با فروشندگان

## 4. نحوه ارتباط و نوتیفیکیشن‌ها

### 4.1. نوتیفیکیشن‌های فروشنده
**فروشنده نوتیفیکیشن دریافت می‌کند در موارد**:
1. تأیید یا رد فاکتور توسط مدیر
2. سررسید اقساط مشتریان
3. ثبت مشتری جدید
4. تغییر قیمت محصولات
5. پرداخت کمیسیون
6. پیام از طرف مدیر
7. تعریف کمپین‌های جدید فروش

### 4.2. نوتیفیکیشن‌های مشتری
**مشتری نوتیفیکیشن دریافت می‌کند در موارد**:
1. صدور فاکتور جدید
2. تأیید پرداخت
3. سررسید قسط بعدی
4. تخفیف‌ها و پیشنهادات ویژه
5. تغییر وضعیت اعتبار
6. یادآوری پرداخت اقساط معوق
7. پاسخ به درخواست‌ها

### 4.3. نوتیفیکیشن‌های مدیر
**مدیر نوتیفیکیشن دریافت می‌کند در موارد**:
1. فاکتور جدید در انتظار تأیید
2. درخواست تسویه فروشندگان
3. محصولات کم‌موجود
4. ثبت پرداخت جدید
5. ثبت مشتری جدید
6. اقساط معوق
7. گزارش‌های تحلیلی روزانه

### 4.4. کانال‌های ارسال نوتیفیکیشن
1. **درون برنامه‌ای**: در بخش نوتیفیکیشن‌های اپلیکیشن
2. **ایمیل**: ارسال به ایمیل ثبت‌شده کاربر
3. **پیامک**: ارسال به شماره موبایل کاربر
4. **پوش نوتیفیکیشن**: نمایش روی صفحه موبایل

## 5. مدیریت انبار و موجودی

### 5.1. گردش کالا در انبار
1. **ورود کالا**:
   * مدیر سفارش خرید ثبت می‌کند
   * کالا وارد انبار می‌شود
   * فاکتور خرید ثبت می‌شود
   * موجودی به‌روز می‌شود
   * قیمت‌ها تنظیم می‌شوند

2. **خروج کالا**:
   * با صدور فاکتور، محصولات رزرو می‌شوند
   * پس از تأیید فاکتور، از موجودی کم می‌شوند
   * اگر موجودی به حداقل برسد، **نوتیفیکیشن** صادر می‌شود
   * گزارش خروج کالا ثبت می‌شود

### 5.2. کنترل موجودی
1. تعیین حداقل موجودی برای هر محصول
2. هشدار خودکار کمبود موجودی
3. پیش‌بینی نیاز به سفارش مجدد
4. گزارش محصولات پرفروش/کم‌فروش
5. محاسبه ارزش موجودی انبار

### 5.3. مدیریت قیمت‌ها
1. تنظیم قیمت پایه
2. تعریف قیمت‌های ویژه برای گروه‌های مشتری
3. تعریف تخفیف‌های دوره‌ای
4. تغییر خودکار قیمت بر اساس موجودی

## 6. تسویه حساب‌ها

### 6.1. تسویه با فروشندگان
1. **فروشنده** درخواست تسویه ثبت می‌کند
2. مبلغ کمیسیون محاسبه می‌شود
3. **مدیر** درخواست را بررسی می‌کند
4. در صورت تأیید، روش پرداخت مشخص می‌شود
5. پرداخت انجام می‌شود
6. رسید تسویه صادر می‌شود
7. سابقه پرداخت ثبت می‌شود

### 6.2. تسویه با تأمین‌کنندگان
1. **مدیر** درخواست پرداخت ثبت می‌کند
2. جزئیات پرداخت مشخص می‌شود
3. تأیید پرداخت توسط مدیر مالی
4. ثبت پرداخت و به‌روزرسانی بدهی‌ها
5. صدور رسید پرداخت

## 7. چرخه عملیات روزانه

### 7.1. آغاز روز
1. **مدیر** گزارش موجودی انبار را بررسی می‌کند
2. **فروشنده** لیست مشتریان امروز را چک می‌کند
3. اقساط سررسید شده امروز مشخص می‌شوند
4. نوتیفیکیشن‌های یادآوری به مشتریان ارسال می‌شود

### 7.2. پایان روز
1. **مدیر** گزارش فروش روزانه را بررسی می‌کند
2. فاکتورهای در انتظار تأیید بررسی می‌شوند
3. پرداخت‌های امروز تأیید نهایی می‌شوند
4. گزارش مالی روزانه تولید می‌شود
5. بک‌آپ اطلاعات انجام می‌شود 