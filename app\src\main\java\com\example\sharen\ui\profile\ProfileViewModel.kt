package com.example.sharen.ui.profile

import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.local.entity.UserEntity
import com.example.sharen.data.repository.AuthRepository
import com.example.sharen.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _user = MutableLiveData<UserEntity?>()
    val user: LiveData<UserEntity?> = _user

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    fun loadUserProfile() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val userId = authRepository.getCurrentUserId()
                if (userId != null) {
                    userRepository.getUserById(userId).collect { user ->
                        _user.value = user
                        _isLoading.value = false
                    }
                } else {
                    _error.value = "کاربر پیدا نشد"
                    _isLoading.value = false
                }
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در بارگذاری پروفایل"
                _isLoading.value = false
            }
        }
    }

    fun updateUserName(name: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val currentUser = _user.value ?: return@launch
                val updatedUser = currentUser.copy(
                    name = name,
                    updatedAt = Date().time
                )
                val result = userRepository.updateUser(updatedUser)
                if (result.isSuccess) {
                    _user.value = updatedUser
                } else {
                    _error.value = "خطا در به‌روزرسانی نام"
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در به‌روزرسانی نام"
                _isLoading.value = false
            }
        }
    }

    fun updateUserPhone(phone: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val currentUser = _user.value ?: return@launch
                val updatedUser = currentUser.copy(
                    phone = phone,
                    updatedAt = Date().time
                )
                val result = userRepository.updateUser(updatedUser)
                if (result.isSuccess) {
                    _user.value = updatedUser
                } else {
                    _error.value = "خطا در به‌روزرسانی شماره موبایل"
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در به‌روزرسانی شماره موبایل"
                _isLoading.value = false
            }
        }
    }

    fun updateProfilePicture(imageUri: Uri) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = userRepository.uploadProfileImage(imageUri)
                if (result.isSuccess) {
                    val imageUrl = result.getOrNull()
                    if (imageUrl != null) {
                        val currentUser = _user.value ?: return@launch
                        val updatedUser = currentUser.copy(
                            imageUrl = imageUrl,
                            updatedAt = Date().time
                        )
                        userRepository.updateUser(updatedUser)
                        _user.value = updatedUser
                    }
                } else {
                    _error.value = "خطا در آپلود تصویر"
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در آپلود تصویر"
                _isLoading.value = false
            }
        }
    }

    fun logout() {
        viewModelScope.launch {
            try {
                authRepository.logout()
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در خروج از حساب کاربری"
            }
        }
    }
} 