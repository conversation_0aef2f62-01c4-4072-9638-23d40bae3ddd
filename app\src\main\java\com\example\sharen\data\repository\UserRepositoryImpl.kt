package com.example.sharen.data.repository

import com.example.sharen.data.local.dao.UserDao
import com.example.sharen.data.local.entity.UserEntity
import com.example.sharen.domain.model.User
import com.example.sharen.domain.model.UserRole
import com.example.sharen.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * پیاده‌سازی Repository برای مدیریت کاربران
 */
@Singleton
class UserRepositoryImpl @Inject constructor(
    private val userDao: UserDao
) : UserRepository {

    override suspend fun login(email: String, password: String): Result<User> {
        return try {
            // در حال حاضر فقط بررسی محلی انجام می‌دهیم
            // بعداً باید با Supabase ادغام شود
            val userEntity = userDao.getUserByEmail(email)
            if (userEntity != null && userEntity.isApproved) {
                Result.success(userEntity.toDomainModel())
            } else {
                Result.failure(Exception("کاربر یافت نشد یا تأیید نشده است"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun register(
        email: String,
        password: String,
        name: String,
        phone: String,
        role: UserRole,
        referrerCode: String?
    ): Result<User> {
        return try {
            val existingUser = userDao.getUserByEmail(email)
            if (existingUser != null) {
                return Result.failure(Exception("کاربری با این ایمیل قبلاً ثبت‌نام کرده است"))
            }

            val newUser = User(
                id = java.util.UUID.randomUUID().toString(),
                email = email,
                name = name,
                phone = phone,
                role = role,
                isApproved = role == UserRole.CUSTOMER, // مشتریان خودکار تأیید می‌شوند
                referrerCode = generateUniqueReferrerCode()
            )

            val userEntity = UserEntity.fromDomainModel(newUser)
            userDao.insertUser(userEntity)
            Result.success(newUser)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun logout(): Result<Unit> {
        return try {
            // پاک کردن کاربر فعلی
            userDao.clearCurrentUser()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getCurrentUser(): Result<User?> {
        return try {
            val userEntity = userDao.getCurrentUser()
            val user = userEntity?.toDomainModel()
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getUserById(userId: String): Result<User?> {
        return try {
            val userEntity = userDao.getUserById(userId)
            val user = userEntity?.toDomainModel()
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getAllUsers(): Flow<List<User>> {
        return userDao.getAllUsers().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getUsersByRole(role: UserRole): Flow<List<User>> {
        return userDao.getUsersByRole(role.name).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun searchUsers(query: String): Flow<List<User>> {
        return userDao.searchUsers(query).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun updateProfile(user: User): Result<User> {
        return try {
            val userEntity = UserEntity.fromDomainModel(user)
            userDao.updateUser(userEntity)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun changePassword(oldPassword: String, newPassword: String): Result<Unit> {
        return try {
            // این باید با Supabase پیاده‌سازی شود
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun approveUser(userId: String): Result<Unit> {
        return try {
            userDao.approveUser(userId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun rejectUser(userId: String, reason: String): Result<Unit> {
        return try {
            userDao.deleteUser(userId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteUser(userId: String): Result<Unit> {
        return try {
            userDao.deleteUser(userId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun resetPassword(email: String): Result<Unit> {
        return try {
            // این باید با Supabase پیاده‌سازی شود
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun uploadProfileImage(userId: String, imageData: ByteArray): Result<String> {
        return try {
            // این باید با Supabase Storage پیاده‌سازی شود
            val imageUrl = "https://example.com/image.jpg"
            Result.success(imageUrl)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getPendingUsers(): Flow<List<User>> {
        return userDao.getPendingUsers().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun generateReferrerCode(userId: String): Result<String> {
        return try {
            val code = generateUniqueReferrerCode()
            userDao.updateReferrerCode(userId, code)
            Result.success(code)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun validateReferrerCode(code: String): Result<User?> {
        return try {
            val userEntity = userDao.getUserByReferrerCode(code)
            val user = userEntity?.toDomainModel()
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun generateUniqueReferrerCode(): String {
        return "REF${System.currentTimeMillis().toString().takeLast(6)}"
    }
}
