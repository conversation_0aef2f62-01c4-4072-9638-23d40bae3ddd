package com.example.sharen.ui.payment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.example.sharen.R
import com.example.sharen.databinding.FragmentPaymentDetailsBinding
import com.example.sharen.util.DateUtils
import com.example.sharen.util.NumberUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class PaymentDetailsFragment : Fragment() {

    private var _binding: FragmentPaymentDetailsBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PaymentDetailsViewModel by viewModels()
    private val args: PaymentDetailsFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPaymentDetailsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupToolbar()
        observePayment()
    }

    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }
    }

    private fun observePayment() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.payment.collect { payment ->
                    payment?.let {
                        binding.apply {
                            // Set payment amount
                            amountTextView.text = getString(
                                R.string.payment_amount,
                                NumberUtils.formatCurrency(payment.amount)
                            )

                            // Set payment date
                            dateTextView.text = getString(
                                R.string.payment_date,
                                DateUtils.formatDate(payment.date)
                            )

                            // Set payment method
                            methodTextView.text = getString(
                                R.string.payment_method,
                                getPaymentMethodText(payment.method)
                            )

                            // Set reference number if available
                            referenceTextView.text = payment.referenceNumber?.let {
                                getString(R.string.payment_reference, it)
                            } ?: ""

                            // Set notes if available
                            notesTextView.text = payment.notes?.let {
                                getString(R.string.payment_notes, it)
                            } ?: ""

                            // Set status chip
                            statusChip.text = getPaymentStatusText(payment.status)
                            statusChip.setChipBackgroundColorResource(getPaymentStatusColor(payment.status))
                        }
                    }
                }
            }
        }
    }

    private fun getPaymentMethodText(method: PaymentMethod): String {
        return when (method) {
            PaymentMethod.CASH -> getString(R.string.payment_method_cash)
            PaymentMethod.CARD -> getString(R.string.payment_method_card)
            PaymentMethod.TRANSFER -> getString(R.string.payment_method_transfer)
            PaymentMethod.CHECK -> getString(R.string.payment_method_check)
            PaymentMethod.INSTALLMENT -> getString(R.string.payment_method_installment)
            PaymentMethod.OTHER -> getString(R.string.payment_method_other)
        }
    }

    private fun getPaymentStatusText(status: PaymentStatus): String {
        return when (status) {
            PaymentStatus.PENDING -> getString(R.string.payment_status_pending)
            PaymentStatus.CONFIRMED -> getString(R.string.payment_status_confirmed)
            PaymentStatus.REJECTED -> getString(R.string.payment_status_rejected)
            PaymentStatus.COMPLETED -> getString(R.string.payment_status_completed)
            PaymentStatus.FAILED -> getString(R.string.payment_status_failed)
        }
    }

    private fun getPaymentStatusColor(status: PaymentStatus): Int {
        return when (status) {
            PaymentStatus.PENDING -> R.color.payment_status_pending
            PaymentStatus.CONFIRMED -> R.color.payment_status_confirmed
            PaymentStatus.REJECTED -> R.color.payment_status_rejected
            PaymentStatus.COMPLETED -> R.color.payment_status_completed
            PaymentStatus.FAILED -> R.color.payment_status_failed
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 