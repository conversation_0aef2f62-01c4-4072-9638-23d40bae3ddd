package com.example.sharen.core.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Base ViewModel برای تمام ViewModel های برنامه
 */
abstract class BaseViewModel : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _success = MutableStateFlow<String?>(null)
    val success: StateFlow<String?> = _success.asStateFlow()

    /**
     * Exception Handler برای مدیریت خطاها
     */
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        _isLoading.value = false
        _error.value = exception.message ?: "خطای نامشخص"
    }

    /**
     * اجرای عملیات با مدیریت Loading و Error
     */
    protected fun launchWithLoading(
        showLoading: Boolean = true,
        block: suspend () -> Unit
    ) {
        viewModelScope.launch(exceptionHandler) {
            try {
                if (showLoading) _isLoading.value = true
                block()
            } finally {
                if (showLoading) _isLoading.value = false
            }
        }
    }

    /**
     * نمایش پیام خطا
     */
    protected fun showError(message: String) {
        _error.value = message
    }

    /**
     * نمایش پیام موفقیت
     */
    protected fun showSuccess(message: String) {
        _success.value = message
    }

    /**
     * پاک کردن پیام خطا
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * پاک کردن پیام موفقیت
     */
    fun clearSuccess() {
        _success.value = null
    }
}
