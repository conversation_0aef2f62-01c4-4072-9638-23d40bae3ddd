package com.example.sharen.data.model

import java.util.Date

data class User(
    val id: String,
    val email: String,
    val name: String,
    val phone: String,
    val role: UserRole,
    val isApproved: Boolean = false,
    val imageUrl: String? = null,
    val referrerId: String? = null,
    val referrerCode: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)

enum class UserRole {
    ADMIN, MANAGER, SELLER, CUSTOMER
}