package com.example.sharen.ui.notification

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Notification
import com.example.sharen.data.model.NotificationType
import com.example.sharen.databinding.ItemNotificationBinding
import java.text.SimpleDateFormat

class NotificationAdapter(
    private val dateFormatter: SimpleDateFormat,
    private val onNotificationClick: (Notification) -> Unit,
    private val onNotificationLongClick: (Notification) -> Boolean
) : ListAdapter<Notification, NotificationAdapter.NotificationViewHolder>(NotificationDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationViewHolder {
        val binding = ItemNotificationBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return NotificationViewHolder(binding)
    }

    override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class NotificationViewHolder(
        private val binding: ItemNotificationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(notification: Notification) {
            binding.root.setOnClickListener { onNotificationClick(notification) }
            binding.root.setOnLongClickListener { onNotificationLongClick(notification) }
            
            // عنوان اعلان
            binding.tvTitle.text = notification.title
            
            // متن اعلان
            binding.tvMessage.text = notification.message
            
            // تاریخ اعلان
            binding.tvDate.text = dateFormatter.format(notification.createdAt)
            
            // وضعیت خوانده شده
            if (!notification.isRead) {
                binding.root.setBackgroundColor(
                    ContextCompat.getColor(binding.root.context, R.color.light_gray)
                )
            } else {
                binding.root.setBackgroundColor(
                    ContextCompat.getColor(binding.root.context, android.R.color.white)
                )
            }
            
            // آیکون بر اساس نوع اعلان
            val icon = when (notification.type) {
                NotificationType.SYSTEM -> R.drawable.ic_notification_system
                NotificationType.INVOICE -> R.drawable.ic_notification_invoice
                NotificationType.PAYMENT -> R.drawable.ic_notification_payment
                NotificationType.INSTALLMENT -> R.drawable.ic_notification_installment
                NotificationType.USER -> R.drawable.ic_notification_user
                NotificationType.PRODUCT -> R.drawable.ic_notification_product
                else -> R.drawable.ic_notification_system
            }
            
            binding.ivIcon.setImageResource(icon)
        }
    }

    class NotificationDiffCallback : DiffUtil.ItemCallback<Notification>() {
        override fun areItemsTheSame(oldItem: Notification, newItem: Notification): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Notification, newItem: Notification): Boolean {
            return oldItem == newItem
        }
    }
} 