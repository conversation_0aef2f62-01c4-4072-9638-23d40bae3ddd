package com.example.sharen.ui.product

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.example.sharen.R
import com.example.sharen.data.model.Product
import com.example.sharen.databinding.ActivityProductDetailsBinding
import com.example.sharen.util.ImageUtils
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.util.Locale

@AndroidEntryPoint
class ProductDetailsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProductDetailsBinding
    private val viewModel: ProductDetailsViewModel by viewModels()
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))
    
    private var productId: String? = null
    private var currentProduct: Product? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        productId = intent.getStringExtra(EXTRA_PRODUCT_ID)
        if (productId == null) {
            Toast.makeText(this, "خطا: شناسه محصول نامعتبر است", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        setupToolbar()
        setupClickListeners()
        setupObservers()
        
        // بارگذاری اطلاعات محصول
        viewModel.loadProduct(productId!!)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    private fun setupClickListeners() {
        binding.fabEdit.setOnClickListener {
            currentProduct?.let { product ->
                val intent = Intent(this, ProductFormActivity::class.java).apply {
                    putExtra(ProductFormActivity.EXTRA_PRODUCT_ID, product.id)
                }
                startActivity(intent)
            }
        }
    }

    private fun setupObservers() {
        viewModel.product.observe(this) { product ->
            if (product != null) {
                currentProduct = product
                updateUI(product)
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(this) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun updateUI(product: Product) {
        // اطلاعات پایه
        binding.tvProductName.text = product.name
        binding.collapsingToolbar.title = product.name
        
        // کد و بارکد
        binding.tvProductCode.text = product.code ?: "ندارد"
        binding.tvBarcode.text = product.barcode ?: "ندارد"
        
        // دسته‌بندی
        binding.tvCategory.text = product.category ?: "دسته‌بندی نشده"
        
        // قیمت‌ها
        binding.tvPurchasePrice.text = formatCurrency(product.purchasePrice)
        binding.tvSellingPrice.text = formatCurrency(product.sellingPrice)
        
        // موجودی
        binding.tvStock.text = "${numberFormatter.format(product.stock)} عدد"
        binding.tvMinimumStock.text = "${numberFormatter.format(product.minimumStock)} عدد"
        
        // توضیحات
        binding.tvDescription.text = product.description ?: "بدون توضیحات"
        
        // نمایش تصویر محصول اگر وجود داشته باشد
        ImageUtils.loadImage(this, product.imageUrl, binding.ivProductImage)
    }
    
    private fun formatCurrency(amount: Long): String {
        return "${numberFormatter.format(amount)} تومان"
    }

    private fun confirmDeleteProduct() {
        AlertDialog.Builder(this)
            .setTitle("حذف محصول")
            .setMessage("آیا از حذف این محصول اطمینان دارید؟ این عمل غیرقابل بازگشت است.")
            .setPositiveButton("بله، حذف شود") { _, _ ->
                productId?.let { id ->
                    viewModel.deleteProduct(id) {
                        Toast.makeText(this, "محصول با موفقیت حذف شد", Toast.LENGTH_SHORT).show()
                        finish()
                    }
                }
            }
            .setNegativeButton("انصراف", null)
            .show()
    }

    private fun showUpdateStockDialog() {
        currentProduct?.let { product ->
            val currentStock = product.stock
            
            val builder = AlertDialog.Builder(this)
            builder.setTitle("بروزرسانی موجودی")
            
            val view = layoutInflater.inflate(R.layout.dialog_update_stock, null)
            builder.setView(view)
            
            val stockEditText = view.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.etNewStock)
            stockEditText.setText(currentStock.toString())
            
            builder.setPositiveButton("بروزرسانی") { _, _ ->
                try {
                    val newStock = stockEditText.text.toString().toInt()
                    productId?.let { id ->
                        viewModel.updateStock(id, newStock) {
                            Toast.makeText(this, "موجودی با موفقیت بروزرسانی شد", Toast.LENGTH_SHORT).show()
                        }
                    }
                } catch (e: NumberFormatException) {
                    Toast.makeText(this, "لطفاً عدد صحیح وارد کنید", Toast.LENGTH_SHORT).show()
                }
            }
            
            builder.setNegativeButton("انصراف", null)
            
            builder.show()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_product_details, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
            R.id.action_update_stock -> {
                showUpdateStockDialog()
                return true
            }
            R.id.action_delete -> {
                confirmDeleteProduct()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onRestart() {
        super.onRestart()
        // بارگذاری مجدد اطلاعات محصول پس از بازگشت از صفحه ویرایش
        productId?.let { viewModel.loadProduct(it) }
    }

    companion object {
        const val EXTRA_PRODUCT_ID = "extra_product_id"
    }
} 