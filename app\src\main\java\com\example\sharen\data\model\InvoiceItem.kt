package com.example.sharen.data.model

/**
 * مدل داده اقلام فاکتور
 */
data class InvoiceItem(
    val id: String = java.util.UUID.randomUUID().toString(),
    val invoiceId: String,              // شناسه فاکتور
    val productId: String,              // شناسه محصول
    val productName: String,            // نام محصول (برای نمایش سریع)
    val productCode: String? = null,    // کد محصول
    val quantity: Int,                  // تعداد
    val unitPrice: Long,                // قیمت واحد
    val discount: Long = 0,             // تخفیف
    val tax: Long = 0,                  // مالیات
    val notes: String? = null           // یادداشت‌ها
) {
    // محاسبه مبلغ کل آیتم
    val totalPrice: Long
        get() = (unitPrice * quantity) - discount + tax
} 