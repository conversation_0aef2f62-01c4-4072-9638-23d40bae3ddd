package com.example.sharen.data.repository

import com.example.sharen.data.local.dao.UserDao
import com.example.sharen.data.local.entity.UserEntity
import com.example.sharen.domain.model.User
import com.example.sharen.data.remote.AuthRemoteDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepositoryImpl @Inject constructor(
    private val userDao: UserDao,
    private val remoteDataSource: AuthRemoteDataSource
) : AuthRepository {

    override suspend fun login(email: String, password: String): Result<User> {
        return try {
            val result = remoteDataSource.login(email, password)
            result.onSuccess { user ->
                saveLoginState(user.id)
                // Save user to local database
                userDao.insertUser(UserEntity.fromDomainModel(user))
            }
            result
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun register(name: String, email: String, password: String, phone: String): Result<User> {
        return try {
            val result = remoteDataSource.register(name, email, password, phone)
            result.onSuccess { user ->
                saveLoginState(user.id)
                // Save user to local database
                userDao.insertUser(UserEntity.fromDomainModel(user))
            }
            result
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun logout(): Result<Unit> {
        return try {
            remoteDataSource.logout()
            clearLoginState()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getCurrentUser(): Flow<User?> {
        return userDao.getCurrentUser().map { userEntity ->
            userEntity?.toDomainModel()
        }
    }

    override suspend fun updateUser(user: User): Result<User> {
        return try {
            val result = remoteDataSource.updateUser(user)
            result.onSuccess { updatedUser ->
                // Update user in local database
                userDao.updateUser(UserEntity.fromDomainModel(updatedUser))
            }
            result
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun changePassword(oldPassword: String, newPassword: String): Result<Unit> {
        return remoteDataSource.changePassword(oldPassword, newPassword)
    }

    override suspend fun resetPassword(email: String): Result<Unit> {
        return try {
            remoteDataSource.resetPassword(email)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun isUserLoggedIn(): Boolean {
        return try {
            getCurrentUserId() != null
        } catch (e: Exception) {
            false
        }
    }

    override suspend fun getCurrentUserId(): String? {
        return try {
            // TODO: Implement actual logic to get current user ID
            null
        } catch (e: Exception) {
            null
        }
    }

    private suspend fun saveLoginState(userId: String) {
        // TODO: Save login state to SharedPreferences or secure storage
    }

    private suspend fun clearLoginState() {
        // TODO: Clear login state from SharedPreferences or secure storage
    }


}