package com.example.sharen.domain.usecase.product

import com.example.sharen.domain.model.Product
import com.example.sharen.domain.repository.ProductRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use Case برای جستجوی محصولات
 */
class SearchProductsUseCase @Inject constructor(
    private val productRepository: ProductRepository
) {
    operator fun invoke(query: String): Flow<List<Product>> {
        return if (query.isBlank()) {
            productRepository.getAllProducts()
        } else {
            productRepository.searchProducts(query.trim())
        }
    }
}
