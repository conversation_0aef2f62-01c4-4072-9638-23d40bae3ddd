<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.Sharen" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@android:color/white</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_dark</item>
        <item name="colorOnSecondary">@android:color/white</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorError">@color/error</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorOnError">@android:color/white</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/surface</item>
        <item name="android:windowLightNavigationBar">true</item>

        <!-- Typography -->
        <item name="android:fontFamily">@font/font_family</item>
        <item name="fontFamily">@font/font_family</item>

        <!-- RTL Support -->
        <item name="android:layoutDirection">rtl</item>
        <item name="android:textDirection">rtl</item>
    </style>

    <style name="Theme.Sharen" parent="Base.Theme.Sharen" />

    <!-- Splash Theme -->
    <style name="Theme.Sharen.Splash" parent="Theme.Sharen">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- No Action Bar Theme -->
    <style name="Theme.Sharen.NoActionBar" parent="Theme.Sharen">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Button Styles -->
    <style name="Button.Primary" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/iransans_bold</item>
    </style>

    <style name="Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/primary</item>
        <item name="android:textColor">@color/primary</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/iransans_bold</item>
    </style>

    <!-- Card Styles -->
    <style name="Card.Elevated" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
    </style>

    <!-- Text Styles -->
    <style name="Text.Headline" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/iransans_bold</item>
    </style>

    <style name="Text.Body" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/font_family</item>
    </style>

    <style name="Text.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">@font/font_family</item>
    </style>
</resources>