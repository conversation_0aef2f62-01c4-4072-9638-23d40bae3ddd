package com.example.sharen.domain.usecase.product

import com.example.sharen.domain.repository.ProductRepository
import javax.inject.Inject

/**
 * Use Case برای بروزرسانی موجودی محصول
 */
class UpdateProductStockUseCase @Inject constructor(
    private val productRepository: ProductRepository
) {
    suspend operator fun invoke(productId: String, newStock: Int): Result<Unit> {
        
        // اعتبارسنجی ورودی‌ها
        if (productId.isBlank()) {
            return Result.failure(IllegalArgumentException("شناسه محصول نمی‌تواند خالی باشد"))
        }
        
        if (newStock < 0) {
            return Result.failure(IllegalArgumentException("موجودی نمی‌تواند منفی باشد"))
        }
        
        return productRepository.updateProductStock(productId, newStock)
    }
}
