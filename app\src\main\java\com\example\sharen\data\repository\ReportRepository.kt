package com.example.sharen.data.repository

import com.example.sharen.data.model.Report
import com.example.sharen.data.model.ReportType
import com.example.sharen.data.model.ReportData
import kotlinx.coroutines.flow.Flow
import java.util.Date

interface ReportRepository {
    suspend fun getReportData(startDate: Long, endDate: Long): ReportData

    // Financial Reports
    suspend fun generateDailyFinancialReport(date: Date): Result<Report>
    suspend fun generateMonthlyFinancialReport(year: Int, month: Int): Result<Report>
    suspend fun generateYearlyFinancialReport(year: Int): Result<Report>
    suspend fun generateProfitLossReport(startDate: Date, endDate: Date): Result<Report>
    suspend fun generateDebtReport(): Result<Report>

    // Sales Reports
    suspend fun generateDailySalesReport(date: Date): Result<Report>
    suspend fun generateMonthlySalesReport(year: Int, month: Int): Result<Report>
    suspend fun generateYearlySalesReport(year: Int): Result<Report>
    suspend fun generateSalesByProductReport(startDate: Date, endDate: Date): Result<Report>
    suspend fun generateSalesByCustomerReport(startDate: Date, endDate: Date): Result<Report>
    suspend fun generateSalesByCategoryReport(startDate: Date, endDate: Date): Result<Report>

    // Inventory Reports
    suspend fun generateCurrentInventoryReport(): Result<Report>
    suspend fun generateLowStockReport(): Result<Report>
    suspend fun generateInventoryMovementReport(startDate: Date, endDate: Date): Result<Report>
    suspend fun generateInventoryValueReport(): Result<Report>

    // Customer Reports
    suspend fun generateStockMovementReport(startDate: Date, endDate: Date): Result<Report>
    suspend fun generateCustomerActivityReport(startDate: Date, endDate: Date): Result<Report>
    suspend fun generateCustomerLoyaltyReport(): Result<Report>
    suspend fun generateCustomerDebtReport(): Result<Report>

    // Report Management
    suspend fun saveReport(report: Report): Result<Report>
    suspend fun updateReport(report: Report): Result<Report>
    suspend fun deleteReport(reportId: String): Result<Unit>
    suspend fun getReport(reportId: String): Result<Report>
    fun getAllReports(): Flow<List<Report>>
    fun getReportsByType(type: ReportType): Flow<List<Report>>
    fun getReportsByDateRange(startDate: Date, endDate: Date): Flow<List<Report>>

    // Report Export
    suspend fun exportReportToPdf(reportId: String): Result<String>
    suspend fun exportReportToExcel(reportId: String): Result<String>
    suspend fun exportReportToCsv(reportId: String): Result<String>

    // Report Templates
    suspend fun saveReportTemplate(template: Report): Result<Report>
    suspend fun getReportTemplate(templateId: String): Result<Report>
    fun getAllReportTemplates(): Flow<List<Report>>
    suspend fun deleteReportTemplate(templateId: String): Result<Unit>
}
