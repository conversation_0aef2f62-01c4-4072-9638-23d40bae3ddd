package com.example.sharen.data.local.relation

import androidx.room.Embedded
import androidx.room.Relation
import com.example.sharen.data.local.entity.CustomerEntity
import com.example.sharen.data.local.entity.UserEntity

data class CustomerWithUser(
    @Embedded
    val customer: CustomerEntity,
    
    @Relation(
        parentColumn = "userId",
        entityColumn = "id"
    )
    val user: UserEntity
) 