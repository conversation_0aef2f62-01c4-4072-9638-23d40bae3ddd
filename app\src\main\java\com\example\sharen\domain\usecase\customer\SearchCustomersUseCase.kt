package com.example.sharen.domain.usecase.customer

import com.example.sharen.domain.model.Customer
import com.example.sharen.domain.repository.CustomerRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use Case برای جستجوی مشتریان
 */
class SearchCustomersUseCase @Inject constructor(
    private val customerRepository: CustomerRepository
) {
    operator fun invoke(query: String): Flow<List<Customer>> {
        return if (query.isBlank()) {
            customerRepository.getAllCustomers()
        } else {
            customerRepository.searchCustomers(query.trim())
        }
    }
}
