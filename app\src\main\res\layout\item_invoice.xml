<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/ivDocIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@android:drawable/ic_menu_agenda"
            android:contentDescription="@string/invoice_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvInvoiceNumber"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/ivPaid"
            app:layout_constraintStart_toEndOf="@+id/ivDocIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="شماره فاکتور: INV-1001" />

        <TextView
            android:id="@+id/tvCustomerName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.AppCompat.Small"
            app:layout_constraintEnd_toStartOf="@+id/ivPaid"
            app:layout_constraintStart_toStartOf="@+id/tvInvoiceNumber"
            app:layout_constraintTop_toBottomOf="@+id/tvInvoiceNumber"
            tools:text="مشتری: محمد احمدی" />

        <TextView
            android:id="@+id/tvDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textAppearance="@style/TextAppearance.AppCompat.Small"
            android:textColor="@android:color/darker_gray"
            app:layout_constraintStart_toStartOf="@+id/tvCustomerName"
            app:layout_constraintTop_toBottomOf="@+id/tvCustomerName"
            tools:text="1402/10/15" />

        <TextView
            android:id="@+id/tvPaymentType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textAppearance="@style/TextAppearance.AppCompat.Small"
            android:textColor="@android:color/darker_gray"
            app:layout_constraintStart_toEndOf="@+id/tvDate"
            app:layout_constraintTop_toTopOf="@+id/tvDate"
            tools:text="نقدی" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="@android:color/darker_gray"
            app:layout_constraintTop_toBottomOf="@+id/tvDate" />

        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:background="@drawable/status_background"
            android:padding="4dp"
            android:textAppearance="@style/TextAppearance.AppCompat.Small"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider"
            tools:text="پرداخت شده"
            tools:textColor="@color/green" />

        <TextView
            android:id="@+id/tvAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/tvStatus"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvStatus"
            tools:text="250,000 تومان" />

        <ImageView
            android:id="@+id/ivPaid"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_send"
            android:tint="@color/green"
            android:visibility="gone"
            android:contentDescription="@string/paid_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 